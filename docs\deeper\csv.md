# CSV

functionality for reading CSV files and data using the `ICSV` interface. It includes methods for reading from various sources such as files, URLs, and strings.

Base on [gocarina/gocsv: The GoCSV package aims to provide easy CSV serialization and deserialization to the golang programming language (github.com)](https://github.com/gocarina/gocsv)

#### ICSV[T any]

```go
type ICSV[T any] interface {
	ReadFromFile(data []byte, options *ICSVOptions) ([]T, error)
	ReadFromPath(path string, options *ICSVOptions) ([]T, error)
	ReadFromString(data string, options *ICSVOptions) ([]T, error)
	ReadFromURL(url string, options *ICSVOptions) ([]T, error)
	ReadFromFileMaps(data []byte, options *ICSVOptions) ([]map[string]interface{}, error)
}
```
`ICSV[T any]` is an interface that defines methods for reading CSV data of type `T`. It includes the following methods:
- `ReadFromFile`: reads CSV data from a byte slice (`[]byte`), returning a slice of `T` and an error.
- `ReadFromPath`: reads CSV data from a file path, returning a slice of `T` and an error.
- `ReadFromString`: reads CSV data from a string, returning a slice of `T` and an error.
- `ReadFromURL`: reads CSV data from a URL, returning a slice of `T` and an error.
- `ReadFromFileMaps`: reads CSV data from a byte slice (`[]byte`), returning a slice of `map[string]interface{}` and an error.


#### ICSVOptions

```go
type ICSVOptions struct {
	FirstRowIsHeader bool
	Separator        string
}
```

`ICSVOptions` represents the options for reading CSV data. It includes two fields:
- `FirstRowIsHeader`: a boolean value indicating whether the first row of the CSV data should be considered as headers.
- `Separator`: a string representing the field separator in the CSV data.


### Functions

#### NewCSV[T any](ctx IContext) ICSV[T]

```go
func NewCSV[T any](ctx IContext) ICSV[T] {
	return &csv[T]{
		ctx: ctx,
	}
}
```

`NewCSV` is a factory function that creates a new instance of the `csv[T]` struct and returns it as an `ICSV[T]` interface. It takes an `IContext` parameter and sets it as the context of the new `csv[T]` instance.

## Example
Certainly! Here's an example usage of the `core` package to read CSV data from a file:

```go
package main

import (
	"fmt"
	"io/ioutil"
	"log"

    core "gitlab.finema.co/finema/idin-core"
)

type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
  Col1 string `csv:"Col1"`
  Col2 string `csv:"Col2"`
  Col3 string `csv:"Col3"`
  Col4 string `csv:"Col4"`
  Col5 string `csv:"Col5"`
  Col6 string `csv:"Col6"`
}

func main() {
	// Create an instance of the CSV reader
	csvReader := core.NewCSV[ExampleCSV](<IContext>)

	// Read CSV data from a file
	filePath := "data.csv"
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		log.Fatal(err)
	}

	// Define the CSV options
	options := &core.ICSVOptions{
		FirstRowIsHeader: true,
		Separator:        ",",
	}

	// Read the CSV data from the file
	items, err := csvReader.ReadFromFile(data, options)
	if err != nil {
		log.Fatal(err)
	}

	// Print the items
	for _, item := range items {
		fmt.Println(item)
	}
}
```

In this example, we first create an instance of the CSV reader using `core.NewCSV(<IContext>)`. Then, we read the contents of a CSV file using `ioutil.ReadFile` and pass the file data to the `ReadFromFile` method of the CSV reader. We also specify the CSV options, such as `FirstRowIsHeader` and `Separator`.

Finally, we iterate over the items returned by the `ReadFromFile` method and print each item.

Feel free to modify the code according to your needs and add error handling as necessary.
