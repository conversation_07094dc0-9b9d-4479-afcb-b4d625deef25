# Task Scheduling (cronjob)

In this document, we will |explain how to create a cronjob service in Go. The code consists of multiple packages, including the `main` package, `cmd` package, and `user` package. We will go through each package and explain how they contribute to creating the cronjob service.

Base on [go-co-op/gocron: Easy and fluent Go cron scheduling. This is a fork from https://github.com/jasonlvhit/gocron](https://github.com/go-co-op/gocron)

## Package `main` (main.go)

The `main` package is the entry point of the application. It checks the value of the `Service` configuration from the environment and executes the corresponding command. In this case, if the `Service` is set to `consts.ServiceCronjob`, it calls the `CronjobRun()` function from the `cmd` package.

```go
package main

import (
  "log"

  "gitlab.finema.co/finema/dga/dga-uc/uc-backend/cmd"
  "gitlab.finema.co/finema/dga/dga-uc/uc-backend/consts"
  core "gitlab.finema.co/finema/idin-core"
)

func main() {
  // Check the service type from the configuration
  serviceType := core.NewEnv().Config().Service

  switch serviceType {
  case consts.ServiceCronjob:
    // Start the Cronjob service
    cmd.CronjobRun()
  default:
    // Service type not supported, log an error and exit
    log.Fatal("Service not supported")
  }
}

```

## Package `cmd` (cmd/cronjob.go)

The `cmd` package contains the `CronjobRun()` function, which sets up the necessary components for running cron jobs. It initializes the database connection, creates a cron job context, and starts the cron scheduler.

```go
package cmd

import (
  "fmt"
  "os"
  "time"

  "gitlab.finema.co/finema/dga/dga-uc/uc-backend/modules/dashboard"
  "gitlab.finema.co/finema/dga/dga-uc/uc-backend/modules/user"
  core "gitlab.finema.co/finema/idin-core"
)

// CronjobRun is the main function to start the Cronjob service
func CronjobRun() {
  env := core.NewEnv()

  // Create a new Cronjob context
  e := core.NewCronjobContext(&core.CronjobContextOptions{
    TimeLocation: time.UTC,
    ContextOptions: &core.ContextOptions{
      ENV: env,
    },
  })

  // Add user-related Cronjob tasks
  user.NewUserCronjob(e)

  // Add dashboard-related Cronjob tasks
  dashboard.NewDashboardCronjob(e)

  // Start the Cronjob service
  e.Start()
}

```

## Package `user` (modules/user/user.cronjob.go)

The `user` package is responsible for defining and scheduling cron jobs related to user management. It provides the `NewUserCronjob` function, which accepts a `core.ICronjobContext` interface and adds various jobs to it.

```go
package user

import (
	core "gitlab.finema.co/finema/idin-core"
)

func NewUserCronjob(e core.ICronjobContext) {
	user := &UserController{}

  // Add your Cronjob tasks here using e.AddJob() function

  // Example tasks:
	e.AddJob(e.Job().Every(2).Minutes(), user.MyTask1)
	e.AddJob(e.Job().Every(1).Day().At("18:00"), user.MyTask2)
}
```

The `NewUserCronjob` function creates an instance of the `UserController` and adds several jobs to the cron job context using the `AddJob` method. Each job is scheduled to run at a specific interval or time.


## Code flow

To create the cronjob service, follow these steps:

1. Set up the project structure and import the required packages.
2. Create a `main` package with the entry point function.
3. In the `main` function, check the value of the `Service` configuration.
4. If the `Service` is set to `consts.ServiceCronjob`, call the `CronjobRun()` function from the `cmd` package.
5. In the `cmd` package, initialize the necessary components such as the database connection and cron job context.
6. Create the desired cron jobs in separate packages (e.g., `user` and `dashboard`).
7. In the `user` package, define the `NewUserCronjob` function and add jobs to the cron job context using the `AddJob` method.
8. Customize the scheduling intervals and actions based on your requirements.
9. Build and run the application.

