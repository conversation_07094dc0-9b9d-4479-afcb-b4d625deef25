package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO LG Info Repository
var PMOLGInfo = repository.Make[models.PMOLGInfo]()

func PMOLGInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOLGInfo] {
	return func(c repository.IRepository[models.PMOLGInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("start_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOLGInfoByProjectID(projectID string) repository.Option[models.PMOLGInfo] {
	return func(c repository.IRepository[models.PMOLGInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOLGInfoWithRelations() repository.Option[models.PMOLGInfo] {
	return func(c repository.IRepository[models.PMOLGInfo]) {
		c.Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}

func PMOLGInfoWithVersions() repository.Option[models.PMOLGInfo] {
	return func(c repository.IRepository[models.PMOLGInfo]) {
		c.Preload("LGInfoVersions")
	}
}
