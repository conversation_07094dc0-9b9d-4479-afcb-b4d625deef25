# Getting Started
Testify is a popular testing toolkit for Golang that provides a number of useful assertions and mocks. This document will show you how to use testify to write unit tests for your Golang code.

Base on [stretchr/testify: A toolkit with common assertions and mocks that plays nicely with the standard library (github.com)](https://github.com/stretchr/testify)

## Getting Started

The first step is to install testify. You can do this by running the following command:

Code snippet

```bash
go get github.com/stretchr/testify

```

Once testify is installed, you can start writing tests.

## Writing Tests

To write a test with testify, you need to create a file whose name ends with `_test.go`. This file will contain your test functions.

A test function in Golang starts with the `Test` keyword and takes a `*testing.T` object as its only parameter. The `*testing.T` object provides a number of methods that you can use to assert the correctness of your code.

For example, the following test function asserts that the `Add` function correctly adds two numbers:

```go
func TestAdd(t *testing.T) {
  expected := 3
  actual := Add(1, 2)
  assert.Equal(t, expected, actual)
}

```

The `assert.Equal` function asserts that the two values are equal. If they are not equal, the test will fail.

## Using Assertions

Testify provides a number of assertions that you can use to test your code. Some of the most commonly used assertions include:

-   `assert.Equal`
-   `assert.NotEqual`
-   `assert.True`
-   `assert.False`
-   `assert.Nil`
-   `assert.NotNil`

Refer to the [Testify documentation](https://pkg.go.dev/github.com/stretchr/testify/assert) for a complete list of available assertions and utilities.

Using Mocks
-----------

Testify also provides a number of mocks that you can use to test your code. Mocks are objects that simulate the behavior of real objects. This can be useful for testing code that depends on external dependencies, such as databases or APIs.

To use a mock, you need to create a new instance of the mock object. You can then use the `On` method to specify the behavior of the mock. For example, the following code creates a mock database and specifies that the `Get` method should return the value `10`:

Code snippet

```go
db := mock.NewMockDatabase()
db.On("Get", "1").Return(10)

```

Once you have created a mock object, you can use it in your tests. For example, the following test function asserts that the `Get` method of the mock database returns the value `10`:

```go
func TestGet(t *testing.T) {
  db := mock.NewMockDatabase()
  db.On("Get", "1").Return(10)

  actual := db.Get("1")
  assert.Equal(t, 10, actual)
}

```

Running Tests
-------------

To run your tests, you can use the `go test` command. For example, to run the tests in the `my_package` package, you would run the following command:

```
go test ./my_package

```


Conclusion
----------
You've successfully set up Testify and written your first test using the framework. Testify's assertion library and utilities will enable you to write expressive, readable, and effective tests for your Go projects. Explore the official documentation and experiment with more advanced features to enhance your testing experience.

Happy testing!
