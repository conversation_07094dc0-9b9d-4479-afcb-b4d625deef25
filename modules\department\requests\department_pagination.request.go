package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type DepartmentPaginationRequest struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id" query:"ministry_id"`
}

func (r *DepartmentPaginationRequest) Validate(ctx core.IContext) core.IError {
	// Validate ministry_id exists if provided
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	return r.Error()
}
