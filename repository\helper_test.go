package repository

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type lupRequest struct {
	ID   int
	Name string
}

func TestMapPaginatedItems(t *testing.T) {
	testCases := []struct {
		name           string
		pagination     *Pagination[lupRequest]
		mapFunc        func(lupRequest) string
		expectedResult Pagination[string]
	}{
		{
			name: "map paginated items successfully",
			pagination: &Pagination[lupRequest]{
				Page:  1,
				Total: 10,
				Limit: 10,
				Count: 5,
				Items: []lupRequest{
					{ID: 1, Name: "Request 1"},
					{ID: 2, Name: "Request 2"},
					{ID: 3, Name: "Request 3"},
					{ID: 4, Name: "Request 4"},
					{ID: 5, Name: "Request 5"},
				},
			},
			mapFunc: func(r lupRequest) string {
				return r.Name
			},
			expectedResult: Pagination[string]{
				Page:  1,
				Total: 10,
				Limit: 10,
				Count: 5,
				Items: []string{
					"Request 1",
					"Request 2",
					"Request 3",
					"Request 4",
					"Request 5",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := MapPaginatedItems(tc.pagination, tc.mapFunc)
			assert.Equal(t, tc.expectedResult, *result)
		})
	}
}
