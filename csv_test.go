//go:build integration
// +build integration

package core

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.finema.co/finema/idin-core/utils"
)

func TestReadFromFile(t *testing.T) {
	//add file before test
	clientsFilew, err := os.ReadFile("addresses.csv")
	if err != nil {
		panic(err)
	}

	type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
		Col1 string `csv:"John"`
		Col2 string `csv:"Doe"`
	}

	ctx := NewContext(&ContextOptions{})
	c := NewCSV[ExampleCSV](ctx)
	data, err := c.ReadFromFile(clientsFilew, &ICSVOptions{FirstRowIsHeader: true})
	assert.NoError(t, err)
	utils.LogStruct(data)

}

func TestReadFromPath(t *testing.T) {
	type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
		Col1 string `csv:"Col1"`
		Col2 string `csv:"Col2"`
		Col3 string `csv:"Col3"`
		Col4 string `csv:"Col4"`
		Col5 string `csv:"Col5"`
		Col6 string `csv:"Col6"`
	}

	t.Run("WithHeader", func(t *testing.T) {
		ctx := NewContext(&ContextOptions{})
		c := NewCSV[ExampleCSV](ctx)
		data, err := c.ReadFromPath("test.csv", nil)
		assert.NoError(t, err)
		utils.LogStruct(data)
	})

	t.Run("WithoutHeader", func(t *testing.T) {
		ctx := NewContext(&ContextOptions{})
		c := NewCSV[ExampleCSV](ctx)
		data, err := c.ReadFromPath("test.csv", nil)
		assert.NoError(t, err)
		utils.LogStruct(data)
	})

}

func TestReadFromURL(t *testing.T) {
	type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
		Col1 string `csv:"Col1"`
		Col2 string `csv:"Col2"`
		Col3 string `csv:"Col3"`
		Col4 string `csv:"Col4"`
		Col5 string `csv:"Col5"`
		Col6 string `csv:"Col6"`
	}

	url := "https://people.sc.fsu.edu/~jburkardt/data/csv/addresses.csv"

	t.Run("WithHeader", func(t *testing.T) {
		ctx := NewContext(&ContextOptions{})
		c := NewCSV[ExampleCSV](ctx)
		data, err := c.ReadFromURL(url, nil)
		assert.NoError(t, err)
		utils.LogStruct(data)
	})

	t.Run("WithoutHeader", func(t *testing.T) {
		ctx := NewContext(&ContextOptions{})
		c := NewCSV[ExampleCSV](ctx)
		data, err := c.ReadFromURL(url, &ICSVOptions{FirstRowIsHeader: true})
		assert.NoError(t, err)
		utils.LogStruct(data)
	})

}

func TestReadFromFileMaps(t *testing.T) {
	type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
		Col1 string `csv:"Col1"`
		Col2 string `csv:"Col2"`
		Col3 string `csv:"Col3"`
		Col4 string `csv:"Col4"`
		Col5 string `csv:"Col5"`
		Col6 string `csv:"Col6"`
	}

	clientsFilew, err := os.ReadFile("addresses.csv")
	if err != nil {
		panic(err)
	}

	t.Run("WithHeader", func(t *testing.T) {
		ctx := NewContext(&ContextOptions{})
		c := NewCSV[interface{}](ctx)
		data, err := c.ReadFromFileMaps(clientsFilew, nil)
		assert.NoError(t, err)
		utils.LogStruct(data)
	})

}

func TestReadFromString(t *testing.T) {
	type ExampleCSV struct { // Our example struct, you can use "-" to ignore a field
		Col1 string `csv:"Col1"`
		Col2 string `csv:"Col2"`
		Col3 string `csv:"Col3"`
		Col4 string `csv:"Col4"`
		Col5 string `csv:"Col5"`
	}

	const csvContents = `field_a,field_b
							a,b
							c,d
						`
	ctx := NewContext(&ContextOptions{})
	c := NewCSV[ExampleCSV](ctx)
	data, err := c.ReadFromString(csvContents, nil)
	assert.NoError(t, err)
	utils.LogStruct(data)

}
