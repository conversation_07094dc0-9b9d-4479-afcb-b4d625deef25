# Kubernetes

Here's a complete documentation guide for deploying a GoLang application on Kubernetes

## Kubernetes Deployment Guide for GoLang Application

This guide provides step-by-step instructions on how to deploy a GoLang application on Kubernetes using the provided Kubernetes deployment configuration.

### Prerequisites
Before you begin, make sure you have the following prerequisites:
- A Kubernetes cluster: Set up a Kubernetes cluster where you will deploy your application.
- Kubectl: Install and configure `kubectl` command-line tool to interact with your Kubernetes cluster.

### Step 1: Create the Deployment Configuration
1. Create a file named `deployment.yaml`.
2. Copy the following content into the `deployment.yaml` file:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: finesign-dev
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  selector:
    matchLabels:
      name: api
  template:
    metadata:
      labels:
        name: api
    spec:
      containers:
        - name: api
          # Specify the Docker image for the container
          image: 988056783023.dkr.ecr.ap-southeast-1.amazonaws.com/finesign-dev:latest-api
          # Set the image pull policy to Always
          imagePullPolicy: Always
          livenessProbe:
            tcpSocket:
              # Set the port for the liveness probe
              port: 3000
            # Set the initial delay before starting the liveness probe
            initialDelaySeconds: 30
            # Set the timeout for the liveness probe
            timeoutSeconds: 1
            # Set the period between liveness probe checks
            periodSeconds: 300
          readinessProbe:
            tcpSocket:
              # Set the port for the readiness probe
              port: 3000
            # Set the initial delay before starting the readiness probe
            initialDelaySeconds: 30
            # Set the timeout for the readiness probe
            timeoutSeconds: 1
            # Set the period between readiness probe checks
            periodSeconds: 30
            # Set the failure threshold for the readiness probe
            failureThreshold: 5
          ports:
            - name: api
              # Set the container port
              containerPort: 3000
          envFrom:
            - configMapRef:
                # Specify the name of the ConfigMap to be used for environment variables
                name: api-config

```

::: tip
-   The `type: RollingUpdate` indicates that the deployment strategy is a rolling update strategy, which updates the deployment gradually by creating new pods and terminating old pods.
-   `maxUnavailable: 0` specifies that during the rolling update, there should be no unavailable pods (zero downtime), meaning all the pods are available throughout the update process.
-   `maxSurge: 2` specifies the maximum number of pods that can be created over the desired replica count during the rolling update. In this case, it allows a maximum of 2 additional pods to be created temporarily while updating the deployment.

These settings ensure a smooth and controlled update process for the deployment, gradually replacing the old pods with the new ones without causing any downtime.
:::

::: tip
`livenessProbe` and `readinessProbe` are Kubernetes configuration options used to monitor the health of containers running within a pod. They help ensure that the containers are running correctly and ready to handle requests. Here's an explanation of each:

Both probes allow Kubernetes to monitor the health and availability of containers within a pod. The liveness probe ensures containers are restarted if they become unresponsive or encounter issues, while the readiness probe ensures that only fully operational containers receive incoming traffic.
:::

### Step 2: Create the ConfigMap
1. Create a file named `configmap.yaml`.
2. Copy the following content into the `configmap.yaml` file:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-config
  namespace: finesign-dev
data:
  APP_SERVICE: api
  APP_LOG_LEVEL: debug
  APP_HOST: ":3000"
  APP_DB_DRIVER: mysql
  APP_DB_USER: my_user
  APP_DB_PASSWORD: my_password
  APP_DB_HOST: db
  APP_DB_PORT: "3306"
  APP_DB_NAME: finesign
  APP_SENTRY_DSN: "https://xxx.ingest.sentry.io/xxx"
```
::: tip
In Kubernetes, a ConfigMap is a key-value pair object that is used to store configuration data. 
It provides a way to separate configuration from application code, allowing you to manage 
configuration settings independently and make them easily accessible to your containers.
:::

### Step 3: Create the Service
1. Create a file named `service.yaml`.
2. Copy the following content into the `service.yaml` file:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: api
  namespace: finesign-dev
spec:
  ports:
    - port: 80
      name: "api80"
      targetPort: 3000
      protocol: TCP
  selector:
    name: api
```
::: tip
In Kubernetes, a Service is an abstract mechanism that provides network connectivity to a set of 
pods. Services enable communication between different components within a cluster, allowing pods to
discover and communicate with each other without requiring knowledge of their specific IP addresses 
or port numbers
:::

### Step 4: Create the Ingress
1. Create a file named `ingress.yaml`.
2. Copy the following content into the `ingress.yaml` file:

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api
  namespace: finesign-dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,X-LANG,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,X-Api-Key,X-Device-Id,Access-Control-Allow-Origin,X-Real-Ip,X-Forwarded-For,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  rules:
    - host: finesign-api.finema.dev
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api
                port:
                  number: 80
```
::: tip
In Kubernetes, a Service is an abstract mechanism that provides network connectivity to a set of 
pods. Services enable communication between different components within a cluster, allowing pods 
to discover and communicate with each other without requiring knowledge of their specific IP 
addresses or port numbers
:::

### Step 5: Deploy the Application
1. Apply the deployment, config map, service, and ingress by running the following command:

   ```shell
   kubectl apply -f deployment.yaml -f configmap.yaml -f service.yaml -f ingress.yaml
   ```

2. Verify the deployment by running the following command:

   ```shell
   kubectl get deployments -n finesign-dev
   ```

   You should see the `api` deployment with the desired number of replicas.

3. Verify the service by running the following command:

   ```shell
   kubectl get services -n finesign-dev
   ```

   You should see the `api` service with the corresponding port mappings.

4. Verify the ingress by running the following command:

   ```shell
   kubectl get ingress -n finesign-dev
   ```

   You should see the `api` ingress with the configured rules.

### Step 6: Access the Deployed Application
After the deployment is completed and the service and ingress are successfully configured, you can access your GoLang application using the appropriate URL associated with your ingress configuration (`finesign-api.finema.dev` in this case).

That's it! You have successfully deployed your GoLang application on Kubernetes using the provided configuration. You can customize the deployment, config map,

service, and ingress files according to your specific requirements.
