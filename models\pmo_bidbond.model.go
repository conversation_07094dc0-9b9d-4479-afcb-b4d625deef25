package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOBidbondInfo represents project bidbond information
type PMOBidbondInfo struct {
	BaseModel
	ProjectID      string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	GuaranteeAsset string     `json:"guarantee_asset" gorm:"column:guarantee_asset"`
	BidbondPayer   string     `json:"bidbond_payer" gorm:"column:bidbond_payer"`
	BidbondValue   float64    `json:"bidbond_value" gorm:"column:bidbond_value"`
	StartDate      *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate        *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationMonth  int64      `json:"duration_month" gorm:"column:duration_month"`
	DurationYear   int64      `json:"duration_year" gorm:"column:duration_year"`
	Fee            float64    `json:"fee" gorm:"column:fee"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBidbondInfo) TableName() string {
	return "pmo_bidbond_info"
}

// PMOBidbondInfoVersion represents versioned bidbond information
type PMOBidbondInfoVersion struct {
	PMOBidbondInfo
	OriginalID string `json:"bidbond_info_id" gorm:"column:bidbond_info_id;type:uuid;index"`
}

func (PMOBidbondInfoVersion) TableName() string {
	return "pmo_bidbond_info_versions"
}

func (u *PMOBidbondInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
