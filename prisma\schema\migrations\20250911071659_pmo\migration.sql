-- Create<PERSON>num
CREATE TYPE "public"."PMOTabPermission" AS ENUM ('READONLY', 'MODIFY');

-- CreateEnum
CREATE TYPE "public"."PMOCommentChannel" AS ENUM ('OVERALL', 'CONFIDENTIAL', 'SALES', 'PRESALES', 'BIDDING', 'PMO_PROGRESS', 'PMO_HWSW', 'PMO_WARRANTY', 'PMO_LEGAL', 'BIZ<PERSON>');

-- CreateEnum
CREATE TYPE "public"."PMODocType" AS ENUM ('INBOUND', 'OUTBOUND');

-- CreateTable
CREATE TABLE "public"."pmo_collaborators" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "tab_permission" "public"."PMOTabPermission" NOT NULL,
    "tab_main_response" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_collaborators_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_comments" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "channel" "public"."PMOCommentChannel" NOT NULL,
    "detail" TEXT NOT NULL,
    "is_client_flag" BOOLEAN NOT NULL DEFAULT false,
    "parent_comment_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_comment_versions" (
    "id" UUID NOT NULL,
    "comment_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "channel" "public"."PMOCommentChannel" NOT NULL,
    "detail" TEXT NOT NULL,
    "is_client_flag" BOOLEAN NOT NULL DEFAULT false,
    "parent_comment_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_comment_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_checklist_items" (
    "id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "detail" TEXT NOT NULL,
    "is_checked" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_checklist_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_remarks" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "detail" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_remarks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_remark_versions" (
    "id" UUID NOT NULL,
    "remark_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "detail" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_remark_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_document_groups" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "group_name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_document_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_document_items" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "group_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "sharepoint_url" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "type" "public"."PMODocType" NOT NULL,
    "file_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_document_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_document_item_versions" (
    "id" UUID NOT NULL,
    "document_item_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "group_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "sharepoint_url" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "type" "public"."PMODocType" NOT NULL,
    "file_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_document_item_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_contacts" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "fullname" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "detail" TEXT NOT NULL,
    "company" TEXT NOT NULL,
    "position" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_contacts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_competitors" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "company" TEXT NOT NULL,
    "detail" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_competitors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_partners" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "company" TEXT NOT NULL,
    "detail" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_partners_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_budget_info" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "fund_type" TEXT NOT NULL,
    "project_value" DOUBLE PRECISION NOT NULL,
    "bidbond_value" DOUBLE PRECISION NOT NULL,
    "partner" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_budget_info_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_budget_info_versions" (
    "id" UUID NOT NULL,
    "budget_info_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "fund_type" TEXT NOT NULL,
    "project_value" DOUBLE PRECISION NOT NULL,
    "bidbond_value" DOUBLE PRECISION NOT NULL,
    "partner" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_budget_info_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_bidding_info" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "bidding_type" TEXT NOT NULL,
    "bidding_value" DOUBLE PRECISION NOT NULL,
    "tender_date" DATE NOT NULL,
    "tender_entity" TEXT NOT NULL,
    "announce_date" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_bidding_info_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_bidding_info_versions" (
    "id" UUID NOT NULL,
    "bidding_info_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "bidding_type" TEXT NOT NULL,
    "bidding_value" DOUBLE PRECISION NOT NULL,
    "tender_date" DATE NOT NULL,
    "tender_entity" TEXT NOT NULL,
    "announce_date" DATE NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_bidding_info_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_contract_info" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "contract_no" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "signing_date" DATE NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "duration_day" INTEGER NOT NULL,
    "warranty_duration_day" INTEGER NOT NULL,
    "warranty_duration_year" INTEGER NOT NULL,
    "prime" TEXT NOT NULL,
    "penalty_fee" DOUBLE PRECISION NOT NULL,
    "is_legalize_stamp" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_contract_info_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_contract_info_versions" (
    "id" UUID NOT NULL,
    "contract_info_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "contract_no" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "signing_date" DATE NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "duration_day" INTEGER NOT NULL,
    "warranty_duration_day" INTEGER NOT NULL,
    "warranty_duration_year" INTEGER NOT NULL,
    "prime" TEXT NOT NULL,
    "penalty_fee" DOUBLE PRECISION NOT NULL,
    "is_legalize_stamp" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_contract_info_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_bidbond_info" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "guarantee_asset" TEXT NOT NULL,
    "bidbond_payer" TEXT NOT NULL,
    "bidbond_value" DOUBLE PRECISION NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "duration_month" INTEGER NOT NULL,
    "duration_year" INTEGER NOT NULL,
    "fee" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_bidbond_info_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_bidbond_info_versions" (
    "id" UUID NOT NULL,
    "bidbond_info_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "guarantee_asset" TEXT NOT NULL,
    "bidbond_payer" TEXT NOT NULL,
    "bidbond_value" DOUBLE PRECISION NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "duration_month" INTEGER NOT NULL,
    "duration_year" INTEGER NOT NULL,
    "fee" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_bidbond_info_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_lg_info" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "fee" DOUBLE PRECISION NOT NULL,
    "interest" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_lg_info_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_lg_info_versions" (
    "id" UUID NOT NULL,
    "lg_info_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "fee" DOUBLE PRECISION NOT NULL,
    "interest" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_lg_info_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_vendor_items" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "vendor_name" TEXT NOT NULL,
    "item_name" TEXT NOT NULL,
    "item_detail" TEXT NOT NULL,
    "deliver_duration_day" INTEGER NOT NULL,
    "is_tor" BOOLEAN NOT NULL,
    "is_implementation" BOOLEAN NOT NULL,
    "is_training" BOOLEAN NOT NULL,
    "is_user_manual" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_vendor_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_vendor_items_versions" (
    "id" UUID NOT NULL,
    "vendor_item_id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "vendor_name" TEXT NOT NULL,
    "item_name" TEXT NOT NULL,
    "item_detail" TEXT NOT NULL,
    "deliver_duration_day" INTEGER NOT NULL,
    "is_tor" BOOLEAN NOT NULL,
    "is_implementation" BOOLEAN NOT NULL,
    "is_training" BOOLEAN NOT NULL,
    "is_user_manual" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_vendor_items_versions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pmo_collaborators_project_id_user_id_tab_key_idx" ON "public"."pmo_collaborators"("project_id", "user_id", "tab_key");

-- CreateIndex
CREATE INDEX "pmo_comments_project_id_user_id_channel_idx" ON "public"."pmo_comments"("project_id", "user_id", "channel");

-- CreateIndex
CREATE INDEX "pmo_comment_versions_project_id_user_id_channel_idx" ON "public"."pmo_comment_versions"("project_id", "user_id", "channel");

-- CreateIndex
CREATE INDEX "pmo_checklist_items_tab_key_idx" ON "public"."pmo_checklist_items"("tab_key");

-- CreateIndex
CREATE INDEX "pmo_remarks_project_id_tab_key_idx" ON "public"."pmo_remarks"("project_id", "tab_key");

-- CreateIndex
CREATE INDEX "pmo_remark_versions_project_id_tab_key_idx" ON "public"."pmo_remark_versions"("project_id", "tab_key");

-- CreateIndex
CREATE INDEX "pmo_document_groups_project_id_tab_key_idx" ON "public"."pmo_document_groups"("project_id", "tab_key");

-- CreateIndex
CREATE INDEX "pmo_document_items_project_id_tab_key_group_id_idx" ON "public"."pmo_document_items"("project_id", "tab_key", "group_id");

-- CreateIndex
CREATE INDEX "pmo_document_item_versions_project_id_tab_key_group_id_idx" ON "public"."pmo_document_item_versions"("project_id", "tab_key", "group_id");

-- CreateIndex
CREATE INDEX "pmo_contacts_project_id_idx" ON "public"."pmo_contacts"("project_id");

-- CreateIndex
CREATE INDEX "pmo_competitors_project_id_idx" ON "public"."pmo_competitors"("project_id");

-- CreateIndex
CREATE INDEX "pmo_partners_project_id_idx" ON "public"."pmo_partners"("project_id");

-- CreateIndex
CREATE INDEX "pmo_budget_info_project_id_idx" ON "public"."pmo_budget_info"("project_id");

-- CreateIndex
CREATE INDEX "pmo_budget_info_versions_project_id_idx" ON "public"."pmo_budget_info_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_budget_info_versions_budget_info_id_idx" ON "public"."pmo_budget_info_versions"("budget_info_id");

-- CreateIndex
CREATE INDEX "pmo_bidding_info_project_id_idx" ON "public"."pmo_bidding_info"("project_id");

-- CreateIndex
CREATE INDEX "pmo_bidding_info_versions_project_id_idx" ON "public"."pmo_bidding_info_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_bidding_info_versions_bidding_info_id_idx" ON "public"."pmo_bidding_info_versions"("bidding_info_id");

-- CreateIndex
CREATE INDEX "pmo_contract_info_project_id_idx" ON "public"."pmo_contract_info"("project_id");

-- CreateIndex
CREATE INDEX "pmo_contract_info_versions_project_id_idx" ON "public"."pmo_contract_info_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_contract_info_versions_contract_info_id_idx" ON "public"."pmo_contract_info_versions"("contract_info_id");

-- CreateIndex
CREATE INDEX "pmo_bidbond_info_project_id_idx" ON "public"."pmo_bidbond_info"("project_id");

-- CreateIndex
CREATE INDEX "pmo_bidbond_info_versions_project_id_idx" ON "public"."pmo_bidbond_info_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_bidbond_info_versions_bidbond_info_id_idx" ON "public"."pmo_bidbond_info_versions"("bidbond_info_id");

-- CreateIndex
CREATE INDEX "pmo_lg_info_project_id_idx" ON "public"."pmo_lg_info"("project_id");

-- CreateIndex
CREATE INDEX "pmo_lg_info_versions_project_id_idx" ON "public"."pmo_lg_info_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_lg_info_versions_lg_info_id_idx" ON "public"."pmo_lg_info_versions"("lg_info_id");

-- CreateIndex
CREATE INDEX "pmo_vendor_items_project_id_idx" ON "public"."pmo_vendor_items"("project_id");

-- CreateIndex
CREATE INDEX "pmo_vendor_items_versions_project_id_idx" ON "public"."pmo_vendor_items_versions"("project_id");

-- CreateIndex
CREATE INDEX "pmo_vendor_items_versions_vendor_item_id_idx" ON "public"."pmo_vendor_items_versions"("vendor_item_id");

-- AddForeignKey
ALTER TABLE "public"."pmo_collaborators" ADD CONSTRAINT "pmo_collaborators_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_collaborators" ADD CONSTRAINT "pmo_collaborators_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_comments" ADD CONSTRAINT "pmo_comments_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_comments" ADD CONSTRAINT "pmo_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_comment_versions" ADD CONSTRAINT "pmo_comment_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_comment_versions" ADD CONSTRAINT "pmo_comment_versions_comment_id_fkey" FOREIGN KEY ("comment_id") REFERENCES "public"."pmo_comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_comment_versions" ADD CONSTRAINT "pmo_comment_versions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_remarks" ADD CONSTRAINT "pmo_remarks_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_remark_versions" ADD CONSTRAINT "pmo_remark_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_remark_versions" ADD CONSTRAINT "pmo_remark_versions_remark_id_fkey" FOREIGN KEY ("remark_id") REFERENCES "public"."pmo_remarks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_groups" ADD CONSTRAINT "pmo_document_groups_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_items" ADD CONSTRAINT "pmo_document_items_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_items" ADD CONSTRAINT "pmo_document_items_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."pmo_document_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_items" ADD CONSTRAINT "pmo_document_items_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_item_versions" ADD CONSTRAINT "pmo_document_item_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_item_versions" ADD CONSTRAINT "pmo_document_item_versions_document_item_id_fkey" FOREIGN KEY ("document_item_id") REFERENCES "public"."pmo_document_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_document_item_versions" ADD CONSTRAINT "pmo_document_item_versions_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_contacts" ADD CONSTRAINT "pmo_contacts_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_competitors" ADD CONSTRAINT "pmo_competitors_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_partners" ADD CONSTRAINT "pmo_partners_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_budget_info" ADD CONSTRAINT "pmo_budget_info_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_budget_info_versions" ADD CONSTRAINT "pmo_budget_info_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidding_info" ADD CONSTRAINT "pmo_bidding_info_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidding_info_versions" ADD CONSTRAINT "pmo_bidding_info_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidding_info_versions" ADD CONSTRAINT "pmo_bidding_info_versions_bidding_info_id_fkey" FOREIGN KEY ("bidding_info_id") REFERENCES "public"."pmo_bidding_info"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_contract_info" ADD CONSTRAINT "pmo_contract_info_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_contract_info_versions" ADD CONSTRAINT "pmo_contract_info_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_contract_info_versions" ADD CONSTRAINT "pmo_contract_info_versions_contract_info_id_fkey" FOREIGN KEY ("contract_info_id") REFERENCES "public"."pmo_contract_info"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidbond_info" ADD CONSTRAINT "pmo_bidbond_info_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidbond_info_versions" ADD CONSTRAINT "pmo_bidbond_info_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_bidbond_info_versions" ADD CONSTRAINT "pmo_bidbond_info_versions_bidbond_info_id_fkey" FOREIGN KEY ("bidbond_info_id") REFERENCES "public"."pmo_bidbond_info"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_lg_info" ADD CONSTRAINT "pmo_lg_info_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_lg_info_versions" ADD CONSTRAINT "pmo_lg_info_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_lg_info_versions" ADD CONSTRAINT "pmo_lg_info_versions_lg_info_id_fkey" FOREIGN KEY ("lg_info_id") REFERENCES "public"."pmo_lg_info"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_vendor_items" ADD CONSTRAINT "pmo_vendor_items_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_vendor_items_versions" ADD CONSTRAINT "pmo_vendor_items_versions_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_vendor_items_versions" ADD CONSTRAINT "pmo_vendor_items_versions_vendor_item_id_fkey" FOREIGN KEY ("vendor_item_id") REFERENCES "public"."pmo_vendor_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;
