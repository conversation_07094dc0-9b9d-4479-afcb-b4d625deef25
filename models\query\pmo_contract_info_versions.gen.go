// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOContractInfoVersion(db *gorm.DB, opts ...gen.DOOption) pMOContractInfoVersion {
	_pMOContractInfoVersion := pMOContractInfoVersion{}

	_pMOContractInfoVersion.pMOContractInfoVersionDo.UseDB(db, opts...)
	_pMOContractInfoVersion.pMOContractInfoVersionDo.UseModel(&models.PMOContractInfoVersion{})

	tableName := _pMOContractInfoVersion.pMOContractInfoVersionDo.TableName()
	_pMOContractInfoVersion.ALL = field.NewAsterisk(tableName)
	_pMOContractInfoVersion.ID = field.NewString(tableName, "id")
	_pMOContractInfoVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOContractInfoVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOContractInfoVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOContractInfoVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOContractInfoVersion.ContractNo = field.NewString(tableName, "contract_no")
	_pMOContractInfoVersion.Value = field.NewFloat64(tableName, "value")
	_pMOContractInfoVersion.SigningDate = field.NewTime(tableName, "signing_date")
	_pMOContractInfoVersion.StartDate = field.NewTime(tableName, "start_date")
	_pMOContractInfoVersion.EndDate = field.NewTime(tableName, "end_date")
	_pMOContractInfoVersion.DurationDay = field.NewInt64(tableName, "duration_day")
	_pMOContractInfoVersion.WarrantyDurationDay = field.NewInt64(tableName, "warranty_duration_day")
	_pMOContractInfoVersion.WarrantyDurationYear = field.NewInt64(tableName, "warranty_duration_year")
	_pMOContractInfoVersion.Prime = field.NewString(tableName, "prime")
	_pMOContractInfoVersion.PenaltyFee = field.NewFloat64(tableName, "penalty_fee")
	_pMOContractInfoVersion.IsLegalizeStamp = field.NewBool(tableName, "is_legalize_stamp")
	_pMOContractInfoVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOContractInfoVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOContractInfoVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOContractInfoVersion.OriginalID = field.NewString(tableName, "contract_info_id")
	_pMOContractInfoVersion.Project = pMOContractInfoVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOContractInfoVersion.CreatedBy = pMOContractInfoVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOContractInfoVersion.UpdatedBy = pMOContractInfoVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOContractInfoVersion.fillFieldMap()

	return _pMOContractInfoVersion
}

type pMOContractInfoVersion struct {
	pMOContractInfoVersionDo

	ALL                  field.Asterisk
	ID                   field.String
	CreatedAt            field.Time
	UpdatedAt            field.Time
	DeletedAt            field.Field
	ProjectID            field.String
	ContractNo           field.String
	Value                field.Float64
	SigningDate          field.Time
	StartDate            field.Time
	EndDate              field.Time
	DurationDay          field.Int64
	WarrantyDurationDay  field.Int64
	WarrantyDurationYear field.Int64
	Prime                field.String
	PenaltyFee           field.Float64
	IsLegalizeStamp      field.Bool
	CreatedByID          field.String
	UpdatedByID          field.String
	DeletedByID          field.String
	OriginalID           field.String
	Project              pMOContractInfoVersionHasOneProject

	CreatedBy pMOContractInfoVersionBelongsToCreatedBy

	UpdatedBy pMOContractInfoVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOContractInfoVersion) Table(newTableName string) *pMOContractInfoVersion {
	p.pMOContractInfoVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOContractInfoVersion) As(alias string) *pMOContractInfoVersion {
	p.pMOContractInfoVersionDo.DO = *(p.pMOContractInfoVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOContractInfoVersion) updateTableName(table string) *pMOContractInfoVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.ContractNo = field.NewString(table, "contract_no")
	p.Value = field.NewFloat64(table, "value")
	p.SigningDate = field.NewTime(table, "signing_date")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.DurationDay = field.NewInt64(table, "duration_day")
	p.WarrantyDurationDay = field.NewInt64(table, "warranty_duration_day")
	p.WarrantyDurationYear = field.NewInt64(table, "warranty_duration_year")
	p.Prime = field.NewString(table, "prime")
	p.PenaltyFee = field.NewFloat64(table, "penalty_fee")
	p.IsLegalizeStamp = field.NewBool(table, "is_legalize_stamp")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "contract_info_id")

	p.fillFieldMap()

	return p
}

func (p *pMOContractInfoVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOContractInfoVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 23)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["contract_no"] = p.ContractNo
	p.fieldMap["value"] = p.Value
	p.fieldMap["signing_date"] = p.SigningDate
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["duration_day"] = p.DurationDay
	p.fieldMap["warranty_duration_day"] = p.WarrantyDurationDay
	p.fieldMap["warranty_duration_year"] = p.WarrantyDurationYear
	p.fieldMap["prime"] = p.Prime
	p.fieldMap["penalty_fee"] = p.PenaltyFee
	p.fieldMap["is_legalize_stamp"] = p.IsLegalizeStamp
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["contract_info_id"] = p.OriginalID

}

func (p pMOContractInfoVersion) clone(db *gorm.DB) pMOContractInfoVersion {
	p.pMOContractInfoVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOContractInfoVersion) replaceDB(db *gorm.DB) pMOContractInfoVersion {
	p.pMOContractInfoVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOContractInfoVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOContractInfoVersionHasOneProject) Where(conds ...field.Expr) *pMOContractInfoVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoVersionHasOneProject) WithContext(ctx context.Context) *pMOContractInfoVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoVersionHasOneProject) Session(session *gorm.Session) *pMOContractInfoVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoVersionHasOneProject) Model(m *models.PMOContractInfoVersion) *pMOContractInfoVersionHasOneProjectTx {
	return &pMOContractInfoVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoVersionHasOneProject) Unscoped() *pMOContractInfoVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOContractInfoVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoVersionHasOneProjectTx) Unscoped() *pMOContractInfoVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOContractInfoVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMOContractInfoVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMOContractInfoVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMOContractInfoVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoVersionBelongsToCreatedBy) Model(m *models.PMOContractInfoVersion) *pMOContractInfoVersionBelongsToCreatedByTx {
	return &pMOContractInfoVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoVersionBelongsToCreatedBy) Unscoped() *pMOContractInfoVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOContractInfoVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoVersionBelongsToCreatedByTx) Unscoped() *pMOContractInfoVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOContractInfoVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOContractInfoVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOContractInfoVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMOContractInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoVersionBelongsToUpdatedBy) Model(m *models.PMOContractInfoVersion) *pMOContractInfoVersionBelongsToUpdatedByTx {
	return &pMOContractInfoVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoVersionBelongsToUpdatedBy) Unscoped() *pMOContractInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoVersionBelongsToUpdatedByTx) Unscoped() *pMOContractInfoVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoVersionDo struct{ gen.DO }

type IPMOContractInfoVersionDo interface {
	gen.SubQuery
	Debug() IPMOContractInfoVersionDo
	WithContext(ctx context.Context) IPMOContractInfoVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOContractInfoVersionDo
	WriteDB() IPMOContractInfoVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOContractInfoVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOContractInfoVersionDo
	Not(conds ...gen.Condition) IPMOContractInfoVersionDo
	Or(conds ...gen.Condition) IPMOContractInfoVersionDo
	Select(conds ...field.Expr) IPMOContractInfoVersionDo
	Where(conds ...gen.Condition) IPMOContractInfoVersionDo
	Order(conds ...field.Expr) IPMOContractInfoVersionDo
	Distinct(cols ...field.Expr) IPMOContractInfoVersionDo
	Omit(cols ...field.Expr) IPMOContractInfoVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo
	Group(cols ...field.Expr) IPMOContractInfoVersionDo
	Having(conds ...gen.Condition) IPMOContractInfoVersionDo
	Limit(limit int) IPMOContractInfoVersionDo
	Offset(offset int) IPMOContractInfoVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOContractInfoVersionDo
	Unscoped() IPMOContractInfoVersionDo
	Create(values ...*models.PMOContractInfoVersion) error
	CreateInBatches(values []*models.PMOContractInfoVersion, batchSize int) error
	Save(values ...*models.PMOContractInfoVersion) error
	First() (*models.PMOContractInfoVersion, error)
	Take() (*models.PMOContractInfoVersion, error)
	Last() (*models.PMOContractInfoVersion, error)
	Find() ([]*models.PMOContractInfoVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOContractInfoVersion, err error)
	FindInBatches(result *[]*models.PMOContractInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOContractInfoVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOContractInfoVersionDo
	Assign(attrs ...field.AssignExpr) IPMOContractInfoVersionDo
	Joins(fields ...field.RelationField) IPMOContractInfoVersionDo
	Preload(fields ...field.RelationField) IPMOContractInfoVersionDo
	FirstOrInit() (*models.PMOContractInfoVersion, error)
	FirstOrCreate() (*models.PMOContractInfoVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOContractInfoVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOContractInfoVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOContractInfoVersionDo) Debug() IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOContractInfoVersionDo) WithContext(ctx context.Context) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOContractInfoVersionDo) ReadDB() IPMOContractInfoVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOContractInfoVersionDo) WriteDB() IPMOContractInfoVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOContractInfoVersionDo) Session(config *gorm.Session) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOContractInfoVersionDo) Clauses(conds ...clause.Expression) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOContractInfoVersionDo) Returning(value interface{}, columns ...string) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOContractInfoVersionDo) Not(conds ...gen.Condition) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOContractInfoVersionDo) Or(conds ...gen.Condition) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOContractInfoVersionDo) Select(conds ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOContractInfoVersionDo) Where(conds ...gen.Condition) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOContractInfoVersionDo) Order(conds ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOContractInfoVersionDo) Distinct(cols ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOContractInfoVersionDo) Omit(cols ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOContractInfoVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOContractInfoVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOContractInfoVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOContractInfoVersionDo) Group(cols ...field.Expr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOContractInfoVersionDo) Having(conds ...gen.Condition) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOContractInfoVersionDo) Limit(limit int) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOContractInfoVersionDo) Offset(offset int) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOContractInfoVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOContractInfoVersionDo) Unscoped() IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOContractInfoVersionDo) Create(values ...*models.PMOContractInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOContractInfoVersionDo) CreateInBatches(values []*models.PMOContractInfoVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOContractInfoVersionDo) Save(values ...*models.PMOContractInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOContractInfoVersionDo) First() (*models.PMOContractInfoVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfoVersion), nil
	}
}

func (p pMOContractInfoVersionDo) Take() (*models.PMOContractInfoVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfoVersion), nil
	}
}

func (p pMOContractInfoVersionDo) Last() (*models.PMOContractInfoVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfoVersion), nil
	}
}

func (p pMOContractInfoVersionDo) Find() ([]*models.PMOContractInfoVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOContractInfoVersion), err
}

func (p pMOContractInfoVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOContractInfoVersion, err error) {
	buf := make([]*models.PMOContractInfoVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOContractInfoVersionDo) FindInBatches(result *[]*models.PMOContractInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOContractInfoVersionDo) Attrs(attrs ...field.AssignExpr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOContractInfoVersionDo) Assign(attrs ...field.AssignExpr) IPMOContractInfoVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOContractInfoVersionDo) Joins(fields ...field.RelationField) IPMOContractInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOContractInfoVersionDo) Preload(fields ...field.RelationField) IPMOContractInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOContractInfoVersionDo) FirstOrInit() (*models.PMOContractInfoVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfoVersion), nil
	}
}

func (p pMOContractInfoVersionDo) FirstOrCreate() (*models.PMOContractInfoVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfoVersion), nil
	}
}

func (p pMOContractInfoVersionDo) FindByPage(offset int, limit int) (result []*models.PMOContractInfoVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOContractInfoVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOContractInfoVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOContractInfoVersionDo) Delete(models ...*models.PMOContractInfoVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOContractInfoVersionDo) withDO(do gen.Dao) *pMOContractInfoVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
