package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOLGCreate struct {
	core.BaseValidator
	Value     *float64 `json:"value"`
	StartDate *string  `json:"start_date"`
	EndDate   *string  `json:"end_date"`
	Fee       *float64 `json:"fee"`
	Interest  *float64 `json:"interest"`
}

func (r *PMOLGCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsRequired(r.Value, "value"))
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))
	r.Must(r.IsRequired(r.Fee, "fee"))
	r.Must(r.IsRequired(r.Interest, "interest"))

	return r.Error()
}
