start:
	docker-compose up -d

start-build:
	docker-compose up -d --build

stop:
	docker-compose down

restart:
	make stop && make start

restart-build:
	make stop && make start-build

dev:
	chmod +x ./dev.sh
	./dev.sh

run:
	go run main.go
	
gorm-gen:
	APP_SERVICE=GORM_GEN go run main.go

install:
	export GOPRIVATE=gitlab.finema.co/finema/* && git config --global url."********************:".insteadOf "https://gitlab.finema.co/" && go get

logs:
	 docker logs -f api

migrate:
	docker-compose up -d --build migration

test:
	go test ./...
