package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOTemplateDocumentCreate struct {
	core.BaseValidator
	TabKey        *string `json:"tab_key"`
	Name          *string `json:"name"`
	SharepointURL *string `json:"sharepoint_url"`
}

func (r *PMOTemplateDocumentCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Tab<PERSON><PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.Name, "name"))
	r.Must(r.Is<PERSON>trRequired(r.SharepointURL, "sharepoint_url"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))

	
	r.Must(r.IsStrIn(r.<PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.<PERSON><PERSON>()
}
