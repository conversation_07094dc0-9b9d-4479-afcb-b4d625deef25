# เริ่มต้นการใช้งาน

เอกสารนี้ให้คำแนะนำเกี่ยวกับวิธีการเริ่มต้นใช้งาน API

## การติดตั้ง

ในการติดตั้ง IDIN Core Go เวอร์ชัน 1.19 หรือสูงกว่านั้นจำเป็นต้องใช้งาน โปรดตรวจสอบให้แน่ใจว่าโฟลเดอร์โปรเจคของคุณอยู่นอก ```$GOPATH```

```bash
# สร้างโฟลเดอร์ใหม่และเข้าไปในโฟลเดอร์นั้น
mkdir myapp && cd myapp

# เริ่มต้นโมดูล Go ใหม่ด้วยชื่อ "myapp"
go mod init myapp

# ตั้งค่าตัวแปรแวดล้อม GOPRIVATE เพื่อรวม URL ของเก็บรักษาข้อมูลส่วนตัว
export GOPRIVATE=gitlab.finema.co/finema/*

# กำหนดการกำหนดค่า Git เพื่อใช้ SSH แทน HTTPS สำหรับ URL ของเก็บรักษาข้อมูลส่วนตัว
git config --global url."********************:".insteadOf "https://gitlab.finema.co/"

# ติดตั้งแพ็คเกจ "idin-core" จากเก็บรักษาข้อมูลส่วนตัว
go get gitlab.finema.co/finema/idin-core
```

## สวัสดีชาวโลก!

#### สร้าง .env

```env
HOST=0.0.0.0:3000
ENV=dev
LOG_LEVEL=debug
```

#### สร้าง main.go

```go
package main

import (
  "github.com/labstack/echo/v4"
  core "gitlab.finema.co/finema/idin-core"
  "net/http"
)

func main() {
  // สร้างการกำหนดค่าสภาพแวดล้อมใหม่
  env := core.NewEnv()

  // สร้างเซิร์ฟเวอร์ HTTP ใหม่ของ Echo
  e := core.NewHTTPServer(&core.HTTPContextOptions{
    ContextOptions: &core.ContextOptions{
      ENV: env,
    },
  })

  // กำหนดเส้นทางสำหรับเส้นทางหลัก ("/")
  e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
    // ส่งข้อมูล JSON กลับไปพร้อมกับฟิลด์ "message"
    return c.JSON(http.StatusOK, core.Map{
      "message": "สวัสดีชาวโลก!",
    })
  }))

  // เริ่มเซิร์ฟเวอร์ HTTP
  core.StartHTTPServer(e, env)
}

```

#### เริ่มเซิร์ฟเวอร์

```bash
$ go run main.go
```

เรียกดูที่ [http://localhost:3000](http://localhost:3000) แล้วคุณควรเห็น "สวัสดีชาวโลก!" บนหน้าเว็บ
