package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectChecklistItemController struct {
}

// PMO Project Checklist Items methods
func (m PMOProjectChecklistItemController) ChecklistItemsPagination(c core.IHTTPContext) error {
	input := &requests.PMOProjectChecklistItemPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoChecklistItemSvc := services.NewPMOProjectChecklistItemService(c)
	res, ierr := pmoChecklistItemSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOProjectChecklistItemPaginationOptions{
		TabKey:    input.TabKey,
		IsChecked: input.IsChecked,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectChecklistItemController) ChecklistItemsFind(c core.IHTTPContext) error {
	pmoChecklistItemSvc := services.NewPMOProjectChecklistItemService(c)
	checklistItem, err := pmoChecklistItemSvc.Find(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, checklistItem)
}

func (m PMOProjectChecklistItemController) ChecklistItemsCreate(c core.IHTTPContext) error {
	input := &requests.PMOProjectChecklistItemCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoChecklistItemSvc := services.NewPMOProjectChecklistItemService(c)
	payload := &dto.PMOProjectChecklistItemCreatePayload{
		ProjectID: c.Param("id"),
		TabKey:    utils.ToNonPointer(input.TabKey),
		Detail:    utils.ToNonPointer(input.Detail),
		IsChecked: utils.ToNonPointer(input.IsChecked),
	}

	checklistItem, err := pmoChecklistItemSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, checklistItem)
}

func (m PMOProjectChecklistItemController) ChecklistItemsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOProjectChecklistItemUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoChecklistItemSvc := services.NewPMOProjectChecklistItemService(c)
	payload := &dto.PMOProjectChecklistItemUpdatePayload{
		Detail:    input.Detail,
		IsChecked: input.IsChecked,
	}

	checklistItem, err := pmoChecklistItemSvc.Update(c.Param("item_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, checklistItem)
}

func (m PMOProjectChecklistItemController) ChecklistItemsDelete(c core.IHTTPContext) error {
	pmoChecklistItemSvc := services.NewPMOProjectChecklistItemService(c)
	err := pmoChecklistItemSvc.Delete(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
