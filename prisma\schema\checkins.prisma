enum CheckinType {
  OFFICE_HQ
  OFFICE_AKV
  WFH
  ONSITE
  BUSINESS_TRIP
  LEAVE
}

enum CheckinPeriod {
  FULL_DAY
  HALF_MORNING
  HALF_AFTERNOON
  MANY_DAYS
}

enum CheckinLeaveType {
  AN<PERSON>AL
  SICK
  BUSINESS
  MENSTRUAL
  BIRTHDAY
  ORDINATION
}

model checkins {
  id               String            @id @default(uuid()) @db.Uuid
  user_id          String            @db.Uuid
  type             CheckinType
  leave_type       CheckinLeaveType?
  period           CheckinPeriod
  location         String
  remarks          String?
  is_unused        <PERSON>olean           @default(false)
  date             DateTime          @default(now())
  first_checkin_at DateTime          @default(now())
  created_at       DateTime          @default(now())
  updated_at       DateTime          @default(now()) @updatedAt

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([type, location])
}
