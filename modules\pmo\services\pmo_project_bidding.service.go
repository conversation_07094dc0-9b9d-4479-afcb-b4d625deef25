package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm/clause"
)

type IPMOProjectBiddingService interface {
	Create(input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError)
	Find(projectID string) (*models.PMOBiddingInfo, core.IError)
	BiddingVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBiddingInfoVersion], core.IError)
}

type pmoProjectBiddingService struct {
	ctx core.IContext
}

// PMO Bidding methods implementation
func (s pmoProjectBiddingService) Create(input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError) {
	// Check if bidding already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	bidding := &models.PMOBiddingInfo{
		BaseModel:    models.NewBaseModel(),
		ProjectID:    input.ProjectID,
		BiddingType:  input.BiddingType,
		BiddingValue: input.BiddingValue,
		TenderDate:   input.TenderDate,
		TenderEntity: input.TenderEntity,
		AnnounceDate: input.AnnounceDate,
		CreatedByID:  utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOBiddingInfo(s.ctx).Create(bidding)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOBiddingInfoVersion{
		PMOBiddingInfo: *bidding,
		OriginalID:     bidding.ID,
	}

	ierr = repositories.PMOBiddingInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(bidding.ProjectID)
}

func (s pmoProjectBiddingService) update(existing *models.PMOBiddingInfo, input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError) {
	mapUpdate := map[string]interface{}{
		"bidding_type":  input.BiddingType,
		"bidding_value": input.BiddingValue,
		"tender_entity": input.TenderEntity,
		"updated_by_id": s.ctx.GetUser().ID,
		"updated_at":    utils.GetCurrentDateTime(),
	}

	if input.TenderDate != nil {
		mapUpdate["tender_date"] = input.TenderDate
	}
	if input.AnnounceDate != nil {
		mapUpdate["announce_date"] = input.AnnounceDate
	}

	ierr := repositories.PMOBiddingInfo(s.ctx).Where("id = ?", existing.ID).Updates(mapUpdate)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOBiddingInfoVersion{
		PMOBiddingInfo: *existing,
		OriginalID:     existing.ID,
	}

	ierr = repositories.PMOBiddingInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectBiddingService) Find(projectID string) (*models.PMOBiddingInfo, core.IError) {
	return repositories.PMOBiddingInfo(s.ctx,
		repositories.PMOBiddingInfoWithRelations(),
	).FindOne("project_id = ?", projectID)
}

func (s pmoProjectBiddingService) BiddingVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBiddingInfoVersion], core.IError) {
	// First get the bidding info to get the original ID
	bidding, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOBiddingInfoVersion(s.ctx,
		repositories.PMOBiddingInfoVersionOrderBy(pageOptions),
		repositories.PMOBiddingInfoVersionWithRelations(),
		repositories.PMOBiddingInfoVersionByBiddingInfoID(bidding.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectBiddingService(ctx core.IContext) IPMOProjectBiddingService {
	return &pmoProjectBiddingService{ctx: ctx}
}
