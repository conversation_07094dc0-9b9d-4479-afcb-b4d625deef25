package models

type Project struct {
	BaseModel
	Name        string  `json:"name" gorm:"column:name"`
	Code        string  `json:"code" gorm:"column:code"`
	Description *string `json:"description" gorm:"column:description"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	PMO *PMOProject `json:"pmo,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (Project) TableName() string {
	return "projects"
}
