package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserPaginationRequest struct {
	core.BaseValidator
	TeamCode *string `json:"team_code" query:"team_code"`
	IsActive *bool   `json:"is_active" query:"is_active"`
}

func (r *UserPaginationRequest) Validate(ctx core.IContext) core.IError {
	// Validate team_code exists if provided
	if r.TeamCode != nil {
		r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	}

	return r.Error()
}
