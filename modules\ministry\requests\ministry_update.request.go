package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/ministry/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MinistryUpdate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""

	ministry, _ := repositories.Ministry(cc).FindOne("id = ?", cc.Param("id"))
	if ministry != nil {
		oldNameTh = ministry.NameTh
		if ministry.NameEn != nil {
			oldNameEn = utils.ToNonPointer(ministry.NameEn)
		}
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Ministry{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Ministry{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	return r.Error()
}
