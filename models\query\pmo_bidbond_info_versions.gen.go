// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBidbondInfoVersion(db *gorm.DB, opts ...gen.DOOption) pMOBidbondInfoVersion {
	_pMOBidbondInfoVersion := pMOBidbondInfoVersion{}

	_pMOBidbondInfoVersion.pMOBidbondInfoVersionDo.UseDB(db, opts...)
	_pMOBidbondInfoVersion.pMOBidbondInfoVersionDo.UseModel(&models.PMOBidbondInfoVersion{})

	tableName := _pMOBidbondInfoVersion.pMOBidbondInfoVersionDo.TableName()
	_pMOBidbondInfoVersion.ALL = field.NewAsterisk(tableName)
	_pMOBidbondInfoVersion.ID = field.NewString(tableName, "id")
	_pMOBidbondInfoVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBidbondInfoVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBidbondInfoVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBidbondInfoVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOBidbondInfoVersion.GuaranteeAsset = field.NewString(tableName, "guarantee_asset")
	_pMOBidbondInfoVersion.BidbondPayer = field.NewString(tableName, "bidbond_payer")
	_pMOBidbondInfoVersion.BidbondValue = field.NewFloat64(tableName, "bidbond_value")
	_pMOBidbondInfoVersion.StartDate = field.NewTime(tableName, "start_date")
	_pMOBidbondInfoVersion.EndDate = field.NewTime(tableName, "end_date")
	_pMOBidbondInfoVersion.DurationMonth = field.NewInt64(tableName, "duration_month")
	_pMOBidbondInfoVersion.DurationYear = field.NewInt64(tableName, "duration_year")
	_pMOBidbondInfoVersion.Fee = field.NewFloat64(tableName, "fee")
	_pMOBidbondInfoVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBidbondInfoVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBidbondInfoVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBidbondInfoVersion.OriginalID = field.NewString(tableName, "bidbond_info_id")
	_pMOBidbondInfoVersion.Project = pMOBidbondInfoVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBidbondInfoVersion.CreatedBy = pMOBidbondInfoVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBidbondInfoVersion.UpdatedBy = pMOBidbondInfoVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBidbondInfoVersion.fillFieldMap()

	return _pMOBidbondInfoVersion
}

type pMOBidbondInfoVersion struct {
	pMOBidbondInfoVersionDo

	ALL            field.Asterisk
	ID             field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	ProjectID      field.String
	GuaranteeAsset field.String
	BidbondPayer   field.String
	BidbondValue   field.Float64
	StartDate      field.Time
	EndDate        field.Time
	DurationMonth  field.Int64
	DurationYear   field.Int64
	Fee            field.Float64
	CreatedByID    field.String
	UpdatedByID    field.String
	DeletedByID    field.String
	OriginalID     field.String
	Project        pMOBidbondInfoVersionHasOneProject

	CreatedBy pMOBidbondInfoVersionBelongsToCreatedBy

	UpdatedBy pMOBidbondInfoVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBidbondInfoVersion) Table(newTableName string) *pMOBidbondInfoVersion {
	p.pMOBidbondInfoVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBidbondInfoVersion) As(alias string) *pMOBidbondInfoVersion {
	p.pMOBidbondInfoVersionDo.DO = *(p.pMOBidbondInfoVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBidbondInfoVersion) updateTableName(table string) *pMOBidbondInfoVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.GuaranteeAsset = field.NewString(table, "guarantee_asset")
	p.BidbondPayer = field.NewString(table, "bidbond_payer")
	p.BidbondValue = field.NewFloat64(table, "bidbond_value")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.DurationMonth = field.NewInt64(table, "duration_month")
	p.DurationYear = field.NewInt64(table, "duration_year")
	p.Fee = field.NewFloat64(table, "fee")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "bidbond_info_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBidbondInfoVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBidbondInfoVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 20)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["guarantee_asset"] = p.GuaranteeAsset
	p.fieldMap["bidbond_payer"] = p.BidbondPayer
	p.fieldMap["bidbond_value"] = p.BidbondValue
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["duration_month"] = p.DurationMonth
	p.fieldMap["duration_year"] = p.DurationYear
	p.fieldMap["fee"] = p.Fee
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["bidbond_info_id"] = p.OriginalID

}

func (p pMOBidbondInfoVersion) clone(db *gorm.DB) pMOBidbondInfoVersion {
	p.pMOBidbondInfoVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBidbondInfoVersion) replaceDB(db *gorm.DB) pMOBidbondInfoVersion {
	p.pMOBidbondInfoVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBidbondInfoVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBidbondInfoVersionHasOneProject) Where(conds ...field.Expr) *pMOBidbondInfoVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoVersionHasOneProject) WithContext(ctx context.Context) *pMOBidbondInfoVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoVersionHasOneProject) Session(session *gorm.Session) *pMOBidbondInfoVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoVersionHasOneProject) Model(m *models.PMOBidbondInfoVersion) *pMOBidbondInfoVersionHasOneProjectTx {
	return &pMOBidbondInfoVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoVersionHasOneProject) Unscoped() *pMOBidbondInfoVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoVersionHasOneProjectTx) Unscoped() *pMOBidbondInfoVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBidbondInfoVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBidbondInfoVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBidbondInfoVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMOBidbondInfoVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoVersionBelongsToCreatedBy) Model(m *models.PMOBidbondInfoVersion) *pMOBidbondInfoVersionBelongsToCreatedByTx {
	return &pMOBidbondInfoVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoVersionBelongsToCreatedBy) Unscoped() *pMOBidbondInfoVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoVersionBelongsToCreatedByTx) Unscoped() *pMOBidbondInfoVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBidbondInfoVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBidbondInfoVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBidbondInfoVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBidbondInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoVersionBelongsToUpdatedBy) Model(m *models.PMOBidbondInfoVersion) *pMOBidbondInfoVersionBelongsToUpdatedByTx {
	return &pMOBidbondInfoVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoVersionBelongsToUpdatedBy) Unscoped() *pMOBidbondInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoVersionBelongsToUpdatedByTx) Unscoped() *pMOBidbondInfoVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoVersionDo struct{ gen.DO }

type IPMOBidbondInfoVersionDo interface {
	gen.SubQuery
	Debug() IPMOBidbondInfoVersionDo
	WithContext(ctx context.Context) IPMOBidbondInfoVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBidbondInfoVersionDo
	WriteDB() IPMOBidbondInfoVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBidbondInfoVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBidbondInfoVersionDo
	Not(conds ...gen.Condition) IPMOBidbondInfoVersionDo
	Or(conds ...gen.Condition) IPMOBidbondInfoVersionDo
	Select(conds ...field.Expr) IPMOBidbondInfoVersionDo
	Where(conds ...gen.Condition) IPMOBidbondInfoVersionDo
	Order(conds ...field.Expr) IPMOBidbondInfoVersionDo
	Distinct(cols ...field.Expr) IPMOBidbondInfoVersionDo
	Omit(cols ...field.Expr) IPMOBidbondInfoVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo
	Group(cols ...field.Expr) IPMOBidbondInfoVersionDo
	Having(conds ...gen.Condition) IPMOBidbondInfoVersionDo
	Limit(limit int) IPMOBidbondInfoVersionDo
	Offset(offset int) IPMOBidbondInfoVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBidbondInfoVersionDo
	Unscoped() IPMOBidbondInfoVersionDo
	Create(values ...*models.PMOBidbondInfoVersion) error
	CreateInBatches(values []*models.PMOBidbondInfoVersion, batchSize int) error
	Save(values ...*models.PMOBidbondInfoVersion) error
	First() (*models.PMOBidbondInfoVersion, error)
	Take() (*models.PMOBidbondInfoVersion, error)
	Last() (*models.PMOBidbondInfoVersion, error)
	Find() ([]*models.PMOBidbondInfoVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBidbondInfoVersion, err error)
	FindInBatches(result *[]*models.PMOBidbondInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBidbondInfoVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBidbondInfoVersionDo
	Assign(attrs ...field.AssignExpr) IPMOBidbondInfoVersionDo
	Joins(fields ...field.RelationField) IPMOBidbondInfoVersionDo
	Preload(fields ...field.RelationField) IPMOBidbondInfoVersionDo
	FirstOrInit() (*models.PMOBidbondInfoVersion, error)
	FirstOrCreate() (*models.PMOBidbondInfoVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOBidbondInfoVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBidbondInfoVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBidbondInfoVersionDo) Debug() IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBidbondInfoVersionDo) WithContext(ctx context.Context) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBidbondInfoVersionDo) ReadDB() IPMOBidbondInfoVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBidbondInfoVersionDo) WriteDB() IPMOBidbondInfoVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBidbondInfoVersionDo) Session(config *gorm.Session) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBidbondInfoVersionDo) Clauses(conds ...clause.Expression) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBidbondInfoVersionDo) Returning(value interface{}, columns ...string) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBidbondInfoVersionDo) Not(conds ...gen.Condition) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBidbondInfoVersionDo) Or(conds ...gen.Condition) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBidbondInfoVersionDo) Select(conds ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBidbondInfoVersionDo) Where(conds ...gen.Condition) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBidbondInfoVersionDo) Order(conds ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBidbondInfoVersionDo) Distinct(cols ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBidbondInfoVersionDo) Omit(cols ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBidbondInfoVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBidbondInfoVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBidbondInfoVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBidbondInfoVersionDo) Group(cols ...field.Expr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBidbondInfoVersionDo) Having(conds ...gen.Condition) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBidbondInfoVersionDo) Limit(limit int) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBidbondInfoVersionDo) Offset(offset int) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBidbondInfoVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBidbondInfoVersionDo) Unscoped() IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBidbondInfoVersionDo) Create(values ...*models.PMOBidbondInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBidbondInfoVersionDo) CreateInBatches(values []*models.PMOBidbondInfoVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBidbondInfoVersionDo) Save(values ...*models.PMOBidbondInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBidbondInfoVersionDo) First() (*models.PMOBidbondInfoVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfoVersion), nil
	}
}

func (p pMOBidbondInfoVersionDo) Take() (*models.PMOBidbondInfoVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfoVersion), nil
	}
}

func (p pMOBidbondInfoVersionDo) Last() (*models.PMOBidbondInfoVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfoVersion), nil
	}
}

func (p pMOBidbondInfoVersionDo) Find() ([]*models.PMOBidbondInfoVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBidbondInfoVersion), err
}

func (p pMOBidbondInfoVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBidbondInfoVersion, err error) {
	buf := make([]*models.PMOBidbondInfoVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBidbondInfoVersionDo) FindInBatches(result *[]*models.PMOBidbondInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBidbondInfoVersionDo) Attrs(attrs ...field.AssignExpr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBidbondInfoVersionDo) Assign(attrs ...field.AssignExpr) IPMOBidbondInfoVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBidbondInfoVersionDo) Joins(fields ...field.RelationField) IPMOBidbondInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBidbondInfoVersionDo) Preload(fields ...field.RelationField) IPMOBidbondInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBidbondInfoVersionDo) FirstOrInit() (*models.PMOBidbondInfoVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfoVersion), nil
	}
}

func (p pMOBidbondInfoVersionDo) FirstOrCreate() (*models.PMOBidbondInfoVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfoVersion), nil
	}
}

func (p pMOBidbondInfoVersionDo) FindByPage(offset int, limit int) (result []*models.PMOBidbondInfoVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBidbondInfoVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBidbondInfoVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBidbondInfoVersionDo) Delete(models ...*models.PMOBidbondInfoVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBidbondInfoVersionDo) withDO(do gen.Dao) *pMOBidbondInfoVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
