package handlers

import (
	"net/http"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/timesheet/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetAdminController struct {
}

func (m TimesheetAdminController) Pagination(c core.IHTTPContext) error {
	input := &requests.TimesheetPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := emsgs.InvalidParamsError
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	res, ierr := timesheetSvc.Pagination(c.GetPageOptions(), &dto.TimesheetPaginationOptions{
		UserID:      input.UserID,
		StartDate:   input.StartDate,
		EndDate:     input.EndDate,
		ProjectCode: input.ProjectCode,
		TeamCode:    input.TeamCode,
		SgaID:       input.SgaID,
		Type:        input.Type,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m TimesheetAdminController) Find(c core.IHTTPContext) error {
	timesheetSvc := services.NewTimesheetService(c)
	timesheet, err := timesheetSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, timesheet)
}

func (m TimesheetAdminController) SummaryReport(c core.IHTTPContext) error {
	input := &requests.TimesheetSummaryReportRequest{}
	if err := c.Bind(input); err != nil {
		ierr := emsgs.InvalidParamsError
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	res, ierr := timesheetSvc.SummaryReport(&dto.TimesheetSummaryReportOptions{
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
		TeamCode:  strings.Split(utils.ToNonPointer(input.TeamCode), ","),
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
