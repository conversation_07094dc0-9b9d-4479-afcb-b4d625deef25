package repository

import (
	core "gitlab.finema.co/finema/idin-core"
)

// Option is a generic repository option that can mutate the repository query.
// Example usage:
//
//	type UserOption = Option[models.User]
//	repo := Build[models.User](ctx, someUserOption)
type Option[T IModel] func(IRepository[T])

// Build creates a repository for the given model type and applies provided options.
func Build[T IModel](c core.IContext, options ...Option[T]) IRepository[T] {
	r := New[T](c)
	for _, opt := range options {
		opt(r)
	}
	return r
}

// Factory is a typed constructor for repositories of model T.
// It enables concise declarations like:
//
//	var User = Make[models.User]()
//	// usage: repo := User(ctx, UserWithSearch("john"))
type Factory[T IModel] func(core.IContext, ...Option[T]) IRepository[T]

// Make returns a repository factory function for model T that applies options.
func Make[T IModel]() Factory[T] {
	return func(c core.IContext, options ...Option[T]) IRepository[T] {
		return Build[T](c, options...)
	}
}
