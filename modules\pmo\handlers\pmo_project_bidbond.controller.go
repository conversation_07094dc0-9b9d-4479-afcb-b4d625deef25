package handlers

import (
	"net/http"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectBidbondController struct {
}

// PMO Project Bidbond methods
func (m PMOProjectBidbondController) BidbondFind(c core.IHTTPContext) error {
	input := &requests.PMOBidbondFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoBidbondSvc := services.NewPMOProjectBidbondService(c)
	bidbond, err := pmoBidbondSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, bidbond)
}

func (m PMOProjectBidbondController) BidbondCreate(c core.IHTTPContext) error {
	input := &requests.PMOBidbondCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoBidbondSvc := services.NewPMOProjectBidbondService(c)
	startDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.StartDate))
	endDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.EndDate))
	payload := &dto.PMOBidbondCreatePayload{
		ProjectID:      c.Param("id"),
		GuaranteeAsset: utils.ToNonPointer(input.GuaranteeAsset),
		BidbondPayer:   utils.ToNonPointer(input.BidbondPayer),
		BidbondValue:   utils.ToNonPointer(input.BidbondValue),
		StartDate:      &startDate,
		EndDate:        &endDate,
		DurationMonth:  utils.ToNonPointer(input.DurationMonth),
		DurationYear:   utils.ToNonPointer(input.DurationYear),
		Fee:            utils.ToNonPointer(input.Fee),
	}

	bidbond, err := pmoBidbondSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, bidbond)
}

func (m PMOProjectBidbondController) BidbondVersionsFind(c core.IHTTPContext) error {
	pmoBidbondSvc := services.NewPMOProjectBidbondService(c)
	res, err := pmoBidbondSvc.BidbondVersionsFind(c.Param("id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
