# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
before:
  hooks:
  # You may remove this if you don't use go modules.
  #    - go mod tidy
  # you may remove this if you don't need go generate
#    - go generate ./...
builds:
  - skip: true

archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        format: zip
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^release:'
      - '^cicd:'
# .goreleaser.yml
gitlab_urls:
  api: https://gitlab.finema.co/api/v4/
  download: https://gitlab.finema.co

  # set to true if you use a self-signed certificate
  skip_tls_verify: false

  # set to true if you want to upload to the Package Registry rather than attachments
  # Only works with GitLab 13.5+
  #
  # Since: v1.3
  use_package_registry: false

  # Set this if you set GITLAB_TOKEN to the value of CI_JOB_TOKEN.
  #
  # Default: false
  # Since: v1.11
  use_job_token: true


# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
