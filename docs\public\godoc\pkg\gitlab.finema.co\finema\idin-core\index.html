<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="theme-color" content="#375EAB">

  <title>core - Go Documentation Server</title>

<link type="text/css" rel="stylesheet" href="../../../../lib/godoc/style.css">

<script>window.initFuncs = [];</script>
<script src="../../../../lib/godoc/jquery.js" defer></script>



<script>var goVersion = "go1.20.4";</script>
<script src="../../../../lib/godoc/godocs.js" defer></script>
</head>
<body>

<div id='lowframe' style="position: fixed; bottom: 0; left: 0; height: 0; width: 100%; border-top: thin solid grey; background-color: white; overflow: auto;">
...
</div><!-- #lowframe -->

<div id="topbar" class="wide"><div class="container">
<div class="top-heading" id="heading-wide"><a href="http://localhost:6060/pkg/">Go Documentation Server</a></div>
<div class="top-heading" id="heading-narrow"><a href="http://localhost:6060/pkg/">GoDoc</a></div>
<a href="index.html#" id="menu-button"><span id="menu-button-arrow">&#9661;</span></a>

</div></div>



<div id="page" class="wide">
<div class="container">


  <h1>
    Package core
    <span class="text-muted"></span>
  </h1>







<div id="nav"></div>


<!--
	Copyright 2009 The Go Authors. All rights reserved.
	Use of this source code is governed by a BSD-style
	license that can be found in the LICENSE file.
-->
<!--
	Note: Static (i.e., not template-generated) href and id
	attributes start with "pkg-" to make it impossible for
	them to conflict with generated attributes (some of which
	correspond to Go identifiers).
-->

	<script>
	document.ANALYSIS_DATA = null;
	document.CALLGRAPH = null;
	</script>



		<div id="short-nav">
			<dl>
			<dd><code>import "gitlab.finema.co/finema/idin-core"</code></dd>
			</dl>
			<dl>
			<dd><a href="index.html#pkg-overview" class="overviewLink">Overview</a></dd>
			<dd><a href="index.html#pkg-index" class="indexLink">Index</a></dd>


				<dd><a href="index.html#pkg-subdirectories">Subdirectories</a></dd>

			</dl>
		</div>
		<!-- The package's Name is printed as title by the top-level template -->
		<div id="pkg-overview" class="toggleVisible">
			<div class="collapsed">
				<h2 class="toggleButton" title="Click to show Overview section">Overview ▹</h2>
			</div>
			<div class="expanded">
				<h2 class="toggleButton" title="Click to hide Overview section">Overview ▾</h2>


			</div>
		</div>

		<div id="pkg-index" class="toggleVisible">
		<div class="collapsed">
			<h2 class="toggleButton" title="Click to show Index section">Index ▹</h2>
		</div>
		<div class="expanded">
			<h2 class="toggleButton" title="Click to hide Index section">Index ▾</h2>

		<!-- Table of contents for API; must be named manual-nav to turn off auto nav. -->
			<div id="manual-nav">
			<dl>

				<dd><a href="index.html#pkg-constants">Constants</a></dd>


				<dd><a href="index.html#pkg-variables">Variables</a></dd>



				<dd><a href="index.html#CaptureError">func CaptureError(ctx IContext, level sentry.Level, err error, args ...interface{})</a></dd>


				<dd><a href="index.html#CaptureErrorEcho">func CaptureErrorEcho(ctx echo.Context, level sentry.Level, err error)</a></dd>


				<dd><a href="index.html#CaptureHTTPError">func CaptureHTTPError(ctx IHTTPContext, level sentry.Level, err error, args ...interface{})</a></dd>


				<dd><a href="index.html#CaptureSimpleError">func CaptureSimpleError(level sentry.Level, err error, args ...interface{})</a></dd>


				<dd><a href="index.html#Core">func Core(options *HTTPContextOptions) func(next echo.HandlerFunc) echo.HandlerFunc</a></dd>


				<dd><a href="index.html#Crash">func Crash(err error) error</a></dd>


				<dd><a href="index.html#ErrorToJson">func ErrorToJson(err error) (m map[string]jsonErr)</a></dd>


				<dd><a href="index.html#Fake">func Fake(a interface{}) error</a></dd>


				<dd><a href="index.html#GetBodyString">func GetBodyString(c echo.Context) []byte</a></dd>


				<dd><a href="index.html#HTTPMiddlewareCore">func HTTPMiddlewareCore(options *HTTPContextOptions) echo.MiddlewareFunc</a></dd>


				<dd><a href="index.html#HTTPMiddlewareCreateLogger">func HTTPMiddlewareCreateLogger(next echo.HandlerFunc) echo.HandlerFunc</a></dd>


				<dd><a href="index.html#HTTPMiddlewareFromCache">func HTTPMiddlewareFromCache(key func(IHTTPContext) string) echo.MiddlewareFunc</a></dd>


				<dd><a href="index.html#HTTPMiddlewareHandleError">func HTTPMiddlewareHandleError(env IENV) echo.HTTPErrorHandler</a></dd>


				<dd><a href="index.html#HTTPMiddlewareHandleNotFound">func HTTPMiddlewareHandleNotFound(c echo.Context) error</a></dd>


				<dd><a href="index.html#HTTPMiddlewareRateLimit">func HTTPMiddlewareRateLimit(options *HTTPContextOptions) echo.MiddlewareFunc</a></dd>


				<dd><a href="index.html#HTTPMiddlewareRecoverWithConfig">func HTTPMiddlewareRecoverWithConfig(env IENV, config middleware.RecoverConfig) echo.MiddlewareFunc</a></dd>


				<dd><a href="index.html#HTTPMiddlewareRequestID">func HTTPMiddlewareRequestID() echo.MiddlewareFunc</a></dd>


				<dd><a href="index.html#IsError">func IsError(err error) bool</a></dd>


				<dd><a href="index.html#MockMiddleware">func MockMiddleware(model interface{}, options *MockMiddlewareOptions) func(next echo.HandlerFunc) echo.HandlerFunc</a></dd>


				<dd><a href="index.html#NewHTTPServer">func NewHTTPServer(options *HTTPContextOptions) *echo.Echo</a></dd>


				<dd><a href="index.html#NewMockENV">func NewMockENV() *mockENV</a></dd>


				<dd><a href="index.html#Recover">func Recover(textError string)</a></dd>


				<dd><a href="index.html#RequesterToStructPagination">func RequesterToStructPagination(items interface{}, options *PageOptions, requester func() (*RequestResponse, error)) (*PageResponse, IError)</a></dd>


				<dd><a href="index.html#SetSearch">func SetSearch(db *gorm.DB, keywordCondition *KeywordConditionWrapper) *gorm.DB</a></dd>


				<dd><a href="index.html#SetSearchSimple">func SetSearchSimple(db *gorm.DB, q string, columns []string) *gorm.DB</a></dd>


				<dd><a href="index.html#StartHTTPServer">func StartHTTPServer(e *echo.Echo, env IENV)</a></dd>


				<dd><a href="index.html#WithHTTPContext">func WithHTTPContext(h HandlerFunc) echo.HandlerFunc</a></dd>



				<dd><a href="index.html#ABCIContext">type ABCIContext</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#ABCIContext.GetMessageJSON">func (A ABCIContext) GetMessageJSON(tx []byte) string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ABCIContext.GetOperation">func (A ABCIContext) GetOperation(tx []byte) string</a></dd>



				<dd><a href="index.html#ABCIContextOptions">type ABCIContextOptions</a></dd>




				<dd><a href="index.html#ArchiveByteBody">type ArchiveByteBody</a></dd>




				<dd><a href="index.html#ArchiverOptions">type ArchiverOptions</a></dd>




				<dd><a href="index.html#BaseValidator">type BaseValidator</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.AddValidator">func (b *BaseValidator) AddValidator(errs ...*Valid)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.Error">func (b *BaseValidator) Error() IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.GetValidator">func (b *BaseValidator) GetValidator() *Valid</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsArrayBetween">func (b *BaseValidator) IsArrayBetween(array interface{}, min int, max int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsArrayMax">func (b *BaseValidator) IsArrayMax(array interface{}, size int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsArrayMin">func (b *BaseValidator) IsArrayMin(array interface{}, size int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsArraySize">func (b *BaseValidator) IsArraySize(array interface{}, size int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsBase64">func (b *BaseValidator) IsBase64(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsBoolRequired">func (b *BaseValidator) IsBoolRequired(value interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsCustom">func (b *BaseValidator) IsCustom(customFunc func() (bool, *IValidMessage)) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsDateTime">func (b *BaseValidator) IsDateTime(input *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsDateTimeAfter">func (b *BaseValidator) IsDateTimeAfter(input *string, after *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsDateTimeBefore">func (b *BaseValidator) IsDateTimeBefore(input *string, before *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsEmail">func (b *BaseValidator) IsEmail(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsExists">func (b *BaseValidator) IsExists(ctx IContext, field *string, table string, column string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsExistsWithCondition">func (b *BaseValidator) IsExistsWithCondition(ctx IContext, table string, condition map[string]interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsFloatNumberBetween">func (b *BaseValidator) IsFloatNumberBetween(field *float64, min float64, max float64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsFloatNumberMax">func (b *BaseValidator) IsFloatNumberMax(field *float64, max float64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsFloatNumberMin">func (b *BaseValidator) IsFloatNumberMin(field *float64, min float64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsIP">func (b *BaseValidator) IsIP(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONArray">func (b *BaseValidator) IsJSONArray(field *json.RawMessage, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONArrayMax">func (b *BaseValidator) IsJSONArrayMax(field *json.RawMessage, max int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONArrayMin">func (b *BaseValidator) IsJSONArrayMin(field *json.RawMessage, min int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONBoolPathRequired">func (b *BaseValidator) IsJSONBoolPathRequired(json *json.RawMessage, path string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONObject">func (b *BaseValidator) IsJSONObject(field *json.RawMessage, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONObjectNotEmpty">func (b *BaseValidator) IsJSONObjectNotEmpty(field *json.RawMessage, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONObjectPath">func (b *BaseValidator) IsJSONObjectPath(j *json.RawMessage, path string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONPathRequireNotEmpty">func (b *BaseValidator) IsJSONPathRequireNotEmpty(j *json.RawMessage, path string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONPathRequired">func (b *BaseValidator) IsJSONPathRequired(j *json.RawMessage, path string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONPathStrIn">func (b *BaseValidator) IsJSONPathStrIn(json *json.RawMessage, path string, rules string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONRequired">func (b *BaseValidator) IsJSONRequired(field *json.RawMessage, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsJSONStrPathRequired">func (b *BaseValidator) IsJSONStrPathRequired(json *json.RawMessage, path string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsMongoExistsWithCondition">func (b *BaseValidator) IsMongoExistsWithCondition(ctx IContext, table string, filter interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsMongoStrUnique">func (b *BaseValidator) IsMongoStrUnique(ctx IContext, table string, filter interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsNotEmptyObjectRequired">func (b *BaseValidator) IsNotEmptyObjectRequired(value interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsNumberBetween">func (b *BaseValidator) IsNumberBetween(field *int64, min int64, max int64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsNumberMax">func (b *BaseValidator) IsNumberMax(field *int64, max int64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsNumberMin">func (b *BaseValidator) IsNumberMin(field *int64, min int64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsRequired">func (b *BaseValidator) IsRequired(field interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsRequiredArray">func (b *BaseValidator) IsRequiredArray(array interface{}, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStrIn">func (b *BaseValidator) IsStrIn(input *string, rules string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStrMax">func (b *BaseValidator) IsStrMax(input *string, size int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStrMin">func (b *BaseValidator) IsStrMin(input *string, size int, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStrRequired">func (b *BaseValidator) IsStrRequired(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStrUnique">func (b *BaseValidator) IsStrUnique(ctx IContext, field *string, table string, column string, except string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringContain">func (b *BaseValidator) IsStringContain(field *string, substr string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringEndWith">func (b *BaseValidator) IsStringEndWith(field *string, substr string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringLowercase">func (b *BaseValidator) IsStringLowercase(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringNotContain">func (b *BaseValidator) IsStringNotContain(field *string, substr string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringNumber">func (b *BaseValidator) IsStringNumber(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringNumberMin">func (b *BaseValidator) IsStringNumberMin(field *string, min int64, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringStartWith">func (b *BaseValidator) IsStringStartWith(field *string, substr string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsStringUppercase">func (b *BaseValidator) IsStringUppercase(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsTime">func (b *BaseValidator) IsTime(input *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsTimeAfter">func (b *BaseValidator) IsTimeAfter(input *string, after *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsTimeBefore">func (b *BaseValidator) IsTimeBefore(input *string, before *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsTimeRequired">func (b *BaseValidator) IsTimeRequired(field *time.Time, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.IsURL">func (b *BaseValidator) IsURL(field *string, fieldPath string) (bool, *IValidMessage)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.LoopJSONArray">func (b *BaseValidator) LoopJSONArray(j *json.RawMessage) []interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.Merge">func (b *BaseValidator) Merge(errs ...*Valid) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.Must">func (b *BaseValidator) Must(condition bool, msg *IValidMessage) bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#BaseValidator.SetPrefix">func (b *BaseValidator) SetPrefix(prefix string)</a></dd>



				<dd><a href="index.html#ContextMock">type ContextMock</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockContext">func NewMockContext() *ContextMock</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.Cache">func (m *ContextMock) Cache() ICache</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.Caches">func (m *ContextMock) Caches(name string) ICache</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.DB">func (m *ContextMock) DB() *gorm.DB</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.DBMongo">func (m *ContextMock) DBMongo() IMongoDB</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.DBS">func (m *ContextMock) DBS(name string) *gorm.DB</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.DBSMongo">func (m *ContextMock) DBSMongo(name string) IMongoDB</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.ENV">func (m *ContextMock) ENV() IENV</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.Log">func (m *ContextMock) Log() ILogger</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.MQ">func (m *ContextMock) MQ() IMQ</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.NewError">func (m *ContextMock) NewError(err error, errorType IError, args ...interface{}) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.Requester">func (m *ContextMock) Requester() IRequester</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.SetType">func (m *ContextMock) SetType(t consts.ContextType)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ContextMock.Type">func (m *ContextMock) Type() string</a></dd>



				<dd><a href="index.html#ContextOptions">type ContextOptions</a></dd>




				<dd><a href="index.html#ContextUser">type ContextUser</a></dd>




				<dd><a href="index.html#CronjobContext">type CronjobContext</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#CronjobContext.AddJob">func (c CronjobContext) AddJob(job *gocron.Scheduler, handlerFunc func(ctx ICronjobContext) error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#CronjobContext.Job">func (c CronjobContext) Job() *gocron.Scheduler</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#CronjobContext.Start">func (c CronjobContext) Start()</a></dd>



				<dd><a href="index.html#CronjobContextOptions">type CronjobContextOptions</a></dd>




				<dd><a href="index.html#Database">type Database</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewDatabase">func NewDatabase(env *ENVConfig) *Database</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewDatabaseWithConfig">func NewDatabaseWithConfig(env *ENVConfig, config *gorm.Config) *Database</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Database.Connect">func (db *Database) Connect() (*gorm.DB, error)</a></dd>



				<dd><a href="index.html#DatabaseCache">type DatabaseCache</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewCache">func NewCache(env *ENVConfig) *DatabaseCache</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#DatabaseCache.Connect">func (r DatabaseCache) Connect() (ICache, error)</a></dd>



				<dd><a href="index.html#DatabaseMongo">type DatabaseMongo</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewDatabaseMongo">func NewDatabaseMongo(env *ENVConfig) *DatabaseMongo</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#DatabaseMongo.Connect">func (db *DatabaseMongo) Connect() (IMongoDB, error)</a></dd>



				<dd><a href="index.html#E2EContext">type E2EContext</a></dd>




				<dd><a href="index.html#E2EContextOptions">type E2EContextOptions</a></dd>




				<dd><a href="index.html#ENVConfig">type ENVConfig</a></dd>




				<dd><a href="index.html#ENVType">type ENVType</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.All">func (e ENVType) All() map[string]string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.Bool">func (e ENVType) Bool(key string) bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.Config">func (e ENVType) Config() *ENVConfig</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.Int">func (e ENVType) Int(key string) int</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.IsDev">func (e ENVType) IsDev() bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.IsMock">func (e ENVType) IsMock() bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.IsProd">func (e ENVType) IsProd() bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.IsTest">func (e ENVType) IsTest() bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ENVType.String">func (e ENVType) String(key string) string</a></dd>



				<dd><a href="index.html#Error">type Error</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Error.Error">func (c Error) Error() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Error.GetCode">func (c Error) GetCode() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Error.GetMessage">func (c Error) GetMessage() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Error.GetStatus">func (c Error) GetStatus() int</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Error.JSON">func (c Error) JSON() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Error.OriginalError">func (c Error) OriginalError() error</a></dd>



				<dd><a href="index.html#FieldError">type FieldError</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.Error">func (f FieldError) Error() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.GetCode">func (f FieldError) GetCode() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.GetMessage">func (f FieldError) GetMessage() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.GetStatus">func (FieldError) GetStatus() int</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.JSON">func (f FieldError) JSON() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.OriginalError">func (f FieldError) OriginalError() error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#FieldError.OriginalErrorMessage">func (f FieldError) OriginalErrorMessage() string</a></dd>



				<dd><a href="index.html#File">type File</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#File.Name">func (f File) Name() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#File.Value">func (f File) Value() []byte</a></dd>



				<dd><a href="index.html#HTTPContext">type HTTPContext</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.BindOnly">func (c *HTTPContext) BindOnly(i interface{}) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.BindWithValidate">func (c *HTTPContext) BindWithValidate(ctx IValidateContext) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.BindWithValidateMessage">func (c *HTTPContext) BindWithValidateMessage(ctx IValidateContext) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.GetMessage">func (c *HTTPContext) GetMessage() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.GetPageOptions">func (c *HTTPContext) GetPageOptions() *PageOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.GetPageOptionsWithOptions">func (c *HTTPContext) GetPageOptionsWithOptions(options *PageOptionsOptions) *PageOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.GetSignature">func (c *HTTPContext) GetSignature() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.GetUserAgent">func (c HTTPContext) GetUserAgent() *user_agent.UserAgent</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.Log">func (c *HTTPContext) Log() ILogger</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.NewError">func (c *HTTPContext) NewError(err error, errorType IError, args ...interface{}) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#HTTPContext.WithSaveCache">func (c *HTTPContext) WithSaveCache(data interface{}, key string, duration time.Duration) interface{}</a></dd>



				<dd><a href="index.html#HTTPContextOptions">type HTTPContextOptions</a></dd>




				<dd><a href="index.html#HandlerFunc">type HandlerFunc</a></dd>




				<dd><a href="index.html#IABCIContext">type IABCIContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewABCIContext">func NewABCIContext(options *ABCIContextOptions) IABCIContext</a></dd>




				<dd><a href="index.html#IArchiver">type IArchiver</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewArchiver">func NewArchiver(ctx IContext) IArchiver</a></dd>




				<dd><a href="index.html#ICSV">type ICSV</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewCSV">func NewCSV[T any](ctx IContext) ICSV[T]</a></dd>




				<dd><a href="index.html#ICSVOptions">type ICSVOptions</a></dd>




				<dd><a href="index.html#ICache">type ICache</a></dd>




				<dd><a href="index.html#IContext">type IContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewContext">func NewContext(options *ContextOptions) IContext</a></dd>




				<dd><a href="index.html#ICronjobContext">type ICronjobContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewCronjobContext">func NewCronjobContext(options *CronjobContextOptions) ICronjobContext</a></dd>




				<dd><a href="index.html#IE2EContext">type IE2EContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewE2EContext">func NewE2EContext(options *E2EContextOptions) IE2EContext</a></dd>




				<dd><a href="index.html#IENV">type IENV</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewENVPath">func NewENVPath(path string) IENV</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewEnv">func NewEnv() IENV</a></dd>




				<dd><a href="index.html#IEmail">type IEmail</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewEmail">func NewEmail(ctx IContext) IEmail</a></dd>




				<dd><a href="index.html#IError">type IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#DBErrorToIError">func DBErrorToIError(err error) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockIError">func MockIError(args mock.Arguments, index int) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewValidatorFields">func NewValidatorFields(fields interface{}) IError</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#RequesterToStruct">func RequesterToStruct(desc interface{}, requester func() (*RequestResponse, error)) IError</a></dd>




				<dd><a href="index.html#IFile">type IFile</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewFile">func NewFile(name string, value []byte) IFile</a></dd>




				<dd><a href="index.html#IHTTPContext">type IHTTPContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewHTTPContext">func NewHTTPContext(ctx echo.Context, options *HTTPContextOptions) IHTTPContext</a></dd>




				<dd><a href="index.html#ILogger">type ILogger</a></dd>




				<dd><a href="index.html#IMQ">type IMQ</a></dd>




				<dd><a href="index.html#IMQContext">type IMQContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMQContext">func NewMQContext(options *MQContextOptions) IMQContext</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMQServer">func NewMQServer(options *MQContextOptions) IMQContext</a></dd>




				<dd><a href="index.html#IMongoDB">type IMongoDB</a></dd>




				<dd><a href="index.html#IMongoDBHelper">type IMongoDBHelper</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMongoHelper">func NewMongoHelper() IMongoDBHelper</a></dd>




				<dd><a href="index.html#IMongoIndexBatch">type IMongoIndexBatch</a></dd>




				<dd><a href="index.html#IMongoIndexer">type IMongoIndexer</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMongoIndexer">func NewMongoIndexer(ctx IContext) IMongoIndexer</a></dd>




				<dd><a href="index.html#IRequester">type IRequester</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewRequester">func NewRequester(ctx IContext) IRequester</a></dd>




				<dd><a href="index.html#IS3">type IS3</a></dd>




				<dd><a href="index.html#ISeed">type ISeed</a></dd>




				<dd><a href="index.html#IValidMessage">type IValidMessage</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#IValidMessage.Error">func (f IValidMessage) Error() string</a></dd>



				<dd><a href="index.html#IValidate">type IValidate</a></dd>




				<dd><a href="index.html#IValidateContext">type IValidateContext</a></dd>




				<dd><a href="index.html#IWinRM">type IWinRM</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewWinRM">func NewWinRM(ctx IContext) IWinRM</a></dd>




				<dd><a href="index.html#KeywordCondition">type KeywordCondition</a></dd>




				<dd><a href="index.html#KeywordConditionWrapper">type KeywordConditionWrapper</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordAndCondition">func NewKeywordAndCondition(keywordOptions []KeywordOptions) *KeywordConditionWrapper</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordOrCondition">func NewKeywordOrCondition(keywordOptions []KeywordOptions) *KeywordConditionWrapper</a></dd>




				<dd><a href="index.html#KeywordOptions">type KeywordOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordMustMatchOption">func NewKeywordMustMatchOption(key string, value string) *KeywordOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordMustMatchOptions">func NewKeywordMustMatchOptions(keys []string, value string) []KeywordOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordWildCardOption">func NewKeywordWildCardOption(key string, value string) *KeywordOptions</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewKeywordWildCardOptions">func NewKeywordWildCardOptions(keys []string, value string) []KeywordOptions</a></dd>




				<dd><a href="index.html#KeywordType">type KeywordType</a></dd>




				<dd><a href="index.html#Logger">type Logger</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewLogger">func NewLogger(ctx IContext) *Logger</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewLoggerSimple">func NewLoggerSimple() *Logger</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Logger.Debug">func (logger *Logger) Debug(args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Logger.DebugWithSkip">func (logger *Logger) DebugWithSkip(skip int, args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Logger.Error">func (logger *Logger) Error(message error, args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Logger.ErrorWithSkip">func (logger *Logger) ErrorWithSkip(skip int, message error, args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Logger.Info">func (logger *Logger) Info(args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Logger.Warn">func (logger *Logger) Warn(args ...interface{})</a></dd>



				<dd><a href="index.html#MQ">type MQ</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMQ">func NewMQ(env *ENVConfig) *MQ</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MQ.Connect">func (m *MQ) Connect() (IMQ, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MQ.ReConnect">func (m *MQ) ReConnect() (*amqp.Connection, error)</a></dd>



				<dd><a href="index.html#MQConsumeOptions">type MQConsumeOptions</a></dd>




				<dd><a href="index.html#MQContext">type MQContext</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MQContext.AddConsumer">func (c *MQContext) AddConsumer(handlerFunc func(ctx IMQContext))</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MQContext.Consume">func (c *MQContext) Consume(name string, onConsume func(message amqp.Delivery), options *MQConsumeOptions)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MQContext.Start">func (c *MQContext) Start()</a></dd>



				<dd><a href="index.html#MQContextOptions">type MQContextOptions</a></dd>




				<dd><a href="index.html#MQPublishOptions">type MQPublishOptions</a></dd>




				<dd><a href="index.html#Map">type Map</a></dd>




				<dd><a href="index.html#MockCache">type MockCache</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockCache">func NewMockCache() *MockCache</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.Close">func (m *MockCache) Close()</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.Del">func (m *MockCache) Del(key string) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.Get">func (m *MockCache) Get(dest interface{}, key string) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.GetJSON">func (m *MockCache) GetJSON(dest interface{}, key string) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.Set">func (m *MockCache) Set(key string, value interface{}, expiration time.Duration) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockCache.SetJSON">func (m *MockCache) SetJSON(key string, value interface{}, expiration time.Duration) error</a></dd>



				<dd><a href="index.html#MockDatabase">type MockDatabase</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockDatabase">func NewMockDatabase() *MockDatabase</a></dd>




				<dd><a href="index.html#MockLogger">type MockLogger</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockLogger">func NewMockLogger() *MockLogger</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockLogger.Debug">func (m *MockLogger) Debug(args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockLogger.Error">func (m *MockLogger) Error(message error, args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockLogger.Info">func (m *MockLogger) Info(args ...interface{})</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockLogger.Warn">func (m *MockLogger) Warn(args ...interface{})</a></dd>



				<dd><a href="index.html#MockMQ">type MockMQ</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockMQ">func NewMockMQ() *MockMQ</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockMQ.Close">func (m *MockMQ) Close()</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMQ.Conn">func (m *MockMQ) Conn() *amqp.Connection</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMQ.Consume">func (m *MockMQ) Consume(name string, onConsume func(message amqp.Delivery), options *MQConsumeOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMQ.PublishJSON">func (m *MockMQ) PublishJSON(name string, data interface{}, options *MQPublishOptions) error</a></dd>



				<dd><a href="index.html#MockMiddlewareManual">type MockMiddlewareManual</a></dd>




				<dd><a href="index.html#MockMiddlewareOptions">type MockMiddlewareOptions</a></dd>




				<dd><a href="index.html#MockMiddlewareWrapper">type MockMiddlewareWrapper</a></dd>




				<dd><a href="index.html#MockMongoDB">type MockMongoDB</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockMongoDB">func NewMockMongoDB() *MockMongoDB</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Close">func (m *MockMongoDB) Close()</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Count">func (m *MockMongoDB) Count(coll string, filter interface{}, opts ...*options.CountOptions) (int64, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Create">func (m *MockMongoDB) Create(coll string, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.DB">func (m *MockMongoDB) DB() *mongo.Database</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.DeleteMany">func (m *MockMongoDB) DeleteMany(coll string, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.DeleteOne">func (m *MockMongoDB) DeleteOne(coll string, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Drop">func (m *MockMongoDB) Drop(coll string) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Find">func (m *MockMongoDB) Find(dest interface{}, coll string, filter interface{}, opts ...*options.FindOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindAggregate">func (m *MockMongoDB) FindAggregate(dest interface{}, coll string, pipeline interface{}, opts ...*options.AggregateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindAggregateOne">func (m *MockMongoDB) FindAggregateOne(dest interface{}, coll string, pipeline interface{}, opts ...*options.AggregateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindAggregatePagination">func (m *MockMongoDB) FindAggregatePagination(dest interface{}, coll string, pipeline interface{}, pageOptions *PageOptions, opts ...*options.AggregateOptions) (*PageResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindOne">func (m *MockMongoDB) FindOne(dest interface{}, coll string, filter interface{}, opts ...*options.FindOneOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindOneAndDelete">func (m *MockMongoDB) FindOneAndDelete(coll string, filter interface{}, opts ...*options.FindOneAndDeleteOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindOneAndUpdate">func (m *MockMongoDB) FindOneAndUpdate(dest interface{}, coll string, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.FindPagination">func (m *MockMongoDB) FindPagination(dest interface{}, coll string, filter interface{}, pageOptions *PageOptions, opts ...*options.FindOptions) (*PageResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.Helper">func (m *MockMongoDB) Helper() IMongoDBHelper</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoDB.UpdateOne">func (m *MockMongoDB) UpdateOne(coll string, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)</a></dd>



				<dd><a href="index.html#MockMongoIndexBatch">type MockMongoIndexBatch</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockMongoIndexBatch">func NewMockMongoIndexBatch() *MockMongoIndexBatch</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoIndexBatch.Name">func (m *MockMongoIndexBatch) Name() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoIndexBatch.Run">func (m *MockMongoIndexBatch) Run() error</a></dd>



				<dd><a href="index.html#MockMongoIndexer">type MockMongoIndexer</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockMongoIndexer">func NewMockMongoIndexer() *MockMongoIndexer</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoIndexer.Add">func (m *MockMongoIndexer) Add(batch *MockMongoIndexBatch)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockMongoIndexer.Execute">func (m *MockMongoIndexer) Execute() error</a></dd>



				<dd><a href="index.html#MockRequester">type MockRequester</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewMockRequester">func NewMockRequester() *MockRequester</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MockRequester.Delete">func (m *MockRequester) Delete(url string, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockRequester.Get">func (m *MockRequester) Get(url string, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockRequester.Patch">func (m *MockRequester) Patch(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockRequester.Post">func (m *MockRequester) Post(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MockRequester.Put">func (m *MockRequester) Put(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>



				<dd><a href="index.html#MongoDB">type MongoDB</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Close">func (m MongoDB) Close()</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Count">func (m MongoDB) Count(coll string, filter interface{}, opts ...*options.CountOptions) (int64, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Create">func (m MongoDB) Create(coll string, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.CreateIndex">func (m MongoDB) CreateIndex(coll string, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.DB">func (m MongoDB) DB() *mongo.Database</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.DeleteMany">func (m MongoDB) DeleteMany(coll string, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.DeleteOne">func (m MongoDB) DeleteOne(coll string, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Drop">func (m MongoDB) Drop(coll string) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.DropAll">func (m MongoDB) DropAll(coll string, opts ...*options.DropIndexesOptions) (*MongoDropIndexResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.DropIndex">func (m MongoDB) DropIndex(coll string, name string, opts ...*options.DropIndexesOptions) (*MongoDropIndexResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Find">func (m MongoDB) Find(dest interface{}, coll string, filter interface{}, opts ...*options.FindOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindAggregate">func (m MongoDB) FindAggregate(dest interface{}, coll string, pipeline interface{}, opts ...*options.AggregateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindAggregateOne">func (m MongoDB) FindAggregateOne(dest interface{}, coll string, pipeline interface{}, opts ...*options.AggregateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindAggregatePagination">func (m MongoDB) FindAggregatePagination(dest interface{}, coll string, pipeline interface{}, pageOptions *PageOptions, opts ...*options.AggregateOptions) (*PageResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindOne">func (m MongoDB) FindOne(dest interface{}, coll string, filter interface{}, opts ...*options.FindOneOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindOneAndDelete">func (m MongoDB) FindOneAndDelete(coll string, filter interface{}, opts ...*options.FindOneAndDeleteOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindOneAndUpdate">func (m MongoDB) FindOneAndUpdate(dest interface{}, coll string, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.FindPagination">func (m MongoDB) FindPagination(dest interface{}, coll string, filter interface{}, pageOptions *PageOptions, opts ...*options.FindOptions) (*PageResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.Helper">func (m MongoDB) Helper() IMongoDBHelper</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.ListIndex">func (m MongoDB) ListIndex(coll string, opts ...*options.ListIndexesOptions) ([]MongoListIndexResult, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoDB.UpdateOne">func (m MongoDB) UpdateOne(coll string, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)</a></dd>



				<dd><a href="index.html#MongoDropIndexResult">type MongoDropIndexResult</a></dd>




				<dd><a href="index.html#MongoFilterOptions">type MongoFilterOptions</a></dd>




				<dd><a href="index.html#MongoIndexer">type MongoIndexer</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#MongoIndexer.Add">func (i *MongoIndexer) Add(batch IMongoIndexBatch)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#MongoIndexer.Execute">func (i *MongoIndexer) Execute() error</a></dd>



				<dd><a href="index.html#MongoListIndexResult">type MongoListIndexResult</a></dd>




				<dd><a href="index.html#MongoLookupOptions">type MongoLookupOptions</a></dd>




				<dd><a href="index.html#PageOptions">type PageOptions</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#PageOptions.SetOrderDefault">func (p *PageOptions) SetOrderDefault(orders ...string)</a></dd>



				<dd><a href="index.html#PageOptionsOptions">type PageOptionsOptions</a></dd>




				<dd><a href="index.html#PageResponse">type PageResponse</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Paginate">func Paginate(db *gorm.DB, model interface{}, options *PageOptions) (*PageResponse, error)</a></dd>




				<dd><a href="index.html#Pagination">type Pagination</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewPagination">func NewPagination(items interface{}, options *PageResponse) *Pagination</a></dd>




				<dd><a href="index.html#RequestResponse">type RequestResponse</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#RequestWrapper">func RequestWrapper(dest interface{}, requester func() (*RequestResponse, error)) (*RequestResponse, error)</a></dd>




				<dd><a href="index.html#Requester">type Requester</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Create">func (r Requester) Create(method RequesterMethodType, url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Delete">func (r Requester) Delete(url string, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Get">func (r Requester) Get(url string, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Patch">func (r Requester) Patch(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Post">func (r Requester) Post(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Requester.Put">func (r Requester) Put(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)</a></dd>



				<dd><a href="index.html#RequesterMethodType">type RequesterMethodType</a></dd>




				<dd><a href="index.html#RequesterOptions">type RequesterOptions</a></dd>




				<dd><a href="index.html#S3Config">type S3Config</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewS3">func NewS3(env *ENVConfig) *S3Config</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#S3Config.Connect">func (r *S3Config) Connect() (IS3, error)</a></dd>



				<dd><a href="index.html#Seeder">type Seeder</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewSeeder">func NewSeeder() *Seeder</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Seeder.Add">func (receiver Seeder) Add(seeder ISeed) error</a></dd>



				<dd><a href="index.html#UploadOptions">type UploadOptions</a></dd>




				<dd><a href="index.html#Valid">type Valid</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#NewValid">func NewValid() *Valid</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Valid.Add">func (v *Valid) Add(err ...error)</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.Error">func (v *Valid) Error() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.GetCode">func (v *Valid) GetCode() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.GetMessage">func (v *Valid) GetMessage() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.GetStatus">func (v *Valid) GetStatus() int</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.JSON">func (v *Valid) JSON() interface{}</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.Must">func (v *Valid) Must(x bool, msg *IValidMessage) bool</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.OriginalError">func (v *Valid) OriginalError() error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.OriginalErrorMessage">func (v *Valid) OriginalErrorMessage() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#Valid.Valid">func (v *Valid) Valid() IError</a></dd>



				<dd><a href="index.html#ValidError">type ValidError</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#ValidError.Error">func (err *ValidError) Error() string</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ValidError.Errors">func (err *ValidError) Errors() []error</a></dd>


					<dd>&nbsp; &nbsp; <a href="index.html#ValidError.Strings">func (err *ValidError) Strings() []string</a></dd>



				<dd><a href="index.html#Validator">type Validator</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#Validator.Validate">func (cv *Validator) Validate(i interface{}) error</a></dd>



				<dd><a href="index.html#WinRM">type WinRM</a></dd>



					<dd>&nbsp; &nbsp; <a href="index.html#WinRM.Command">func (w *WinRM) Command(command string, isProduction bool) (*WinRMResult, IError)</a></dd>



				<dd><a href="index.html#WinRMResult">type WinRMResult</a></dd>




			</dl>
			</div><!-- #manual-nav -->




			<h3>Package files</h3>
			<p>
			<span style="font-size:90%">

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go">abci_context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/archiver.go">archiver.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache.go">cache.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go">cache_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context.go">context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go">context_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go">cronjob_context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/csv.go">csv.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go">database.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mock.go">database_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go">database_mongo.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_helper.go">database_mongo_helper.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go">database_mongo_indexer.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go">database_mongo_indexer_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go">database_mongo_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/e2e_context.go">e2e_context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/email.go">email.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go">env.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env_mock.go">env_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go">error.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error_mock.go">error_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/faker.go">faker.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go">http_context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_core.go">http_middleware_core.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_cors.go">http_middleware_cors.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_error.go">http_middleware_error.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_logger.go">http_middleware_logger.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_mock.go">http_middleware_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_rate_limit.go">http_middleware_rate_limit.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_request_id.go">http_middleware_request_id.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_with_cache.go">http_middleware_with_cache.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_server.go">http_server.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go">logger.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go">logger_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go">mq.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go">mq_context.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go">mq_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_server.go">mq_server.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go">pagination.model.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/request.const.go">request.const.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go">requester.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go">requester_mock.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go">s3.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/seeder.go">seeder.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go">sentry.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/types.go">types.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go">valid.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go">valid_base_validator.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_message.go">valid_message.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go">validator.go</a>

				<a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go">winrm.go</a>

			</span>
			</p>

		</div><!-- .expanded -->
		</div><!-- #pkg-index -->




			<h2 id="pkg-constants">Constants</h2>


				<pre>const (
    <span id="MustMatch">MustMatch</span> <a href="index.html#KeywordType">KeywordType</a> = &#34;must_match&#34;
    <span id="Wildcard">Wildcard</span>  <a href="index.html#KeywordType">KeywordType</a> = &#34;wildcard&#34;

    <span id="And">And</span> <a href="index.html#KeywordCondition">KeywordCondition</a> = &#34;and&#34;
    <span id="Or">Or</span>  <a href="index.html#KeywordCondition">KeywordCondition</a> = &#34;or&#34;
)</pre>


				<pre>const (
    <span id="DatabaseDriverPOSTGRES">DatabaseDriverPOSTGRES</span> = &#34;postgres&#34;
    <span id="DatabaseDriverMSSQL">DatabaseDriverMSSQL</span>    = &#34;mssql&#34;
    <span id="DatabaseDriverMYSQL">DatabaseDriverMYSQL</span>    = &#34;mysql&#34;
)</pre>


				<pre>const <span id="DateFormat">DateFormat</span> = &#34;2006-01-02 15:04:05&#34;</pre>


				<pre>const <span id="EnvFileName">EnvFileName</span> = &#34;.env&#34;</pre>


				<pre>const <span id="EnvTestFileName">EnvTestFileName</span> = &#34;test.env&#34;</pre>


				<pre>const <span id="TimeFormat">TimeFormat</span> = &#34;15:04:05&#34;</pre>


				<pre>const <span id="TimeFormatRegex">TimeFormatRegex</span> = &#34;^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$&#34;</pre>



			<h2 id="pkg-variables">Variables</h2>


				<pre>var <span id="ArrayBetweenM">ArrayBetweenM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#int">int</a>, max <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_ARRAY_SIZE_BETWEEN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field field must contain between %v and %v item(s)&#34;, field, min, max),
        <a href="index.html#IValidMessage.Data">Data</a>: map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}{
            &#34;min&#34;: min,
            &#34;max&#34;: max,
        },
    }
}</pre>


				<pre>var <span id="ArrayM">ArrayM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_ARRAY&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be array format&#34;,
    }
}</pre>


				<pre>var <span id="ArrayMaxM">ArrayMaxM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, max <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_ARRAY_SIZE_MAX&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must not be greater than %v item(s)&#34;, field, max),
        <a href="index.html#IValidMessage.Data">Data</a>:    max,
    }
}</pre>


				<pre>var <span id="ArrayMinM">ArrayMinM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_ARRAY_SIZE_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field required at least %v items&#34;, field, min),
        <a href="index.html#IValidMessage.Data">Data</a>:    min,
    }
}</pre>


				<pre>var <span id="ArraySizeM">ArraySizeM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_ARRAY_SIZE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must contain %v item(s)&#34;, field, size),
        <a href="index.html#IValidMessage.Data">Data</a>:    size,
    }
}</pre>


				<pre>var <span id="Base64M">Base64M</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;BASE64&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; must be base64 format&#34;,
    }
}</pre>


				<pre>var <span id="BooleanM">BooleanM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TYPE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be boolean&#34;,
    }
}</pre>


				<pre>var <span id="DateTimeAfterM">DateTimeAfterM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, after <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_DATE_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The &#34;+field+` field must be after &#34;%s&#34;`, after),
    }
}</pre>


				<pre>var <span id="DateTimeBeforeM">DateTimeBeforeM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, before <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_DATE_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The &#34;+field+` field must be before &#34;%s&#34;`, before),
    }
}</pre>


				<pre>var <span id="DateTimeM">DateTimeM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_DATE_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + ` field must be in &#34;yyyy-MM-dd HH:mm:ss&#34; format`,
    }
}</pre>


				<pre>var <span id="EmailM">EmailM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_EMAIL&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be Email Address&#34;, field),
    }
}</pre>


				<pre>var <span id="EmailServiceParserError">EmailServiceParserError</span> = <a href="index.html#Error">Error</a>{
    <a href="index.html#Error.Status">Status</a>:  <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#StatusInternalServerError">StatusInternalServerError</a>,
    <a href="index.html#Error.Code">Code</a>:    &#34;EMAIL_PARSING_ERROR&#34;,
    <a href="index.html#Error.Message">Message</a>: &#34;can not parse email&#34;,
}</pre>


				<pre>var <span id="EmailServiceSendFailed">EmailServiceSendFailed</span> = <a href="index.html#Error">Error</a>{
    <a href="index.html#Error.Status">Status</a>:  <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#StatusBadGateway">StatusBadGateway</a>,
    <a href="index.html#Error.Code">Code</a>:    &#34;SEND_EMAIL_ERROR&#34;,
    <a href="index.html#Error.Message">Message</a>: &#34;can not send email&#34;,
}</pre>


				<pre>var <span id="ExistsM">ExistsM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;NOT_EXISTS&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field&#39;s value is not exists&#34;,
    }
}</pre>


				<pre>var <span id="FloatNumberBetweenM">FloatNumberBetweenM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, max <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be from %v to %v&#34;, field, min, max),
    }
}</pre>


				<pre>var <span id="FloatNumberMaxM">FloatNumberMaxM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MAX&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be less than or equal %v&#34;, field, size),
        <a href="index.html#IValidMessage.Data">Data</a>:    size,
    }
}</pre>


				<pre>var <span id="FloatNumberMinM">FloatNumberMinM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be greater than or equal %v&#34;, field, size),
        <a href="index.html#IValidMessage.Data">Data</a>:    size,
    }
}</pre>


				<pre>var <span id="IPM">IPM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_IP&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be IP Address&#34;, field),
    }
}</pre>


				<pre>var <span id="InM">InM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, rules <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    split := <a href="http://localhost:6060/pkg/strings/">strings</a>.<a href="http://localhost:6060/pkg/strings/#Split">Split</a>(rules, &#34;|&#34;)
    msg := <a href="http://localhost:6060/pkg/strings/">strings</a>.<a href="http://localhost:6060/pkg/strings/#Join">Join</a>(split, &#34;, &#34;)
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_VALUE_NOT_IN_LIST&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be one of &#34; + msg,
        <a href="index.html#IValidMessage.Data">Data</a>:    split,
    }
}</pre>


				<pre>var <span id="JSONArrayM">JSONArrayM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_JSON_ARRAY&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be array format&#34;,
    }
}</pre>


				<pre>var <span id="JSONM">JSONM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_JSON&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be json&#34;,
    }
}</pre>


				<pre>var <span id="JSONObjectEmptyM">JSONObjectEmptyM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_JSON&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field cannot be empty object&#34;,
    }
}</pre>


				<pre>var <span id="JSONObjectM">JSONObjectM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_JSON&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be json object&#34;,
    }
}</pre>


				<pre>var <span id="MQError">MQError</span> = <a href="index.html#Error">Error</a>{
    <a href="index.html#Error.Status">Status</a>:  <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#StatusInternalServerError">StatusInternalServerError</a>,
    <a href="index.html#Error.Code">Code</a>:    &#34;MQ_ERROR&#34;,
    <a href="index.html#Error.Message">Message</a>: &#34;mq internal error&#34;}</pre>


				<pre>var <span id="NumberBetweenM">NumberBetweenM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, max <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be from %v to %v&#34;, field, min, max),
    }
}</pre>


				<pre>var <span id="NumberM">NumberM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TYPE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field cannot parse to number&#34;,
    }
}</pre>


				<pre>var <span id="NumberMaxM">NumberMaxM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MAX&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be less than or equal %v&#34;, field, size),
        <a href="index.html#IValidMessage.Data">Data</a>:    size,
    }
}</pre>


				<pre>var <span id="NumberMinM">NumberMinM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_NUMBER_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be greater than or equal %v&#34;, field, size),
        <a href="index.html#IValidMessage.Data">Data</a>:    size,
    }
}</pre>


				<pre>var <span id="ObjectEmptyM">ObjectEmptyM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TYPE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field cannot be empty object&#34;,
    }
}</pre>


				<pre>var <span id="RequiredM">RequiredM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;REQUIRED&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field is required&#34;,
    }
}</pre>


				<pre>var <span id="StrMaxM">StrMaxM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, max <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_SIZE_MAX&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must not be longer than %v character(s)&#34;, field, max),
        <a href="index.html#IValidMessage.Data">Data</a>:    max,
    }
}</pre>


				<pre>var <span id="StrMinM">StrMinM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#int">int</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_SIZE_MIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must not be shorter than %v character(s)&#34;, field, min),
        <a href="index.html#IValidMessage.Data">Data</a>:    min,
    }
}</pre>


				<pre>var <span id="StringContainM">StringContainM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_CONTAIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must contain %v&#34;, field, substr),
        <a href="index.html#IValidMessage.Data">Data</a>:    substr,
    }
}</pre>


				<pre>var <span id="StringEndWithM">StringEndWithM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_END_WITH&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must end with %v&#34;, field, substr),
        <a href="index.html#IValidMessage.Data">Data</a>:    substr,
    }
}</pre>


				<pre>var <span id="StringLowercaseM">StringLowercaseM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_LOWERCASE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be lowercase&#34;, field),
    }
}</pre>


				<pre>var <span id="StringM">StringM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TYPE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field must be string&#34;,
    }
}</pre>


				<pre>var <span id="StringNotContainM">StringNotContainM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_NOT_CONTAIN&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must not contain %v&#34;, field, substr),
        <a href="index.html#IValidMessage.Data">Data</a>:    substr,
    }
}</pre>


				<pre>var <span id="StringStartWithM">StringStartWithM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_START_WITH&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must start with %v&#34;, field, substr),
        <a href="index.html#IValidMessage.Data">Data</a>:    substr,
    }
}</pre>


				<pre>var <span id="StringUppercaseM">StringUppercaseM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_STRING_UPPERCASE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The %v field must be uppercase&#34;, field),
    }
}</pre>


				<pre>var <span id="TimeAfterM">TimeAfterM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, after <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The &#34;+field+` field must be after &#34;%s&#34;`, after),
    }
}</pre>


				<pre>var <span id="TimeBeforeM">TimeBeforeM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>, before <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: <a href="http://localhost:6060/pkg/fmt/">fmt</a>.<a href="http://localhost:6060/pkg/fmt/#Sprintf">Sprintf</a>(&#34;The &#34;+field+` field must be before &#34;%s&#34;`, before),
    }
}</pre>


				<pre>var <span id="TimeM">TimeM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_TIME&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + ` field must be in &#34;HH:mm:ss&#34;&#34; format`,
    }
}</pre>


				<pre>var <span id="URLM">URLM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;INVALID_URL&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field is not url&#34;,
    }
}</pre>


				<pre>var <span id="UniqueM">UniqueM</span> = func(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#IValidMessage">IValidMessage</a> {
    return &amp;<a href="index.html#IValidMessage">IValidMessage</a>{
        <a href="index.html#IValidMessage.Name">Name</a>:    field,
        <a href="index.html#IValidMessage.Code">Code</a>:    &#34;UNIQUE&#34;,
        <a href="index.html#IValidMessage.Message">Message</a>: &#34;The &#34; + field + &#34; field&#39;s value already exists&#34;,
    }
}</pre>





			<h2 id="CaptureError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go?s=217:300#L3">CaptureError</a>
				<a class="permalink" href="index.html#CaptureError">&#xb6;</a>


			</h2>
			<pre>func CaptureError(ctx <a href="index.html#IContext">IContext</a>, level sentry.<a href="index.html#Level">Level</a>, err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>







			<h2 id="CaptureErrorEcho">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go?s=3375:3445#L126">CaptureErrorEcho</a>
				<a class="permalink" href="index.html#CaptureErrorEcho">&#xb6;</a>


			</h2>
			<pre>func CaptureErrorEcho(ctx echo.<a href="index.html#Context">Context</a>, level sentry.<a href="index.html#Level">Level</a>, err <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>







			<h2 id="CaptureHTTPError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go?s=1407:1498#L49">CaptureHTTPError</a>
				<a class="permalink" href="index.html#CaptureHTTPError">&#xb6;</a>


			</h2>
			<pre>func CaptureHTTPError(ctx <a href="index.html#IHTTPContext">IHTTPContext</a>, level sentry.<a href="index.html#Level">Level</a>, err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>







			<h2 id="CaptureSimpleError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go?s=2945:3020#L109">CaptureSimpleError</a>
				<a class="permalink" href="index.html#CaptureSimpleError">&#xb6;</a>


			</h2>
			<pre>func CaptureSimpleError(level sentry.<a href="index.html#Level">Level</a>, err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>







			<h2 id="Core">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_core.go?s=63:146#L1">Core</a>
				<a class="permalink" href="index.html#Core">&#xb6;</a>


			</h2>
			<pre>func Core(options *<a href="index.html#HTTPContextOptions">HTTPContextOptions</a>) func(next echo.<a href="index.html#HandlerFunc">HandlerFunc</a>) echo.<a href="index.html#HandlerFunc">HandlerFunc</a></pre>







			<h2 id="Crash">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=1044:1071#L50">Crash</a>
				<a class="permalink" href="index.html#Crash">&#xb6;</a>


			</h2>
			<pre>func Crash(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>







			<h2 id="ErrorToJson">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=933:983#L42">ErrorToJson</a>
				<a class="permalink" href="index.html#ErrorToJson">&#xb6;</a>


			</h2>
			<pre>func ErrorToJson(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>) (m map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]jsonErr)</pre>







			<h2 id="Fake">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/faker.go?s=63:93#L1">Fake</a>
				<a class="permalink" href="index.html#Fake">&#xb6;</a>


			</h2>
			<pre>func Fake(a interface{}) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>







			<h2 id="GetBodyString">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/sentry.go?s=2780:2821#L100">GetBodyString</a>
				<a class="permalink" href="index.html#GetBodyString">&#xb6;</a>


			</h2>
			<pre>func GetBodyString(c echo.<a href="index.html#Context">Context</a>) []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a></pre>







			<h2 id="HTTPMiddlewareCore">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_cors.go?s=119:191#L1">HTTPMiddlewareCore</a>
				<a class="permalink" href="index.html#HTTPMiddlewareCore">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareCore(options *<a href="index.html#HTTPContextOptions">HTTPContextOptions</a>) echo.<a href="index.html#MiddlewareFunc">MiddlewareFunc</a></pre>







			<h2 id="HTTPMiddlewareCreateLogger">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_logger.go?s=56:127#L1">HTTPMiddlewareCreateLogger</a>
				<a class="permalink" href="index.html#HTTPMiddlewareCreateLogger">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareCreateLogger(next echo.<a href="index.html#HandlerFunc">HandlerFunc</a>) echo.<a href="index.html#HandlerFunc">HandlerFunc</a></pre>







			<h2 id="HTTPMiddlewareFromCache">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_with_cache.go?s=120:199#L1">HTTPMiddlewareFromCache</a>
				<a class="permalink" href="index.html#HTTPMiddlewareFromCache">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareFromCache(key func(<a href="index.html#IHTTPContext">IHTTPContext</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a>) echo.<a href="index.html#MiddlewareFunc">MiddlewareFunc</a></pre>







			<h2 id="HTTPMiddlewareHandleError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_error.go?s=1095:1157#L40">HTTPMiddlewareHandleError</a>
				<a class="permalink" href="index.html#HTTPMiddlewareHandleError">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareHandleError(env <a href="index.html#IENV">IENV</a>) echo.<a href="index.html#HTTPErrorHandler">HTTPErrorHandler</a></pre>







			<h2 id="HTTPMiddlewareHandleNotFound">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_error.go?s=1447:1502#L53">HTTPMiddlewareHandleNotFound</a>
				<a class="permalink" href="index.html#HTTPMiddlewareHandleNotFound">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareHandleNotFound(c echo.<a href="index.html#Context">Context</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>







			<h2 id="HTTPMiddlewareRateLimit">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_rate_limit.go?s=128:205#L1">HTTPMiddlewareRateLimit</a>
				<a class="permalink" href="index.html#HTTPMiddlewareRateLimit">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareRateLimit(options *<a href="index.html#HTTPContextOptions">HTTPContextOptions</a>) echo.<a href="index.html#MiddlewareFunc">MiddlewareFunc</a></pre>







			<h2 id="HTTPMiddlewareRecoverWithConfig">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_error.go?s=127:226#L1">HTTPMiddlewareRecoverWithConfig</a>
				<a class="permalink" href="index.html#HTTPMiddlewareRecoverWithConfig">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareRecoverWithConfig(env <a href="index.html#IENV">IENV</a>, config <a href="http://localhost:6060/pkg/github.com/labstack/echo/v4/middleware/">middleware</a>.<a href="http://localhost:6060/pkg/github.com/labstack/echo/v4/middleware/#RecoverConfig">RecoverConfig</a>) echo.<a href="index.html#MiddlewareFunc">MiddlewareFunc</a></pre>







			<h2 id="HTTPMiddlewareRequestID">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_request_id.go?s=323:373#L10">HTTPMiddlewareRequestID</a>
				<a class="permalink" href="index.html#HTTPMiddlewareRequestID">&#xb6;</a>


			</h2>
			<pre>func HTTPMiddlewareRequestID() echo.<a href="index.html#MiddlewareFunc">MiddlewareFunc</a></pre>







			<h2 id="IsError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=974:1002#L39">IsError</a>
				<a class="permalink" href="index.html#IsError">&#xb6;</a>


			</h2>
			<pre>func IsError(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>) <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>
			<p>IsError returns true if given error is validate error







			<h2 id="MockMiddleware">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_mock.go?s=394:509#L8">MockMiddleware</a>
				<a class="permalink" href="index.html#MockMiddleware">&#xb6;</a>


			</h2>
			<pre>func MockMiddleware(model interface{}, options *<a href="index.html#MockMiddlewareOptions">MockMiddlewareOptions</a>) func(next echo.<a href="index.html#HandlerFunc">HandlerFunc</a>) echo.<a href="index.html#HandlerFunc">HandlerFunc</a></pre>







			<h2 id="NewHTTPServer">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_server.go?s=220:278#L5">NewHTTPServer</a>
				<a class="permalink" href="index.html#NewHTTPServer">&#xb6;</a>


			</h2>
			<pre>func NewHTTPServer(options *<a href="index.html#HTTPContextOptions">HTTPContextOptions</a>) *echo.<a href="index.html#Echo">Echo</a></pre>







			<h2 id="NewMockENV">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env_mock.go?s=101:127#L1">NewMockENV</a>
				<a class="permalink" href="index.html#NewMockENV">&#xb6;</a>


			</h2>
			<pre>func NewMockENV() *mockENV</pre>







			<h2 id="Recover">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=949:979#L44">Recover</a>
				<a class="permalink" href="index.html#Recover">&#xb6;</a>


			</h2>
			<pre>func Recover(textError <a href="http://localhost:6060/pkg/builtin/#string">string</a>)</pre>







			<h2 id="RequesterToStructPagination">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=10290:10431#L402">RequesterToStructPagination</a>
				<a class="permalink" href="index.html#RequesterToStructPagination">&#xb6;</a>


			</h2>
			<pre>func RequesterToStructPagination(items interface{}, options *<a href="index.html#PageOptions">PageOptions</a>, requester func() (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="index.html#IError">IError</a>)</pre>







			<h2 id="SetSearch">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=4747:4826#L205">SetSearch</a>
				<a class="permalink" href="index.html#SetSearch">&#xb6;</a>


			</h2>
			<pre>func SetSearch(db *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>, keywordCondition *<a href="index.html#KeywordConditionWrapper">KeywordConditionWrapper</a>) *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a></pre>







			<h2 id="SetSearchSimple">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=4876:4946#L209">SetSearchSimple</a>
				<a class="permalink" href="index.html#SetSearchSimple">&#xb6;</a>


			</h2>
			<pre>func SetSearchSimple(db *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>, q <a href="http://localhost:6060/pkg/builtin/#string">string</a>, columns []<a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a></pre>







			<h2 id="StartHTTPServer">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_server.go?s=1277:1321#L39">StartHTTPServer</a>
				<a class="permalink" href="index.html#StartHTTPServer">&#xb6;</a>


			</h2>
			<pre>func StartHTTPServer(e *echo.<a href="index.html#Echo">Echo</a>, env <a href="index.html#IENV">IENV</a>)</pre>







			<h2 id="WithHTTPContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=1651:1703#L56">WithHTTPContext</a>
				<a class="permalink" href="index.html#WithHTTPContext">&#xb6;</a>


			</h2>
			<pre>func WithHTTPContext(h <a href="index.html#HandlerFunc">HandlerFunc</a>) echo.<a href="index.html#HandlerFunc">HandlerFunc</a></pre>








			<h2 id="ABCIContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=264:303#L5">ABCIContext</a>
				<a class="permalink" href="index.html#ABCIContext">&#xb6;</a>


			</h2>

			<pre>type ABCIContext struct {
    <a href="index.html#IContext">IContext</a>
}
</pre>













				<h3 id="ABCIContext.GetMessageJSON">func (ABCIContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=721:774#L24">GetMessageJSON</a>
					<a class="permalink" href="index.html#ABCIContext.GetMessageJSON">&#xb6;</a>


				</h3>
				<pre>func (A <a href="index.html#ABCIContext">ABCIContext</a>) GetMessageJSON(tx []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="ABCIContext.GetOperation">func (ABCIContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=573:624#L19">GetOperation</a>
					<a class="permalink" href="index.html#ABCIContext.GetOperation">&#xb6;</a>


				</h3>
				<pre>func (A <a href="index.html#ABCIContext">ABCIContext</a>) GetOperation(tx []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>








			<h2 id="ABCIContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=307:375#L9">ABCIContextOptions</a>
				<a class="permalink" href="index.html#ABCIContextOptions">&#xb6;</a>


			</h2>

			<pre>type ABCIContextOptions struct {
<span id="ABCIContextOptions.ContextOptions"></span>    ContextOptions *<a href="index.html#ContextOptions">ContextOptions</a>
}
</pre>















			<h2 id="ArchiveByteBody">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/archiver.go?s=470:530#L15">ArchiveByteBody</a>
				<a class="permalink" href="index.html#ArchiveByteBody">&#xb6;</a>


			</h2>

			<pre>type ArchiveByteBody struct {
<span id="ArchiveByteBody.File"></span>    File []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>
<span id="ArchiveByteBody.Name"></span>    Name <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>















			<h2 id="ArchiverOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/archiver.go?s=434:466#L12">ArchiverOptions</a>
				<a class="permalink" href="index.html#ArchiverOptions">&#xb6;</a>


			</h2>

			<pre>type ArchiverOptions struct {
}
</pre>















			<h2 id="BaseValidator">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=505:573#L19">BaseValidator</a>
				<a class="permalink" href="index.html#BaseValidator">&#xb6;</a>


			</h2>

			<pre>type BaseValidator struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="BaseValidator.AddValidator">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=1592:1644#L84">AddValidator</a>
					<a class="permalink" href="index.html#BaseValidator.AddValidator">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) AddValidator(errs ...*<a href="index.html#Valid">Valid</a>)</pre>






				<h3 id="BaseValidator.Error">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=577:615#L24">Error</a>
					<a class="permalink" href="index.html#BaseValidator.Error">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) Error() <a href="index.html#IError">IError</a></pre>






				<h3 id="BaseValidator.GetValidator">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=711:756#L32">GetValidator</a>
					<a class="permalink" href="index.html#BaseValidator.GetValidator">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) GetValidator() *<a href="index.html#Valid">Valid</a></pre>






				<h3 id="BaseValidator.IsArrayBetween">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=18851:18967#L835">IsArrayBetween</a>
					<a class="permalink" href="index.html#BaseValidator.IsArrayBetween">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsArrayBetween(array interface{}, min <a href="http://localhost:6060/pkg/builtin/#int">int</a>, max <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsArrayMax">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=18456:18560#L817">IsArrayMax</a>
					<a class="permalink" href="index.html#BaseValidator.IsArrayMax">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsArrayMax(array interface{}, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsArrayMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=18035:18139#L798">IsArrayMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsArrayMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsArrayMin(array interface{}, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsArraySize">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=17620:17725#L779">IsArraySize</a>
					<a class="permalink" href="index.html#BaseValidator.IsArraySize">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsArraySize(array interface{}, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsBase64">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=6646:6734#L310">IsBase64</a>
					<a class="permalink" href="index.html#BaseValidator.IsBase64">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsBase64(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsBoolRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=20755:20853#L913">IsBoolRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsBoolRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsBoolRequired(value interface{}, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsCustom">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=20983:21080#L922">IsCustom</a>
					<a class="permalink" href="index.html#BaseValidator.IsCustom">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsCustom(customFunc func() (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsDateTime">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=13772:13862#L613">IsDateTime</a>
					<a class="permalink" href="index.html#BaseValidator.IsDateTime">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsDateTime(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsDateTimeAfter">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=14037:14147#L625">IsDateTimeAfter</a>
					<a class="permalink" href="index.html#BaseValidator.IsDateTimeAfter">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsDateTimeAfter(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, after *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsDateTimeBefore">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=14662:14774#L651">IsDateTimeBefore</a>
					<a class="permalink" href="index.html#BaseValidator.IsDateTimeBefore">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsDateTimeBefore(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, before *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsEmail">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=19683:19770#L871">IsEmail</a>
					<a class="permalink" href="index.html#BaseValidator.IsEmail">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsEmail(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsExists">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=9152:9283#L424">IsExists</a>
					<a class="permalink" href="index.html#BaseValidator.IsExists">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsExists(ctx <a href="index.html#IContext">IContext</a>, field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, table <a href="http://localhost:6060/pkg/builtin/#string">string</a>, column <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsExistsWithCondition">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=8387:8547#L382">IsExistsWithCondition</a>
					<a class="permalink" href="index.html#BaseValidator.IsExistsWithCondition">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsExistsWithCondition(
    ctx <a href="index.html#IContext">IContext</a>,
    table <a href="http://localhost:6060/pkg/builtin/#string">string</a>,
    condition map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{},
    fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>,
) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsFloatNumberBetween">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=4651:4778#L222">IsFloatNumberBetween</a>
					<a class="permalink" href="index.html#BaseValidator.IsFloatNumberBetween">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsFloatNumberBetween(field *<a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, min <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, max <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsFloatNumberMax">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=5211:5321#L246">IsFloatNumberMax</a>
					<a class="permalink" href="index.html#BaseValidator.IsFloatNumberMax">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsFloatNumberMax(field *<a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, max <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsFloatNumberMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=4952:5062#L234">IsFloatNumberMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsFloatNumberMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsFloatNumberMin(field *<a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, min <a href="http://localhost:6060/pkg/builtin/#float64">float64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsIP">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=19444:19528#L859">IsIP</a>
					<a class="permalink" href="index.html#BaseValidator.IsIP">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsIP(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONArray">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=10372:10472#L472">IsJSONArray</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONArray">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONArray(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONArrayMax">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=10982:11094#L498">IsJSONArrayMax</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONArrayMax">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONArrayMax(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, max <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONArrayMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=10635:10747#L481">IsJSONArrayMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONArrayMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONArrayMin(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, min <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONBoolPathRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=12324:12447#L554">IsJSONBoolPathRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONBoolPathRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONBoolPathRequired(json *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONObject">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=9769:9870#L449">IsJSONObject</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONObject">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONObject(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONObjectNotEmpty">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=10019:10128#L458">IsJSONObjectNotEmpty</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONObjectNotEmpty">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONObjectNotEmpty(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONObjectPath">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=11329:11443#L515">IsJSONObjectPath</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONObjectPath">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONObjectPath(j *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONPathRequireNotEmpty">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=13183:13306#L589">IsJSONPathRequireNotEmpty</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONPathRequireNotEmpty">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONPathRequireNotEmpty(j *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONPathRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=12751:12867#L571">IsJSONPathRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONPathRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONPathRequired(j *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONPathStrIn">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=19929:20059#L882">IsJSONPathStrIn</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONPathStrIn">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONPathStrIn(json *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, rules <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=9557:9660#L441">IsJSONRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONRequired(field *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsJSONStrPathRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=11831:11953#L534">IsJSONStrPathRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsJSONStrPathRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsJSONStrPathRequired(json *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>, path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsMongoExistsWithCondition">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=8788:8939#L404">IsMongoExistsWithCondition</a>
					<a class="permalink" href="index.html#BaseValidator.IsMongoExistsWithCondition">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsMongoExistsWithCondition(
    ctx <a href="index.html#IContext">IContext</a>,
    table <a href="http://localhost:6060/pkg/builtin/#string">string</a>,
    filter interface{},
    fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>,
) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsMongoStrUnique">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=8094:8223#L372">IsMongoStrUnique</a>
					<a class="permalink" href="index.html#BaseValidator.IsMongoStrUnique">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsMongoStrUnique(ctx <a href="index.html#IContext">IContext</a>, table <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsNotEmptyObjectRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=20467:20575#L904">IsNotEmptyObjectRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsNotEmptyObjectRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsNotEmptyObjectRequired(value interface{}, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsNumberBetween">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=5470:5586#L258">IsNumberBetween</a>
					<a class="permalink" href="index.html#BaseValidator.IsNumberBetween">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsNumberBetween(field *<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, min <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, max <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsNumberMax">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=6000:6101#L282">IsNumberMax</a>
					<a class="permalink" href="index.html#BaseValidator.IsNumberMax">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsNumberMax(field *<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, max <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsNumberMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=5755:5856#L270">IsNumberMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsNumberMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsNumberMin(field *<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, min <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=7332:7426#L340">IsRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsRequired(field interface{}, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsRequiredArray">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=6897:6996#L322">IsRequiredArray</a>
					<a class="permalink" href="index.html#BaseValidator.IsRequiredArray">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsRequiredArray(array interface{}, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStrIn">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=16848:16949#L744">IsStrIn</a>
					<a class="permalink" href="index.html#BaseValidator.IsStrIn">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStrIn(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, rules <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStrMax">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=17112:17210#L755">IsStrMax</a>
					<a class="permalink" href="index.html#BaseValidator.IsStrMax">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStrMax(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStrMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=17366:17464#L767">IsStrMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsStrMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStrMin(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, size <a href="http://localhost:6060/pkg/builtin/#int">int</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStrRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=6245:6338#L294">IsStrRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsStrRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStrRequired(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStrUnique">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=7617:7766#L352">IsStrUnique</a>
					<a class="permalink" href="index.html#BaseValidator.IsStrUnique">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStrUnique(ctx <a href="index.html#IContext">IContext</a>, field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, table <a href="http://localhost:6060/pkg/builtin/#string">string</a>, column <a href="http://localhost:6060/pkg/builtin/#string">string</a>, except <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringContain">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=2730:2840#L144">IsStringContain</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringContain">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringContain(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringEndWith">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=3715:3825#L183">IsStringEndWith</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringEndWith">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringEndWith(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringLowercase">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=4041:4138#L196">IsStringLowercase</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringLowercase">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringLowercase(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringNotContain">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=3055:3168#L157">IsStringNotContain</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringNotContain">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringNotContain(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringNumber">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=2113:2207#L114">IsStringNumber</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringNumber">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringNumber(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringNumberMin">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=2384:2492#L127">IsStringNumberMin</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringNumberMin">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringNumberMin(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, min <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringStartWith">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=3385:3497#L170">IsStringStartWith</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringStartWith">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringStartWith(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, substr <a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsStringUppercase">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=4346:4443#L209">IsStringUppercase</a>
					<a class="permalink" href="index.html#BaseValidator.IsStringUppercase">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsStringUppercase(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsTime">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=16563:16649#L730">IsTime</a>
					<a class="permalink" href="index.html#BaseValidator.IsTime">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsTime(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsTimeAfter">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=15299:15405#L677">IsTimeAfter</a>
					<a class="permalink" href="index.html#BaseValidator.IsTimeAfter">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsTimeAfter(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, after *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsTimeBefore">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=15926:16034#L704">IsTimeBefore</a>
					<a class="permalink" href="index.html#BaseValidator.IsTimeBefore">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsTimeBefore(input *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, before *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsTimeRequired">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=6457:6554#L302">IsTimeRequired</a>
					<a class="permalink" href="index.html#BaseValidator.IsTimeRequired">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsTimeRequired(field *<a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Time">Time</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.IsURL">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=19179:19264#L847">IsURL</a>
					<a class="permalink" href="index.html#BaseValidator.IsURL">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) IsURL(field *<a href="http://localhost:6060/pkg/builtin/#string">string</a>, fieldPath <a href="http://localhost:6060/pkg/builtin/#string">string</a>) (<a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, *<a href="index.html#IValidMessage">IValidMessage</a>)</pre>






				<h3 id="BaseValidator.LoopJSONArray">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=1867:1938#L101">LoopJSONArray</a>
					<a class="permalink" href="index.html#BaseValidator.LoopJSONArray">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) LoopJSONArray(j *<a href="http://localhost:6060/pkg/encoding/json/">json</a>.<a href="http://localhost:6060/pkg/encoding/json/#RawMessage">RawMessage</a>) []interface{}</pre>






				<h3 id="BaseValidator.Merge">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=1350:1402#L68">Merge</a>
					<a class="permalink" href="index.html#BaseValidator.Merge">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) Merge(errs ...*<a href="index.html#Valid">Valid</a>) <a href="index.html#IError">IError</a></pre>






				<h3 id="BaseValidator.Must">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=1112:1181#L56">Must</a>
					<a class="permalink" href="index.html#BaseValidator.Must">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) Must(condition <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, msg *<a href="index.html#IValidMessage">IValidMessage</a>) <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>






				<h3 id="BaseValidator.SetPrefix">func (*BaseValidator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=786:834#L36">SetPrefix</a>
					<a class="permalink" href="index.html#BaseValidator.SetPrefix">&#xb6;</a>


				</h3>
				<pre>func (b *<a href="index.html#BaseValidator">BaseValidator</a>) SetPrefix(prefix <a href="http://localhost:6060/pkg/builtin/#string">string</a>)</pre>








			<h2 id="ContextMock">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=411:620#L10">ContextMock</a>
				<a class="permalink" href="index.html#ContextMock">&#xb6;</a>


			</h2>

			<pre>type ContextMock struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
<span id="ContextMock.MockRequester"></span>    MockRequester *<a href="index.html#MockRequester">MockRequester</a>
<span id="ContextMock.MockCache"></span>    MockCache     *<a href="index.html#MockCache">MockCache</a>
<span id="ContextMock.MockMQ"></span>    MockMQ        *<a href="index.html#MockMQ">MockMQ</a>
<span id="ContextMock.MockLog"></span>    MockLog       *<a href="index.html#MockLogger">MockLogger</a>
<span id="ContextMock.MockDBMongo"></span>    MockDBMongo   *<a href="index.html#MockMongoDB">MockMongoDB</a>
<span id="ContextMock.MockDB"></span>    MockDB        *<a href="index.html#MockDatabase">MockDatabase</a>
}
</pre>











				<h3 id="NewMockContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=130:164#L1">NewMockContext</a>
					<a class="permalink" href="index.html#NewMockContext">&#xb6;</a>


				</h3>
				<pre>func NewMockContext() *<a href="index.html#ContextMock">ContextMock</a></pre>







				<h3 id="ContextMock.Cache">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=624:660#L20">Cache</a>
					<a class="permalink" href="index.html#ContextMock.Cache">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) Cache() <a href="index.html#ICache">ICache</a></pre>






				<h3 id="ContextMock.Caches">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=807:855#L30">Caches</a>
					<a class="permalink" href="index.html#ContextMock.Caches">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) Caches(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#ICache">ICache</a></pre>






				<h3 id="ContextMock.DB">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1318:1353#L55">DB</a>
					<a class="permalink" href="index.html#ContextMock.DB">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) DB() *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a></pre>






				<h3 id="ContextMock.DBMongo">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1528:1568#L65">DBMongo</a>
					<a class="permalink" href="index.html#ContextMock.DBMongo">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) DBMongo() <a href="index.html#IMongoDB">IMongoDB</a></pre>






				<h3 id="ContextMock.DBS">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1415:1462#L60">DBS</a>
					<a class="permalink" href="index.html#ContextMock.DBS">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) DBS(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a></pre>






				<h3 id="ContextMock.DBSMongo">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1630:1682#L70">DBSMongo</a>
					<a class="permalink" href="index.html#ContextMock.DBSMongo">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) DBSMongo(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IMongoDB">IMongoDB</a></pre>






				<h3 id="ContextMock.ENV">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1916:1948#L80">ENV</a>
					<a class="permalink" href="index.html#ContextMock.ENV">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) ENV() <a href="index.html#IENV">IENV</a></pre>






				<h3 id="ContextMock.Log">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1127:1162#L45">Log</a>
					<a class="permalink" href="index.html#ContextMock.Log">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) Log() <a href="index.html#ILogger">ILogger</a></pre>
				<p>Log return the logger






				<h3 id="ContextMock.MQ">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=720:750#L25">MQ</a>
					<a class="permalink" href="index.html#ContextMock.MQ">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) MQ() <a href="index.html#IMQ">IMQ</a></pre>






				<h3 id="ContextMock.NewError">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1748:1835#L75">NewError</a>
					<a class="permalink" href="index.html#ContextMock.NewError">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) NewError(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, errorType <a href="index.html#IError">IError</a>, args ...interface{}) <a href="index.html#IError">IError</a></pre>






				<h3 id="ContextMock.Requester">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=919:963#L35">Requester</a>
					<a class="permalink" href="index.html#ContextMock.Requester">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) Requester() <a href="index.html#IRequester">IRequester</a></pre>






				<h3 id="ContextMock.SetType">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1027:1078#L40">SetType</a>
					<a class="permalink" href="index.html#ContextMock.SetType">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) SetType(t <a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/">consts</a>.<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/#ContextType">ContextType</a>)</pre>






				<h3 id="ContextMock.Type">func (*ContextMock) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context_mock.go?s=1223:1258#L50">Type</a>
					<a class="permalink" href="index.html#ContextMock.Type">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#ContextMock">ContextMock</a>) Type() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>








			<h2 id="ContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context.go?s=1053:1358#L31">ContextOptions</a>
				<a class="permalink" href="index.html#ContextOptions">&#xb6;</a>


			</h2>

			<pre>type ContextOptions struct {
<span id="ContextOptions.DB"></span>    DB       *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>
<span id="ContextOptions.DBS"></span>    DBS      map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]*<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>
<span id="ContextOptions.MongoDB"></span>    MongoDB  <a href="index.html#IMongoDB">IMongoDB</a>
<span id="ContextOptions.MongoDBS"></span>    MongoDBS map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="index.html#IMongoDB">IMongoDB</a>
<span id="ContextOptions.Cache"></span>    Cache    <a href="index.html#ICache">ICache</a>
<span id="ContextOptions.Caches"></span>    Caches   map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="index.html#ICache">ICache</a>
<span id="ContextOptions.ENV"></span>    ENV      <a href="index.html#IENV">IENV</a>
<span id="ContextOptions.MQ"></span>    MQ       <a href="index.html#IMQ">IMQ</a>

<span id="ContextOptions.DATA"></span>    DATA map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>















			<h2 id="ContextUser">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context.go?s=177:529#L2">ContextUser</a>
				<a class="permalink" href="index.html#ContextUser">&#xb6;</a>


			</h2>

			<pre>type ContextUser struct {
<span id="ContextUser.ID"></span>    ID       <a href="http://localhost:6060/pkg/builtin/#string">string</a>            `json:&#34;id,omitempty&#34;`
<span id="ContextUser.Email"></span>    Email    <a href="http://localhost:6060/pkg/builtin/#string">string</a>            `json:&#34;email,omitempty&#34;`
<span id="ContextUser.Username"></span>    Username <a href="http://localhost:6060/pkg/builtin/#string">string</a>            `json:&#34;username,omitempty&#34;`
<span id="ContextUser.Name"></span>    Name     <a href="http://localhost:6060/pkg/builtin/#string">string</a>            `json:&#34;name,omitempty&#34;`
<span id="ContextUser.Segment"></span>    Segment  <a href="http://localhost:6060/pkg/builtin/#string">string</a>            `json:&#34;segment,omitempty&#34;`
<span id="ContextUser.Data"></span>    Data     map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="http://localhost:6060/pkg/builtin/#string">string</a> `json:&#34;data,omitempty&#34;`
}
</pre>















			<h2 id="CronjobContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=437:504#L13">CronjobContext</a>
				<a class="permalink" href="index.html#CronjobContext">&#xb6;</a>


			</h2>

			<pre>type CronjobContext struct {
    <a href="index.html#IContext">IContext</a>
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="CronjobContext.AddJob">func (CronjobContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=508:606#L18">AddJob</a>
					<a class="permalink" href="index.html#CronjobContext.AddJob">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#CronjobContext">CronjobContext</a>) AddJob(job *<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/">gocron</a>.<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/#Scheduler">Scheduler</a>, handlerFunc func(ctx <a href="index.html#ICronjobContext">ICronjobContext</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="CronjobContext.Job">func (CronjobContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=1040:1087#L44">Job</a>
					<a class="permalink" href="index.html#CronjobContext.Job">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#CronjobContext">CronjobContext</a>) Job() *<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/">gocron</a>.<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/#Scheduler">Scheduler</a></pre>






				<h3 id="CronjobContext.Start">func (CronjobContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=975:1006#L40">Start</a>
					<a class="permalink" href="index.html#CronjobContext.Start">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#CronjobContext">CronjobContext</a>) Start()</pre>








			<h2 id="CronjobContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=1112:1215#L48">CronjobContextOptions</a>
				<a class="permalink" href="index.html#CronjobContextOptions">&#xb6;</a>


			</h2>

			<pre>type CronjobContextOptions struct {
<span id="CronjobContextOptions.ContextOptions"></span>    ContextOptions *<a href="index.html#ContextOptions">ContextOptions</a>
<span id="CronjobContextOptions.TimeLocation"></span>    TimeLocation   *<a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Location">Location</a>
}
</pre>















			<h2 id="Database">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=760:917#L35">Database</a>
				<a class="permalink" href="index.html#Database">&#xb6;</a>


			</h2>

			<pre>type Database struct {
<span id="Database.Driver"></span>    Driver   <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Database.Name"></span>    Name     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Database.Host"></span>    Host     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Database.User"></span>    User     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Database.Password"></span>    Password <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Database.Port"></span>    Port     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>











				<h3 id="NewDatabase">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=921:963#L45">NewDatabase</a>
					<a class="permalink" href="index.html#NewDatabase">&#xb6;</a>


				</h3>
				<pre>func NewDatabase(env *<a href="index.html#ENVConfig">ENVConfig</a>) *<a href="index.html#Database">Database</a></pre>





				<h3 id="NewDatabaseWithConfig">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=1181:1254#L57">NewDatabaseWithConfig</a>
					<a class="permalink" href="index.html#NewDatabaseWithConfig">&#xb6;</a>


				</h3>
				<pre>func NewDatabaseWithConfig(env *<a href="index.html#ENVConfig">ENVConfig</a>, config *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#Config">Config</a>) *<a href="index.html#Database">Database</a></pre>







				<h3 id="Database.Connect">func (*Database) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=1496:1543#L70">Connect</a>
					<a class="permalink" href="index.html#Database.Connect">&#xb6;</a>


				</h3>
				<pre>func (db *<a href="index.html#Database">Database</a>) Connect() (*<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>Connect to connect Database








			<h2 id="DatabaseCache">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache.go?s=431:489#L10">DatabaseCache</a>
				<a class="permalink" href="index.html#DatabaseCache">&#xb6;</a>


			</h2>

			<pre>type DatabaseCache struct {
<span id="DatabaseCache.Host"></span>    Host <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="DatabaseCache.Port"></span>    Port <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>











				<h3 id="NewCache">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache.go?s=573:617#L21">NewCache</a>
					<a class="permalink" href="index.html#NewCache">&#xb6;</a>


				</h3>
				<pre>func NewCache(env *<a href="index.html#ENVConfig">ENVConfig</a>) *<a href="index.html#DatabaseCache">DatabaseCache</a></pre>







				<h3 id="DatabaseCache.Connect">func (DatabaseCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache.go?s=703:751#L28">Connect</a>
					<a class="permalink" href="index.html#DatabaseCache.Connect">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#DatabaseCache">DatabaseCache</a>) Connect() (<a href="index.html#ICache">ICache</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="DatabaseMongo">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=325:445#L10">DatabaseMongo</a>
				<a class="permalink" href="index.html#DatabaseMongo">&#xb6;</a>


			</h2>

			<pre>type DatabaseMongo struct {
<span id="DatabaseMongo.Name"></span>    Name     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="DatabaseMongo.Host"></span>    Host     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="DatabaseMongo.UserName"></span>    UserName <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="DatabaseMongo.Password"></span>    Password <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="DatabaseMongo.Port"></span>    Port     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>











				<h3 id="NewDatabaseMongo">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=745:797#L28">NewDatabaseMongo</a>
					<a class="permalink" href="index.html#NewDatabaseMongo">&#xb6;</a>


				</h3>
				<pre>func NewDatabaseMongo(env *<a href="index.html#ENVConfig">ENVConfig</a>) *<a href="index.html#DatabaseMongo">DatabaseMongo</a></pre>







				<h3 id="DatabaseMongo.Connect">func (*DatabaseMongo) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=1025:1077#L39">Connect</a>
					<a class="permalink" href="index.html#DatabaseMongo.Connect">&#xb6;</a>


				</h3>
				<pre>func (db *<a href="index.html#DatabaseMongo">DatabaseMongo</a>) Connect() (<a href="index.html#IMongoDB">IMongoDB</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>Connect to connect Database








			<h2 id="E2EContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/e2e_context.go?s=122:160#L1">E2EContext</a>
				<a class="permalink" href="index.html#E2EContext">&#xb6;</a>


			</h2>

			<pre>type E2EContext struct {
    <a href="index.html#IContext">IContext</a>
}
</pre>















			<h2 id="E2EContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/e2e_context.go?s=164:231#L5">E2EContextOptions</a>
				<a class="permalink" href="index.html#E2EContextOptions">&#xb6;</a>


			</h2>

			<pre>type E2EContextOptions struct {
<span id="E2EContextOptions.ContextOptions"></span>    ContextOptions *<a href="index.html#ContextOptions">ContextOptions</a>
}
</pre>















			<h2 id="ENVConfig">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=397:2470#L17">ENVConfig</a>
				<a class="permalink" href="index.html#ENVConfig">&#xb6;</a>


			</h2>

			<pre>type ENVConfig struct {
<span id="ENVConfig.LogLevel"></span>    LogLevel <a href="http://localhost:6060/pkg/github.com/sirupsen/logrus/">logrus</a>.<a href="http://localhost:6060/pkg/github.com/sirupsen/logrus/#Level">Level</a>
<span id="ENVConfig.LogHost"></span>    LogHost  <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;log_host&#34;`
<span id="ENVConfig.LogPort"></span>    LogPort  <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;log_port&#34;`

<span id="ENVConfig.Host"></span>    Host    <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;host&#34;`
<span id="ENVConfig.ENV"></span>    ENV     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;env&#34;`
<span id="ENVConfig.Service"></span>    Service <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;service&#34;`

<span id="ENVConfig.SentryDSN"></span>    SentryDSN <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;sentry_dsn&#34;`

<span id="ENVConfig.DBDriver"></span>    DBDriver   <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_driver&#34;`
<span id="ENVConfig.DBHost"></span>    DBHost     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_host&#34;`
<span id="ENVConfig.DBName"></span>    DBName     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_name&#34;`
<span id="ENVConfig.DBUser"></span>    DBUser     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_user&#34;`
<span id="ENVConfig.DBPassword"></span>    DBPassword <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_password&#34;`
<span id="ENVConfig.DBPort"></span>    DBPort     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_port&#34;`

<span id="ENVConfig.DBMongoHost"></span>    DBMongoHost     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_mongo_host&#34;`
<span id="ENVConfig.DBMongoName"></span>    DBMongoName     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_mongo_name&#34;`
<span id="ENVConfig.DBMongoUserName"></span>    DBMongoUserName <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_mongo_username&#34;`
<span id="ENVConfig.DBMongoPassword"></span>    DBMongoPassword <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_mongo_password&#34;`
<span id="ENVConfig.DBMongoPort"></span>    DBMongoPort     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;db_mongo_port&#34;`

<span id="ENVConfig.MQHost"></span>    MQHost     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;mq_host&#34;`
<span id="ENVConfig.MQUser"></span>    MQUser     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;mq_user&#34;`
<span id="ENVConfig.MQPassword"></span>    MQPassword <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;mq_password&#34;`
<span id="ENVConfig.MQPort"></span>    MQPort     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;mq_port&#34;`

<span id="ENVConfig.CachePort"></span>    CachePort <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;cache_port&#34;`
<span id="ENVConfig.CacheHost"></span>    CacheHost <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;cache_host&#34;`

<span id="ENVConfig.ABCIEndpoint"></span>    ABCIEndpoint      <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;abci_endpoint&#34;`
<span id="ENVConfig.DIDMethodDefault"></span>    DIDMethodDefault  <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;did_method_default&#34;`
<span id="ENVConfig.DIDKeyTypeDefault"></span>    DIDKeyTypeDefault <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;did_key_type_default&#34;`

<span id="ENVConfig.WinRMHost"></span>    WinRMHost     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;winrm_host&#34;`
<span id="ENVConfig.WinRMUser"></span>    WinRMUser     <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;winrm_user&#34;`
<span id="ENVConfig.WinRMPassword"></span>    WinRMPassword <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;winrm_password&#34;`

<span id="ENVConfig.S3Endpoint"></span>    S3Endpoint  <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;s3_endpoint&#34;`
<span id="ENVConfig.S3AccessKey"></span>    S3AccessKey <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;s3_access_key&#34;`
<span id="ENVConfig.S3SecretKey"></span>    S3SecretKey <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;s3_secret_key&#34;`
<span id="ENVConfig.S3Bucket"></span>    S3Bucket    <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;s3_bucket&#34;`
<span id="ENVConfig.S3Region"></span>    S3Region    <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;s3_region&#34;`
<span id="ENVConfig.S3IsHTTPS"></span>    S3IsHTTPS   <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>   `mapstructure:&#34;s3_https&#34;`

<span id="ENVConfig.EmailServer"></span>    EmailServer   <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;email_server&#34;`
<span id="ENVConfig.EmailPort"></span>    EmailPort     <a href="http://localhost:6060/pkg/builtin/#int">int</a>    `mapstructure:&#34;email_port&#34;`
<span id="ENVConfig.EmailUsername"></span>    EmailUsername <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;email_username&#34;`
<span id="ENVConfig.EmailPassword"></span>    EmailPassword <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;email_password&#34;`
<span id="ENVConfig.EmailSender"></span>    EmailSender   <a href="http://localhost:6060/pkg/builtin/#string">string</a> `mapstructure:&#34;email_sender&#34;`
}
</pre>















			<h2 id="ENVType">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=2474:2518#L71">ENVType</a>
				<a class="permalink" href="index.html#ENVType">&#xb6;</a>


			</h2>

			<pre>type ENVType struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="ENVType.All">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4574:4614#L156">All</a>
					<a class="permalink" href="index.html#ENVType.All">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) All() map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="ENVType.Bool">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4297:4335#L145">Bool</a>
					<a class="permalink" href="index.html#ENVType.Bool">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) Bool(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>






				<h3 id="ENVType.Config">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=3834:3870#L122">Config</a>
					<a class="permalink" href="index.html#ENVType.Config">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) Config() *<a href="index.html#ENVConfig">ENVConfig</a></pre>






				<h3 id="ENVType.Int">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4389:4425#L149">Int</a>
					<a class="permalink" href="index.html#ENVType.Int">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) Int(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#int">int</a></pre>






				<h3 id="ENVType.IsDev">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=3929:3958#L127">IsDev</a>
					<a class="permalink" href="index.html#ENVType.IsDev">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) IsDev() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>
				<p>IsDev config  is Dev config






				<h3 id="ENVType.IsMock">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4001:4031#L131">IsMock</a>
					<a class="permalink" href="index.html#ENVType.IsMock">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) IsMock() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>






				<h3 id="ENVType.IsProd">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4223:4253#L141">IsProd</a>
					<a class="permalink" href="index.html#ENVType.IsProd">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) IsProd() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>
				<p>IsProd config  is production config






				<h3 id="ENVType.IsTest">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4109:4139#L136">IsTest</a>
					<a class="permalink" href="index.html#ENVType.IsTest">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) IsTest() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>
				<p>IsTest config  is Test config






				<h3 id="ENVType.String">func (ENVType) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=4478:4520#L153">String</a>
					<a class="permalink" href="index.html#ENVType.String">&#xb6;</a>


				</h3>
				<pre>func (e <a href="index.html#ENVType">ENVType</a>) String(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>








			<h2 id="Error">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=227:490#L7">Error</a>
				<a class="permalink" href="index.html#Error">&#xb6;</a>


			</h2>

			<pre>type Error struct {
<span id="Error.Status"></span>    Status  <a href="http://localhost:6060/pkg/builtin/#int">int</a>         `json:&#34;-&#34;`
<span id="Error.Code"></span>    Code    <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;code&#34;`
<span id="Error.Message"></span>    Message interface{} `json:&#34;message&#34;`
<span id="Error.Data"></span>    Data    interface{} `json:&#34;-&#34;`
<span id="Error.Fields"></span>    Fields  interface{} `json:&#34;fields,omitempty&#34;`
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="Error.Error">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=547:576#L20">Error</a>
					<a class="permalink" href="index.html#Error.Error">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="Error.GetCode">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=651:682#L24">GetCode</a>
					<a class="permalink" href="index.html#Error.GetCode">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) GetCode() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="Error.GetMessage">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=882:921#L40">GetMessage</a>
					<a class="permalink" href="index.html#Error.GetMessage">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) GetMessage() interface{}</pre>






				<h3 id="Error.GetStatus">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=707:737#L28">GetStatus</a>
					<a class="permalink" href="index.html#Error.GetStatus">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) GetStatus() <a href="http://localhost:6060/pkg/builtin/#int">int</a></pre>






				<h3 id="Error.JSON">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=494:527#L16">JSON</a>
					<a class="permalink" href="index.html#Error.JSON">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) JSON() interface{}</pre>






				<h3 id="Error.OriginalError">func (Error) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=764:800#L32">OriginalError</a>
					<a class="permalink" href="index.html#Error.OriginalError">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#Error">Error</a>) OriginalError() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="FieldError">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=132:282#L2">FieldError</a>
				<a class="permalink" href="index.html#FieldError">&#xb6;</a>


			</h2>

			<pre>type FieldError struct {
<span id="FieldError.Code"></span>    Code    <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;code&#34;`
<span id="FieldError.Message"></span>    Message <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;message&#34;`
<span id="FieldError.Fields"></span>    Fields  interface{} `json:&#34;fields,omitempty&#34;`
}
</pre>













				<h3 id="FieldError.Error">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=420:454#L16">Error</a>
					<a class="permalink" href="index.html#FieldError.Error">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="FieldError.GetCode">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=286:322#L8">GetCode</a>
					<a class="permalink" href="index.html#FieldError.GetCode">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) GetCode() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="FieldError.GetMessage">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=712:756#L32">GetMessage</a>
					<a class="permalink" href="index.html#FieldError.GetMessage">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) GetMessage() interface{}</pre>






				<h3 id="FieldError.GetStatus">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=347:380#L12">GetStatus</a>
					<a class="permalink" href="index.html#FieldError.GetStatus">&#xb6;</a>


				</h3>
				<pre>func (<a href="index.html#FieldError">FieldError</a>) GetStatus() <a href="http://localhost:6060/pkg/builtin/#int">int</a></pre>






				<h3 id="FieldError.JSON">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=654:692#L28">JSON</a>
					<a class="permalink" href="index.html#FieldError.JSON">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) JSON() interface{}</pre>






				<h3 id="FieldError.OriginalError">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=496:537#L20">OriginalError</a>
					<a class="permalink" href="index.html#FieldError.OriginalError">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) OriginalError() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="FieldError.OriginalErrorMessage">func (FieldError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=577:626#L24">OriginalErrorMessage</a>
					<a class="permalink" href="index.html#FieldError.OriginalErrorMessage">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#FieldError">FieldError</a>) OriginalErrorMessage() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>








			<h2 id="File">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=1681:1732#L57">File</a>
				<a class="permalink" href="index.html#File">&#xb6;</a>


			</h2>

			<pre>type File struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="File.Name">func (File) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=2233:2260#L83">Name</a>
					<a class="permalink" href="index.html#File.Name">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#File">File</a>) Name() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="File.Value">func (File) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=2285:2313#L87">Value</a>
					<a class="permalink" href="index.html#File.Value">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#File">File</a>) Value() []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a></pre>








			<h2 id="HTTPContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=791:862#L23">HTTPContext</a>
				<a class="permalink" href="index.html#HTTPContext">&#xb6;</a>


			</h2>

			<pre>type HTTPContext struct {
    echo.<a href="index.html#Context">Context</a>
    <a href="index.html#IContext">IContext</a>
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="HTTPContext.BindOnly">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=5809:5861#L214">BindOnly</a>
					<a class="permalink" href="index.html#HTTPContext.BindOnly">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) BindOnly(i interface{}) <a href="index.html#IError">IError</a></pre>






				<h3 id="HTTPContext.BindWithValidate">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=5405:5472#L196">BindWithValidate</a>
					<a class="permalink" href="index.html#HTTPContext.BindWithValidate">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) BindWithValidate(ctx <a href="index.html#IValidateContext">IValidateContext</a>) <a href="index.html#IError">IError</a></pre>






				<h3 id="HTTPContext.BindWithValidateMessage">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=5602:5676#L205">BindWithValidateMessage</a>
					<a class="permalink" href="index.html#HTTPContext.BindWithValidateMessage">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) BindWithValidateMessage(ctx <a href="index.html#IValidateContext">IValidateContext</a>) <a href="index.html#IError">IError</a></pre>






				<h3 id="HTTPContext.GetMessage">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=6056:6097#L226">GetMessage</a>
					<a class="permalink" href="index.html#HTTPContext.GetMessage">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) GetMessage() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="HTTPContext.GetPageOptions">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=1849:1900#L66">GetPageOptions</a>
					<a class="permalink" href="index.html#HTTPContext.GetPageOptions">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) GetPageOptions() *<a href="index.html#PageOptions">PageOptions</a></pre>






				<h3 id="HTTPContext.GetPageOptionsWithOptions">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=3509:3598#L123">GetPageOptionsWithOptions</a>
					<a class="permalink" href="index.html#HTTPContext.GetPageOptionsWithOptions">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) GetPageOptionsWithOptions(options *<a href="index.html#PageOptionsOptions">PageOptionsOptions</a>) *<a href="index.html#PageOptions">PageOptions</a></pre>






				<h3 id="HTTPContext.GetSignature">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=5957:6000#L222">GetSignature</a>
					<a class="permalink" href="index.html#HTTPContext.GetSignature">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) GetSignature() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="HTTPContext.GetUserAgent">func (HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=6277:6334#L237">GetUserAgent</a>
					<a class="permalink" href="index.html#HTTPContext.GetUserAgent">&#xb6;</a>


				</h3>
				<pre>func (c <a href="index.html#HTTPContext">HTTPContext</a>) GetUserAgent() *<a href="http://localhost:6060/pkg/github.com/mssola/user_agent/">user_agent</a>.<a href="http://localhost:6060/pkg/github.com/mssola/user_agent/#UserAgent">UserAgent</a></pre>






				<h3 id="HTTPContext.Log">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=6151:6186#L230">Log</a>
					<a class="permalink" href="index.html#HTTPContext.Log">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) Log() <a href="index.html#ILogger">ILogger</a></pre>






				<h3 id="HTTPContext.NewError">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=6392:6479#L241">NewError</a>
					<a class="permalink" href="index.html#HTTPContext.NewError">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) NewError(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, errorType <a href="index.html#IError">IError</a>, args ...interface{}) <a href="index.html#IError">IError</a></pre>






				<h3 id="HTTPContext.WithSaveCache">func (*HTTPContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=1318:1419#L44">WithSaveCache</a>
					<a class="permalink" href="index.html#HTTPContext.WithSaveCache">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#HTTPContext">HTTPContext</a>) WithSaveCache(data interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, duration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) interface{}</pre>








			<h2 id="HTTPContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=911:1063#L31">HTTPContextOptions</a>
				<a class="permalink" href="index.html#HTTPContextOptions">&#xb6;</a>


			</h2>

			<pre>type HTTPContextOptions struct {
<span id="HTTPContextOptions.RateLimit"></span>    RateLimit      *<a href="http://localhost:6060/pkg/github.com/labstack/echo/v4/middleware/">middleware</a>.<a href="http://localhost:6060/pkg/github.com/labstack/echo/v4/middleware/#RateLimiterMemoryStoreConfig">RateLimiterMemoryStoreConfig</a>
<span id="HTTPContextOptions.AllowOrigins"></span>    AllowOrigins   []<a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="HTTPContextOptions.ContextOptions"></span>    ContextOptions *<a href="index.html#ContextOptions">ContextOptions</a>
}
</pre>















			<h2 id="HandlerFunc">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=866:907#L29">HandlerFunc</a>
				<a class="permalink" href="index.html#HandlerFunc">&#xb6;</a>


			</h2>

			<pre>type HandlerFunc func(<a href="index.html#IHTTPContext">IHTTPContext</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>















			<h2 id="IABCIContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=149:260#L1">IABCIContext</a>
				<a class="permalink" href="index.html#IABCIContext">&#xb6;</a>


			</h2>

			<pre>type IABCIContext interface {
    <a href="index.html#IContext">IContext</a>
    GetOperation(tx []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    GetMessageJSON(tx []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}</pre>











				<h3 id="NewABCIContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/abci_context.go?s=379:440#L13">NewABCIContext</a>
					<a class="permalink" href="index.html#NewABCIContext">&#xb6;</a>


				</h3>
				<pre>func NewABCIContext(options *<a href="index.html#ABCIContextOptions">ABCIContextOptions</a>) <a href="index.html#IABCIContext">IABCIContext</a></pre>









			<h2 id="IArchiver">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/archiver.go?s=219:430#L7">IArchiver</a>
				<a class="permalink" href="index.html#IArchiver">&#xb6;</a>


			</h2>

			<pre>type IArchiver interface {
    FromURLs(fileName <a href="http://localhost:6060/pkg/builtin/#string">string</a>, urls []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#ArchiverOptions">ArchiverOptions</a>) ([]<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>, <a href="index.html#IError">IError</a>)
    FromBytes(fileName <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body []<a href="index.html#ArchiveByteBody">ArchiveByteBody</a>, options *<a href="index.html#ArchiverOptions">ArchiverOptions</a>) ([]<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>, <a href="index.html#IError">IError</a>)
}</pre>











				<h3 id="NewArchiver">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/archiver.go?s=2944:2984#L111">NewArchiver</a>
					<a class="permalink" href="index.html#NewArchiver">&#xb6;</a>


				</h3>
				<pre>func NewArchiver(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IArchiver">IArchiver</a></pre>









			<h2 id="ICSV">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/csv.go?s=199:581#L8">ICSV</a>
				<a class="permalink" href="index.html#ICSV">&#xb6;</a>


			</h2>

			<pre>type ICSV[T <a href="http://localhost:6060/pkg/builtin/#any">any</a>] interface {
    <span class="comment">//Reader</span>
    ReadFromFile(data []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>, options *<a href="index.html#ICSVOptions">ICSVOptions</a>) ([]T, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    ReadFromPath(path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#ICSVOptions">ICSVOptions</a>) ([]T, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    ReadFromString(data <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#ICSVOptions">ICSVOptions</a>) ([]T, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    ReadFromURL(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#ICSVOptions">ICSVOptions</a>) ([]T, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    ReadFromFileMaps(data []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>, options *<a href="index.html#ICSVOptions">ICSVOptions</a>) ([]map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
}</pre>











				<h3 id="NewCSV">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/csv.go?s=631:671#L21">NewCSV</a>
					<a class="permalink" href="index.html#NewCSV">&#xb6;</a>


				</h3>
				<pre>func NewCSV[T <a href="http://localhost:6060/pkg/builtin/#any">any</a>](ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#ICSV">ICSV</a>[T]</pre>









			<h2 id="ICSVOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/csv.go?s=117:195#L3">ICSVOptions</a>
				<a class="permalink" href="index.html#ICSVOptions">&#xb6;</a>


			</h2>

			<pre>type ICSVOptions struct {
<span id="ICSVOptions.FirstRowIsHeader"></span>    FirstRowIsHeader <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="ICSVOptions.Separator"></span>    Separator        <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>















			<h2 id="ICache">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache.go?s=137:427#L1">ICache</a>
				<a class="permalink" href="index.html#ICache">&#xb6;</a>


			</h2>

			<pre>type ICache interface {
    Set(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value interface{}, expiration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    SetJSON(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value interface{}, expiration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Get(dest interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    GetJSON(dest interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Del(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Close()
}</pre>















			<h2 id="IContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context.go?s=531:1049#L10">IContext</a>
				<a class="permalink" href="index.html#IContext">&#xb6;</a>


			</h2>

			<pre>type IContext interface {
    MQ() <a href="index.html#IMQ">IMQ</a>
    DB() *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>
    DBS(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>
    DBMongo() <a href="index.html#IMongoDB">IMongoDB</a>
    DBSMongo(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IMongoDB">IMongoDB</a>
    WinRM() <a href="index.html#IWinRM">IWinRM</a>
    ENV() <a href="index.html#IENV">IENV</a>
    Log() <a href="index.html#ILogger">ILogger</a>
    Type() <a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/">consts</a>.<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/#ContextType">ContextType</a>
    NewError(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>, errorType <a href="index.html#IError">IError</a>, args ...interface{}) <a href="index.html#IError">IError</a>
    Requester() <a href="index.html#IRequester">IRequester</a>
    Cache() <a href="index.html#ICache">ICache</a>
    Caches(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#ICache">ICache</a>
    GetData(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>) interface{}
    GetAllData() map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}
    SetData(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, data interface{})
    SetUser(user *<a href="index.html#ContextUser">ContextUser</a>)
    GetUser() *<a href="index.html#ContextUser">ContextUser</a>
}</pre>











				<h3 id="NewContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/context.go?s=1362:1411#L44">NewContext</a>
					<a class="permalink" href="index.html#NewContext">&#xb6;</a>


				</h3>
				<pre>func NewContext(options *<a href="index.html#ContextOptions">ContextOptions</a>) <a href="index.html#IContext">IContext</a></pre>









			<h2 id="ICronjobContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=274:433#L6">ICronjobContext</a>
				<a class="permalink" href="index.html#ICronjobContext">&#xb6;</a>


			</h2>

			<pre>type ICronjobContext interface {
    <a href="index.html#IContext">IContext</a>
    Job() *<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/">gocron</a>.<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/#Scheduler">Scheduler</a>
    Start()
    AddJob(job *<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/">gocron</a>.<a href="http://localhost:6060/pkg/github.com/go-co-op/gocron/#Scheduler">Scheduler</a>, handlerFunc func(ctx <a href="index.html#ICronjobContext">ICronjobContext</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
}</pre>











				<h3 id="NewCronjobContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cronjob_context.go?s=1219:1289#L53">NewCronjobContext</a>
					<a class="permalink" href="index.html#NewCronjobContext">&#xb6;</a>


				</h3>
				<pre>func NewCronjobContext(options *<a href="index.html#CronjobContextOptions">CronjobContextOptions</a>) <a href="index.html#ICronjobContext">ICronjobContext</a></pre>









			<h2 id="IE2EContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/e2e_context.go?s=76:118#L1">IE2EContext</a>
				<a class="permalink" href="index.html#IE2EContext">&#xb6;</a>


			</h2>

			<pre>type IE2EContext interface {
    <a href="index.html#IContext">IContext</a>
}</pre>











				<h3 id="NewE2EContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/e2e_context.go?s=235:293#L9">NewE2EContext</a>
					<a class="permalink" href="index.html#NewE2EContext">&#xb6;</a>


				</h3>
				<pre>func NewE2EContext(options *<a href="index.html#E2EContextOptions">E2EContextOptions</a>) <a href="index.html#IE2EContext">IE2EContext</a></pre>









			<h2 id="IENV">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=184:393#L5">IENV</a>
				<a class="permalink" href="index.html#IENV">&#xb6;</a>


			</h2>

			<pre>type IENV interface {
    Config() *<a href="index.html#ENVConfig">ENVConfig</a>
    IsDev() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
    IsTest() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
    IsMock() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
    IsProd() <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
    Bool(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
    Int(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#int">int</a>
    String(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    All() map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="http://localhost:6060/pkg/builtin/#string">string</a>
}</pre>











				<h3 id="NewENVPath">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=2574:2607#L79">NewENVPath</a>
					<a class="permalink" href="index.html#NewENVPath">&#xb6;</a>


				</h3>
				<pre>func NewENVPath(path <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IENV">IENV</a></pre>





				<h3 id="NewEnv">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/env.go?s=2522:2540#L75">NewEnv</a>
					<a class="permalink" href="index.html#NewEnv">&#xb6;</a>


				</h3>
				<pre>func NewEnv() <a href="index.html#IENV">IENV</a></pre>









			<h2 id="IEmail">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/email.go?s=411:764#L14">IEmail</a>
				<a class="permalink" href="index.html#IEmail">&#xb6;</a>


			</h2>

			<pre>type IEmail interface {
    SendHTML(from <a href="http://localhost:6060/pkg/builtin/#string">string</a>, to []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, subject <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IError">IError</a>
    SendHTMLWithAttach(from <a href="http://localhost:6060/pkg/builtin/#string">string</a>, to []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, subject <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body <a href="http://localhost:6060/pkg/builtin/#string">string</a>, file []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>, fileName <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IError">IError</a>
    SendText(from <a href="http://localhost:6060/pkg/builtin/#string">string</a>, to []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, subject <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="index.html#IError">IError</a>
    ParseHTMLToString(path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, data interface{}) (<a href="http://localhost:6060/pkg/builtin/#string">string</a>, <a href="index.html#IError">IError</a>)
}</pre>











				<h3 id="NewEmail">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/email.go?s=2777:2811#L99">NewEmail</a>
					<a class="permalink" href="index.html#NewEmail">&#xb6;</a>


				</h3>
				<pre>func NewEmail(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IEmail">IEmail</a></pre>









			<h2 id="IError">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error.go?s=71:223#L1">IError</a>
				<a class="permalink" href="index.html#IError">&#xb6;</a>


			</h2>

			<pre>type IError interface {
    Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    GetCode() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    GetStatus() <a href="http://localhost:6060/pkg/builtin/#int">int</a>
    JSON() interface{}
    OriginalError() <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    GetMessage() interface{}
}</pre>











				<h3 id="DBErrorToIError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=5040:5078#L213">DBErrorToIError</a>
					<a class="permalink" href="index.html#DBErrorToIError">&#xb6;</a>


				</h3>
				<pre>func DBErrorToIError(err <a href="http://localhost:6060/pkg/builtin/#error">error</a>) <a href="index.html#IError">IError</a></pre>





				<h3 id="MockIError">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/error_mock.go?s=76:130#L1">MockIError</a>
					<a class="permalink" href="index.html#MockIError">&#xb6;</a>


				</h3>
				<pre>func MockIError(args <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Arguments">Arguments</a>, index <a href="http://localhost:6060/pkg/builtin/#int">int</a>) <a href="index.html#IError">IError</a></pre>





				<h3 id="NewValidatorFields">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=1534:1584#L65">NewValidatorFields</a>
					<a class="permalink" href="index.html#NewValidatorFields">&#xb6;</a>


				</h3>
				<pre>func NewValidatorFields(fields interface{}) <a href="index.html#IError">IError</a></pre>





				<h3 id="RequesterToStruct">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=9522:9613#L367">RequesterToStruct</a>
					<a class="permalink" href="index.html#RequesterToStruct">&#xb6;</a>


				</h3>
				<pre>func RequesterToStruct(desc interface{}, requester func() (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)) <a href="index.html#IError">IError</a></pre>









			<h2 id="IFile">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=1619:1677#L52">IFile</a>
				<a class="permalink" href="index.html#IFile">&#xb6;</a>


			</h2>

			<pre>type IFile interface {
    Name() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    Value() []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>
}</pre>











				<h3 id="NewFile">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=2126:2171#L76">NewFile</a>
					<a class="permalink" href="index.html#NewFile">&#xb6;</a>


				</h3>
				<pre>func NewFile(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>) <a href="index.html#IFile">IFile</a></pre>









			<h2 id="IHTTPContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=324:787#L9">IHTTPContext</a>
				<a class="permalink" href="index.html#IHTTPContext">&#xb6;</a>


			</h2>

			<pre>type IHTTPContext interface {
    <a href="index.html#IContext">IContext</a>
    echo.<a href="index.html#Context">Context</a>
    BindWithValidate(ctx <a href="index.html#IValidateContext">IValidateContext</a>) <a href="index.html#IError">IError</a>
    BindWithValidateMessage(ctx <a href="index.html#IValidateContext">IValidateContext</a>) <a href="index.html#IError">IError</a>
    BindOnly(i interface{}) <a href="index.html#IError">IError</a>
    GetSignature() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    GetMessage() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    GetPageOptions() *<a href="index.html#PageOptions">PageOptions</a>
    GetPageOptionsWithOptions(options *<a href="index.html#PageOptionsOptions">PageOptionsOptions</a>) *<a href="index.html#PageOptions">PageOptions</a>
    GetUserAgent() *<a href="http://localhost:6060/pkg/github.com/mssola/user_agent/">user_agent</a>.<a href="http://localhost:6060/pkg/github.com/mssola/user_agent/#UserAgent">UserAgent</a>
    WithSaveCache(data interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, duration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) interface{}
}</pre>











				<h3 id="NewHTTPContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=1067:1146#L37">NewHTTPContext</a>
					<a class="permalink" href="index.html#NewHTTPContext">&#xb6;</a>


				</h3>
				<pre>func NewHTTPContext(ctx echo.<a href="index.html#Context">Context</a>, options *<a href="index.html#HTTPContextOptions">HTTPContextOptions</a>) <a href="index.html#IHTTPContext">IHTTPContext</a></pre>









			<h2 id="ILogger">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=329:594#L8">ILogger</a>
				<a class="permalink" href="index.html#ILogger">&#xb6;</a>


			</h2>

			<pre>type ILogger interface {
    Info(args ...interface{})
    Warn(args ...interface{})
    Debug(args ...interface{})
    DebugWithSkip(skip <a href="http://localhost:6060/pkg/builtin/#int">int</a>, args ...interface{})
    Error(message <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})
    ErrorWithSkip(skip <a href="http://localhost:6060/pkg/builtin/#int">int</a>, message <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})
}</pre>















			<h2 id="IMQ">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=680:936#L29">IMQ</a>
				<a class="permalink" href="index.html#IMQ">&#xb6;</a>


			</h2>

			<pre>type IMQ interface {
    Close()
    PublishJSON(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, data interface{}, options *<a href="index.html#MQPublishOptions">MQPublishOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Consume(ctx <a href="index.html#IMQContext">IMQContext</a>, name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, onConsume func(message <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Delivery">Delivery</a>), options *<a href="index.html#MQConsumeOptions">MQConsumeOptions</a>)
    Conn() *<a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Connection">Connection</a>
    ReConnect()
}</pre>















			<h2 id="IMQContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=114:302#L1">IMQContext</a>
				<a class="permalink" href="index.html#IMQContext">&#xb6;</a>


			</h2>

			<pre>type IMQContext interface {
    <a href="index.html#IContext">IContext</a>
    AddConsumer(handlerFunc func(ctx <a href="index.html#IMQContext">IMQContext</a>))
    Consume(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, onConsume func(message <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Delivery">Delivery</a>), options *<a href="index.html#MQConsumeOptions">MQConsumeOptions</a>)
    Start()
}</pre>











				<h3 id="NewMQContext">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=800:855#L27">NewMQContext</a>
					<a class="permalink" href="index.html#NewMQContext">&#xb6;</a>


				</h3>
				<pre>func NewMQContext(options *<a href="index.html#MQContextOptions">MQContextOptions</a>) <a href="index.html#IMQContext">IMQContext</a></pre>





				<h3 id="NewMQServer">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_server.go?s=83:137#L1">NewMQServer</a>
					<a class="permalink" href="index.html#NewMQServer">&#xb6;</a>


				</h3>
				<pre>func NewMQServer(options *<a href="index.html#MQContextOptions">MQContextOptions</a>) <a href="index.html#IMQContext">IMQContext</a></pre>









			<h2 id="IMongoDB">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=1542:3559#L56">IMongoDB</a>
				<a class="permalink" href="index.html#IMongoDB">&#xb6;</a>


			</h2>

			<pre>type IMongoDB interface {
    DB() *<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#Database">Database</a>
    Create(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, document interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#InsertOneOptions">InsertOneOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#InsertOneResult">InsertOneResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    FindAggregate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    FindAggregatePagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    FindAggregateOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Find(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    FindPagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    FindOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneOptions">FindOneOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    FindOneAndUpdate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndUpdateOptions">FindOneAndUpdateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    UpdateOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#UpdateOptions">UpdateOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#UpdateResult">UpdateResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Count(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#CountOptions">CountOptions</a>) (<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Drop(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    DeleteOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    DeleteMany(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    FindOneAndDelete(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndDeleteOptions">FindOneAndDeleteOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a>
    Close()
    Helper() <a href="index.html#IMongoDBHelper">IMongoDBHelper</a>
    CreateIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, models []<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#IndexModel">IndexModel</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#CreateIndexesOptions">CreateIndexesOptions</a>) ([]<a href="http://localhost:6060/pkg/builtin/#string">string</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    DropIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DropIndexesOptions">DropIndexesOptions</a>) (*<a href="index.html#MongoDropIndexResult">MongoDropIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    DropAll(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DropIndexesOptions">DropIndexesOptions</a>) (*<a href="index.html#MongoDropIndexResult">MongoDropIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    ListIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#ListIndexesOptions">ListIndexesOptions</a>) ([]<a href="index.html#MongoListIndexResult">MongoListIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
}</pre>















			<h2 id="IMongoDBHelper">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_helper.go?s=95:447#L1">IMongoDBHelper</a>
				<a class="permalink" href="index.html#IMongoDBHelper">&#xb6;</a>


			</h2>

			<pre>type IMongoDBHelper interface {
    Lookup(options *<a href="index.html#MongoLookupOptions">MongoLookupOptions</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Set(options <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Project(options <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Size(expression <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Filter(options *<a href="index.html#MongoFilterOptions">MongoFilterOptions</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Match(options <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Unwind(field <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    ReplaceRoot(options interface{}) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
    Or(options []<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>) <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
}</pre>











				<h3 id="NewMongoHelper">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_helper.go?s=451:487#L10">NewMongoHelper</a>
					<a class="permalink" href="index.html#NewMongoHelper">&#xb6;</a>


				</h3>
				<pre>func NewMongoHelper() <a href="index.html#IMongoDBHelper">IMongoDBHelper</a></pre>









			<h2 id="IMongoIndexBatch">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=39:105#L1">IMongoIndexBatch</a>
				<a class="permalink" href="index.html#IMongoIndexBatch">&#xb6;</a>


			</h2>

			<pre>type IMongoIndexBatch interface {
    Name() <a href="http://localhost:6060/pkg/builtin/#string">string</a>
    Run() <a href="http://localhost:6060/pkg/builtin/#error">error</a>
}</pre>















			<h2 id="IMongoIndexer">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=109:190#L2">IMongoIndexer</a>
				<a class="permalink" href="index.html#IMongoIndexer">&#xb6;</a>


			</h2>

			<pre>type IMongoIndexer interface {
    Add(batch <a href="index.html#IMongoIndexBatch">IMongoIndexBatch</a>)
    Execute() <a href="http://localhost:6060/pkg/builtin/#error">error</a>
}</pre>











				<h3 id="NewMongoIndexer">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=275:323#L12">NewMongoIndexer</a>
					<a class="permalink" href="index.html#NewMongoIndexer">&#xb6;</a>


				</h3>
				<pre>func NewMongoIndexer(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IMongoIndexer">IMongoIndexer</a></pre>









			<h2 id="IRequester">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=974:1539#L38">IRequester</a>
				<a class="permalink" href="index.html#IRequester">&#xb6;</a>


			</h2>

			<pre>type IRequester interface {
    Get(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Delete(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Post(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Create(method <a href="index.html#RequesterMethodType">RequesterMethodType</a>, url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Put(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    Patch(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
}</pre>











				<h3 id="NewRequester">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=2339:2381#L91">NewRequester</a>
					<a class="permalink" href="index.html#NewRequester">&#xb6;</a>


				</h3>
				<pre>func NewRequester(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IRequester">IRequester</a></pre>









			<h2 id="IS3">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go?s=983:1358#L37">IS3</a>
				<a class="permalink" href="index.html#IS3">&#xb6;</a>


			</h2>

			<pre>type IS3 interface {
    GetObject(path <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts *<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#GetObjectInput">GetObjectInput</a>) (*<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#GetObjectOutput">GetObjectOutput</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    PutObject(objectName <a href="http://localhost:6060/pkg/builtin/#string">string</a>, file <a href="http://localhost:6060/pkg/io/">io</a>.<a href="http://localhost:6060/pkg/io/#ReadSeeker">ReadSeeker</a>, opts *<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#PutObjectInput">PutObjectInput</a>, uploadOptions *<a href="index.html#UploadOptions">UploadOptions</a>) (*<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#PutObjectOutput">PutObjectOutput</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
    PutObjectByURL(objectName <a href="http://localhost:6060/pkg/builtin/#string">string</a>, url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts *<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#PutObjectInput">PutObjectInput</a>, uploadOptions *<a href="index.html#UploadOptions">UploadOptions</a>) (*<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/">ss3</a>.<a href="http://localhost:6060/pkg/github.com/aws/aws-sdk-go/service/s3/#PutObjectOutput">PutObjectOutput</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)
}</pre>















			<h2 id="ISeed">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/seeder.go?s=16:55#L1">ISeed</a>
				<a class="permalink" href="index.html#ISeed">&#xb6;</a>


			</h2>

			<pre>type ISeed interface {
    Run() <a href="http://localhost:6060/pkg/builtin/#error">error</a>
}</pre>















			<h2 id="IValidMessage">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=67:251#L1">IValidMessage</a>
				<a class="permalink" href="index.html#IValidMessage">&#xb6;</a>


			</h2>

			<pre>type IValidMessage struct {
<span id="IValidMessage.Name"></span>    Name    <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;-&#34;`
<span id="IValidMessage.Code"></span>    Code    <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;code&#34;`
<span id="IValidMessage.Message"></span>    Message <a href="http://localhost:6060/pkg/builtin/#string">string</a>      `json:&#34;message&#34;`
<span id="IValidMessage.Data"></span>    Data    interface{} `json:&#34;data,omitempty&#34;`
}
</pre>













				<h3 id="IValidMessage.Error">func (IValidMessage) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=255:292#L6">Error</a>
					<a class="permalink" href="index.html#IValidMessage.Error">&#xb6;</a>


				</h3>
				<pre>func (f <a href="index.html#IValidMessage">IValidMessage</a>) Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>








			<h2 id="IValidate">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=386:432#L11">IValidate</a>
				<a class="permalink" href="index.html#IValidate">&#xb6;</a>


			</h2>

			<pre>type IValidate interface {
    Valid() <a href="index.html#IError">IError</a>
}</pre>















			<h2 id="IValidateContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid_base_validator.go?s=436:501#L15">IValidateContext</a>
				<a class="permalink" href="index.html#IValidateContext">&#xb6;</a>


			</h2>

			<pre>type IValidateContext interface {
    Valid(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IError">IError</a>
}</pre>















			<h2 id="IWinRM">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go?s=169:263#L4">IWinRM</a>
				<a class="permalink" href="index.html#IWinRM">&#xb6;</a>


			</h2>

			<pre>type IWinRM interface {
    Command(command <a href="http://localhost:6060/pkg/builtin/#string">string</a>, isProduction <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>) (*<a href="index.html#WinRMResult">WinRMResult</a>, <a href="index.html#IError">IError</a>)
}</pre>











				<h3 id="NewWinRM">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go?s=308:342#L12">NewWinRM</a>
					<a class="permalink" href="index.html#NewWinRM">&#xb6;</a>


				</h3>
				<pre>func NewWinRM(ctx <a href="index.html#IContext">IContext</a>) <a href="index.html#IWinRM">IWinRM</a></pre>









			<h2 id="KeywordCondition">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=231:259#L7">KeywordCondition</a>
				<a class="permalink" href="index.html#KeywordCondition">&#xb6;</a>


			</h2>

			<pre>type KeywordCondition <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>















			<h2 id="KeywordConditionWrapper">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=563:671#L24">KeywordConditionWrapper</a>
				<a class="permalink" href="index.html#KeywordConditionWrapper">&#xb6;</a>


			</h2>

			<pre>type KeywordConditionWrapper struct {
<span id="KeywordConditionWrapper.Condition"></span>    Condition      <a href="index.html#KeywordCondition">KeywordCondition</a>
<span id="KeywordConditionWrapper.KeywordOptions"></span>    KeywordOptions []<a href="index.html#KeywordOptions">KeywordOptions</a>
}
</pre>











				<h3 id="NewKeywordAndCondition">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=3351:3436#L143">NewKeywordAndCondition</a>
					<a class="permalink" href="index.html#NewKeywordAndCondition">&#xb6;</a>


				</h3>
				<pre>func NewKeywordAndCondition(keywordOptions []<a href="index.html#KeywordOptions">KeywordOptions</a>) *<a href="index.html#KeywordConditionWrapper">KeywordConditionWrapper</a></pre>





				<h3 id="NewKeywordOrCondition">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=3543:3627#L150">NewKeywordOrCondition</a>
					<a class="permalink" href="index.html#NewKeywordOrCondition">&#xb6;</a>


				</h3>
				<pre>func NewKeywordOrCondition(keywordOptions []<a href="index.html#KeywordOptions">KeywordOptions</a>) *<a href="index.html#KeywordConditionWrapper">KeywordConditionWrapper</a></pre>









			<h2 id="KeywordOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=675:756#L29">KeywordOptions</a>
				<a class="permalink" href="index.html#KeywordOptions">&#xb6;</a>


			</h2>

			<pre>type KeywordOptions struct {
<span id="KeywordOptions.Type"></span>    Type  <a href="index.html#KeywordType">KeywordType</a>
<span id="KeywordOptions.Key"></span>    Key   <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="KeywordOptions.Value"></span>    Value <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>











				<h3 id="NewKeywordMustMatchOption">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=4078:4150#L173">NewKeywordMustMatchOption</a>
					<a class="permalink" href="index.html#NewKeywordMustMatchOption">&#xb6;</a>


				</h3>
				<pre>func NewKeywordMustMatchOption(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#KeywordOptions">KeywordOptions</a></pre>





				<h3 id="NewKeywordMustMatchOptions">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=3733:3810#L157">NewKeywordMustMatchOptions</a>
					<a class="permalink" href="index.html#NewKeywordMustMatchOptions">&#xb6;</a>


				</h3>
				<pre>func NewKeywordMustMatchOptions(keys []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, value <a href="http://localhost:6060/pkg/builtin/#string">string</a>) []<a href="index.html#KeywordOptions">KeywordOptions</a></pre>





				<h3 id="NewKeywordWildCardOption">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=4585:4656#L197">NewKeywordWildCardOption</a>
					<a class="permalink" href="index.html#NewKeywordWildCardOption">&#xb6;</a>


				</h3>
				<pre>func NewKeywordWildCardOption(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value <a href="http://localhost:6060/pkg/builtin/#string">string</a>) *<a href="index.html#KeywordOptions">KeywordOptions</a></pre>





				<h3 id="NewKeywordWildCardOptions">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=4242:4318#L181">NewKeywordWildCardOptions</a>
					<a class="permalink" href="index.html#NewKeywordWildCardOptions">&#xb6;</a>


				</h3>
				<pre>func NewKeywordWildCardOptions(keys []<a href="http://localhost:6060/pkg/builtin/#string">string</a>, value <a href="http://localhost:6060/pkg/builtin/#string">string</a>) []<a href="index.html#KeywordOptions">KeywordOptions</a></pre>









			<h2 id="KeywordType">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=261:284#L8">KeywordType</a>
				<a class="permalink" href="index.html#KeywordType">&#xb6;</a>


			</h2>

			<pre>type KeywordType <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>















			<h2 id="Logger">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=664:821#L18">Logger</a>
				<a class="permalink" href="index.html#Logger">&#xb6;</a>


			</h2>
			<p>Log is the logger utility with information of request context

			<pre>type Logger struct {
<span id="Logger.RequestID"></span>    RequestID <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="Logger.HostName"></span>    HostName  <a href="http://localhost:6060/pkg/builtin/#string">string</a>

<span id="Logger.Type"></span>    Type <a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/">consts</a>.<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/#ContextType">ContextType</a>
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>











				<h3 id="NewLogger">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=893:929#L28">NewLogger</a>
					<a class="permalink" href="index.html#NewLogger">&#xb6;</a>


				</h3>
				<pre>func NewLogger(ctx <a href="index.html#IContext">IContext</a>) *<a href="index.html#Logger">Logger</a></pre>
				<p>NewLogger will create the logger with context from echo context





				<h3 id="NewLoggerSimple">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=1820:1850#L60">NewLoggerSimple</a>
					<a class="permalink" href="index.html#NewLoggerSimple">&#xb6;</a>


				</h3>
				<pre>func NewLoggerSimple() *<a href="index.html#Logger">Logger</a></pre>
				<p>NewLoggerSimple return plain text simple logger







				<h3 id="Logger.Debug">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=4618:4666#L160">Debug</a>
					<a class="permalink" href="index.html#Logger.Debug">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) Debug(args ...interface{})</pre>
				<p>Debug log debug level






				<h3 id="Logger.DebugWithSkip">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=5119:5185#L174">DebugWithSkip</a>
					<a class="permalink" href="index.html#Logger.DebugWithSkip">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) DebugWithSkip(skip <a href="http://localhost:6060/pkg/builtin/#int">int</a>, args ...interface{})</pre>






				<h3 id="Logger.Error">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=5667:5730#L189">Error</a>
					<a class="permalink" href="index.html#Logger.Error">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) Error(message <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>
				<p>Error log error level






				<h3 id="Logger.ErrorWithSkip">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=6281:6362#L205">ErrorWithSkip</a>
					<a class="permalink" href="index.html#Logger.ErrorWithSkip">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) ErrorWithSkip(skip <a href="http://localhost:6060/pkg/builtin/#int">int</a>, message <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>
				<p>Error log error level






				<h3 id="Logger.Info">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=3565:3612#L129">Info</a>
					<a class="permalink" href="index.html#Logger.Info">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) Info(args ...interface{})</pre>
				<p>Info log information level






				<h3 id="Logger.Warn">func (*Logger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger.go?s=4090:4137#L144">Warn</a>
					<a class="permalink" href="index.html#Logger.Warn">&#xb6;</a>


				</h3>
				<pre>func (logger *<a href="index.html#Logger">Logger</a>) Warn(args ...interface{})</pre>
				<p>Warn log warnning level








			<h2 id="MQ">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=313:428#L8">MQ</a>
				<a class="permalink" href="index.html#MQ">&#xb6;</a>


			</h2>

			<pre>type MQ struct {
<span id="MQ.Host"></span>    Host     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQ.User"></span>    User     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQ.Password"></span>    Password <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQ.Port"></span>    Port     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQ.LogLevel"></span>    LogLevel <a href="http://localhost:6060/pkg/github.com/sirupsen/logrus/">logrus</a>.<a href="http://localhost:6060/pkg/github.com/sirupsen/logrus/#Level">Level</a>
}
</pre>











				<h3 id="NewMQ">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=3726:3756#L165">NewMQ</a>
					<a class="permalink" href="index.html#NewMQ">&#xb6;</a>


				</h3>
				<pre>func NewMQ(env *<a href="index.html#ENVConfig">ENVConfig</a>) *<a href="index.html#MQ">MQ</a></pre>







				<h3 id="MQ.Connect">func (*MQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=3948:3983#L176">Connect</a>
					<a class="permalink" href="index.html#MQ.Connect">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MQ">MQ</a>) Connect() (<a href="index.html#IMQ">IMQ</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>ConnectDB to connect Database






				<h3 id="MQ.ReConnect">func (*MQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=4231:4281#L190">ReConnect</a>
					<a class="permalink" href="index.html#MQ.ReConnect">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MQ">MQ</a>) ReConnect() (*<a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Connection">Connection</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>ConnectDB to connect Database








			<h2 id="MQConsumeOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=2109:2294#L90">MQConsumeOptions</a>
				<a class="permalink" href="index.html#MQConsumeOptions">&#xb6;</a>


			</h2>

			<pre>type MQConsumeOptions struct {
<span id="MQConsumeOptions.Durable"></span>    Durable    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.AutoDelete"></span>    AutoDelete <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.Exclusive"></span>    Exclusive  <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.NoWait"></span>    NoWait     <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.Args"></span>    Args       <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Table">Table</a>
<span id="MQConsumeOptions.AutoAck"></span>    AutoAck    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.NoLocal"></span>    NoLocal    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQConsumeOptions.Consumer"></span>    Consumer   <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>















			<h2 id="MQContext">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=306:343#L6">MQContext</a>
				<a class="permalink" href="index.html#MQContext">&#xb6;</a>


			</h2>

			<pre>type MQContext struct {
    <a href="index.html#IContext">IContext</a>
}
</pre>













				<h3 id="MQContext.AddConsumer">func (*MQContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=475:540#L15">AddConsumer</a>
					<a class="permalink" href="index.html#MQContext.AddConsumer">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#MQContext">MQContext</a>) AddConsumer(handlerFunc func(ctx <a href="index.html#IMQContext">IMQContext</a>))</pre>






				<h3 id="MQContext.Consume">func (*MQContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=566:672#L19">Consume</a>
					<a class="permalink" href="index.html#MQContext.Consume">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#MQContext">MQContext</a>) Consume(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, onConsume func(message <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Delivery">Delivery</a>), options *<a href="index.html#MQConsumeOptions">MQConsumeOptions</a>)</pre>






				<h3 id="MQContext.Start">func (*MQContext) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=347:374#L10">Start</a>
					<a class="permalink" href="index.html#MQContext.Start">&#xb6;</a>


				</h3>
				<pre>func (c *<a href="index.html#MQContext">MQContext</a>) Start()</pre>








			<h2 id="MQContextOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_context.go?s=730:796#L23">MQContextOptions</a>
				<a class="permalink" href="index.html#MQContextOptions">&#xb6;</a>


			</h2>

			<pre>type MQContextOptions struct {
<span id="MQContextOptions.ContextOptions"></span>    ContextOptions *<a href="index.html#ContextOptions">ContextOptions</a>
}
</pre>















			<h2 id="MQPublishOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq.go?s=432:676#L16">MQPublishOptions</a>
				<a class="permalink" href="index.html#MQPublishOptions">&#xb6;</a>


			</h2>

			<pre>type MQPublishOptions struct {
<span id="MQPublishOptions.Exchange"></span>    Exchange     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQPublishOptions.MessageID"></span>    MessageID    <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MQPublishOptions.Durable"></span>    Durable      <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.AutoDelete"></span>    AutoDelete   <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.Exclusive"></span>    Exclusive    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.Mandatory"></span>    Mandatory    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.Immediate"></span>    Immediate    <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.NoWait"></span>    NoWait       <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MQPublishOptions.DeliveryMode"></span>    DeliveryMode <a href="http://localhost:6060/pkg/builtin/#uint8">uint8</a>
<span id="MQPublishOptions.Args"></span>    Args         <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Table">Table</a>
}
</pre>















			<h2 id="Map">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/types.go?s=16:47#L1">Map</a>
				<a class="permalink" href="index.html#Map">&#xb6;</a>


			</h2>

			<pre>type Map map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}</pre>















			<h2 id="MockCache">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=77:115#L1">MockCache</a>
				<a class="permalink" href="index.html#MockCache">&#xb6;</a>


			</h2>

			<pre>type MockCache struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockCache">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=119:149#L2">NewMockCache</a>
					<a class="permalink" href="index.html#NewMockCache">&#xb6;</a>


				</h3>
				<pre>func NewMockCache() *<a href="index.html#MockCache">MockCache</a></pre>







				<h3 id="MockCache.Close">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=180:207#L6">Close</a>
					<a class="permalink" href="index.html#MockCache.Close">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) Close()</pre>






				<h3 id="MockCache.Del">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=511:552#L20">Del</a>
					<a class="permalink" href="index.html#MockCache.Del">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) Del(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockCache.Get">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=390:449#L15">Get</a>
					<a class="permalink" href="index.html#MockCache.Get">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) Get(dest interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockCache.GetJSON">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=773:836#L30">GetJSON</a>
					<a class="permalink" href="index.html#MockCache.GetJSON">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) GetJSON(dest interface{}, key <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockCache.Set">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=229:315#L10">Set</a>
					<a class="permalink" href="index.html#MockCache.Set">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) Set(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value interface{}, expiration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockCache.SetJSON">func (*MockCache) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/cache_mock.go?s=608:698#L25">SetJSON</a>
					<a class="permalink" href="index.html#MockCache.SetJSON">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockCache">MockCache</a>) SetJSON(key <a href="http://localhost:6060/pkg/builtin/#string">string</a>, value interface{}, expiration <a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="MockDatabase">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mock.go?s=108:176#L1">MockDatabase</a>
				<a class="permalink" href="index.html#MockDatabase">&#xb6;</a>


			</h2>

			<pre>type MockDatabase struct {
<span id="MockDatabase.Gorm"></span>    Gorm *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>
<span id="MockDatabase.Mock"></span>    Mock sqlmock.<a href="index.html#Sqlmock">Sqlmock</a>
}
</pre>











				<h3 id="NewMockDatabase">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mock.go?s=180:216#L4">NewMockDatabase</a>
					<a class="permalink" href="index.html#NewMockDatabase">&#xb6;</a>


				</h3>
				<pre>func NewMockDatabase() *<a href="index.html#MockDatabase">MockDatabase</a></pre>









			<h2 id="MockLogger">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=68:107#L1">MockLogger</a>
				<a class="permalink" href="index.html#MockLogger">&#xb6;</a>


			</h2>

			<pre>type MockLogger struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockLogger">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=111:143#L1">NewMockLogger</a>
					<a class="permalink" href="index.html#NewMockLogger">&#xb6;</a>


				</h3>
				<pre>func NewMockLogger() *<a href="index.html#MockLogger">MockLogger</a></pre>







				<h3 id="MockLogger.Debug">func (*MockLogger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=319:366#L13">Debug</a>
					<a class="permalink" href="index.html#MockLogger.Debug">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockLogger">MockLogger</a>) Debug(args ...interface{})</pre>






				<h3 id="MockLogger.Error">func (*MockLogger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=392:454#L17">Error</a>
					<a class="permalink" href="index.html#MockLogger.Error">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockLogger">MockLogger</a>) Error(message <a href="http://localhost:6060/pkg/builtin/#error">error</a>, args ...interface{})</pre>






				<h3 id="MockLogger.Info">func (*MockLogger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=175:221#L5">Info</a>
					<a class="permalink" href="index.html#MockLogger.Info">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockLogger">MockLogger</a>) Info(args ...interface{})</pre>






				<h3 id="MockLogger.Warn">func (*MockLogger) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/logger_mock.go?s=247:293#L9">Warn</a>
					<a class="permalink" href="index.html#MockLogger.Warn">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockLogger">MockLogger</a>) Warn(args ...interface{})</pre>








			<h2 id="MockMQ">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=98:133#L1">MockMQ</a>
				<a class="permalink" href="index.html#MockMQ">&#xb6;</a>


			</h2>

			<pre>type MockMQ struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockMQ">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=137:161#L2">NewMockMQ</a>
					<a class="permalink" href="index.html#NewMockMQ">&#xb6;</a>


				</h3>
				<pre>func NewMockMQ() *<a href="index.html#MockMQ">MockMQ</a></pre>







				<h3 id="MockMQ.Close">func (*MockMQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=539:563#L16">Close</a>
					<a class="permalink" href="index.html#MockMQ.Close">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMQ">MockMQ</a>) Close()</pre>






				<h3 id="MockMQ.Conn">func (*MockMQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=585:625#L20">Conn</a>
					<a class="permalink" href="index.html#MockMQ.Conn">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMQ">MockMQ</a>) Conn() *<a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Connection">Connection</a></pre>






				<h3 id="MockMQ.Consume">func (*MockMQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=353:462#L11">Consume</a>
					<a class="permalink" href="index.html#MockMQ.Consume">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMQ">MockMQ</a>) Consume(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, onConsume func(message <a href="http://localhost:6060/pkg/github.com/streadway/amqp/">amqp</a>.<a href="http://localhost:6060/pkg/github.com/streadway/amqp/#Delivery">Delivery</a>), options *<a href="index.html#MQConsumeOptions">MQConsumeOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMQ.PublishJSON">func (*MockMQ) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/mq_mock.go?s=189:281#L6">PublishJSON</a>
					<a class="permalink" href="index.html#MockMQ.PublishJSON">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMQ">MockMQ</a>) PublishJSON(name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, data interface{}, options *<a href="index.html#MQPublishOptions">MQPublishOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="MockMiddlewareManual">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_mock.go?s=185:237#L1">MockMiddlewareManual</a>
				<a class="permalink" href="index.html#MockMiddlewareManual">&#xb6;</a>


			</h2>

			<pre>type MockMiddlewareManual func(c <a href="index.html#IHTTPContext">IHTTPContext</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>















			<h2 id="MockMiddlewareOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_mock.go?s=239:390#L1">MockMiddlewareOptions</a>
				<a class="permalink" href="index.html#MockMiddlewareOptions">&#xb6;</a>


			</h2>

			<pre>type MockMiddlewareOptions struct {
<span id="MockMiddlewareOptions.Wrapper"></span>    Wrapper      <a href="index.html#MockMiddlewareWrapper">MockMiddlewareWrapper</a>
<span id="MockMiddlewareOptions.Manual"></span>    Manual       <a href="index.html#MockMiddlewareManual">MockMiddlewareManual</a>
<span id="MockMiddlewareOptions.IsPagination"></span>    IsPagination <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="MockMiddlewareOptions.IsDisabled"></span>    IsDisabled   <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
}
</pre>















			<h2 id="MockMiddlewareWrapper">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_middleware_mock.go?s=121:183#L1">MockMiddlewareWrapper</a>
				<a class="permalink" href="index.html#MockMiddlewareWrapper">&#xb6;</a>


			</h2>

			<pre>type MockMiddlewareWrapper func(model interface{}) interface{}</pre>















			<h2 id="MockMongoDB">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=152:192#L1">MockMongoDB</a>
				<a class="permalink" href="index.html#MockMongoDB">&#xb6;</a>


			</h2>

			<pre>type MockMongoDB struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockMongoDB">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=196:230#L3">NewMockMongoDB</a>
					<a class="permalink" href="index.html#NewMockMongoDB">&#xb6;</a>


				</h3>
				<pre>func NewMockMongoDB() *<a href="index.html#MockMongoDB">MockMongoDB</a></pre>







				<h3 id="MockMongoDB.Close">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=263:292#L7">Close</a>
					<a class="permalink" href="index.html#MockMongoDB.Close">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Close()</pre>






				<h3 id="MockMongoDB.Count">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=599:705#L16">Count</a>
					<a class="permalink" href="index.html#MockMongoDB.Count">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Count(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#CountOptions">CountOptions</a>) (<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.Create">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=3165:3295#L78">Create</a>
					<a class="permalink" href="index.html#MockMongoDB.Create">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Create(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, document interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#InsertOneOptions">InsertOneOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#InsertOneResult">InsertOneResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.DB">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=3406:3448#L83">DB</a>
					<a class="permalink" href="index.html#MockMongoDB.DB">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) DB() *<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#Database">Database</a></pre>






				<h3 id="MockMongoDB.DeleteMany">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=2737:2863#L68">DeleteMany</a>
					<a class="permalink" href="index.html#MockMongoDB.DeleteMany">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) DeleteMany(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.DeleteOne">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=2316:2441#L58">DeleteOne</a>
					<a class="permalink" href="index.html#MockMongoDB.DeleteOne">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) DeleteOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.Drop">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=2214:2259#L53">Drop</a>
					<a class="permalink" href="index.html#MockMongoDB.Drop">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Drop(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.Find">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=1519:1632#L36">Find</a>
					<a class="permalink" href="index.html#MockMongoDB.Find">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Find(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindAggregate">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=797:926#L21">FindAggregate</a>
					<a class="permalink" href="index.html#MockMongoDB.FindAggregate">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindAggregate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindAggregateOne">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=1308:1440#L31">FindAggregateOne</a>
					<a class="permalink" href="index.html#MockMongoDB.FindAggregateOne">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindAggregateOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindAggregatePagination">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=1005:1187#L26">FindAggregatePagination</a>
					<a class="permalink" href="index.html#MockMongoDB.FindAggregatePagination">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindAggregatePagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.FindOne">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=2969:3088#L73">FindOne</a>
					<a class="permalink" href="index.html#MockMongoDB.FindOne">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneOptions">FindOneOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindOneAndDelete">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=2547:2666#L63">FindOneAndDelete</a>
					<a class="permalink" href="index.html#MockMongoDB.FindOneAndDelete">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindOneAndDelete(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndDeleteOptions">FindOneAndDeleteOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindOneAndUpdate">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=1970:2129#L47">FindOneAndUpdate</a>
					<a class="permalink" href="index.html#MockMongoDB.FindOneAndUpdate">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindOneAndUpdate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{},
    opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndUpdateOptions">FindOneAndUpdateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MockMongoDB.FindPagination">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=314:480#L11">FindPagination</a>
					<a class="permalink" href="index.html#MockMongoDB.FindPagination">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) FindPagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockMongoDB.Helper">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=3517:3562#L88">Helper</a>
					<a class="permalink" href="index.html#MockMongoDB.Helper">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) Helper() <a href="index.html#IMongoDBHelper">IMongoDBHelper</a></pre>






				<h3 id="MockMongoDB.UpdateOne">func (*MockMongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_mock.go?s=1709:1856#L41">UpdateOne</a>
					<a class="permalink" href="index.html#MockMongoDB.UpdateOne">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoDB">MockMongoDB</a>) UpdateOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{},
    opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#UpdateOptions">UpdateOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#UpdateResult">UpdateResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="MockMongoIndexBatch">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=61:109#L1">MockMongoIndexBatch</a>
				<a class="permalink" href="index.html#MockMongoIndexBatch">&#xb6;</a>


			</h2>

			<pre>type MockMongoIndexBatch struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockMongoIndexBatch">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=195:245#L4">NewMockMongoIndexBatch</a>
					<a class="permalink" href="index.html#NewMockMongoIndexBatch">&#xb6;</a>


				</h3>
				<pre>func NewMockMongoIndexBatch() *<a href="index.html#MockMongoIndexBatch">MockMongoIndexBatch</a></pre>







				<h3 id="MockMongoIndexBatch.Name">func (*MockMongoIndexBatch) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=286:329#L8">Name</a>
					<a class="permalink" href="index.html#MockMongoIndexBatch.Name">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoIndexBatch">MockMongoIndexBatch</a>) Name() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="MockMongoIndexBatch.Run">func (*MockMongoIndexBatch) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=383:424#L13">Run</a>
					<a class="permalink" href="index.html#MockMongoIndexBatch.Run">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoIndexBatch">MockMongoIndexBatch</a>) Run() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="MockMongoIndexer">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=113:191#L1">MockMongoIndexer</a>
				<a class="permalink" href="index.html#MockMongoIndexer">&#xb6;</a>


			</h2>

			<pre>type MockMongoIndexer struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
<span id="MockMongoIndexer.Batches"></span>    Batches []*<a href="index.html#MockMongoIndexBatch">MockMongoIndexBatch</a>
}
</pre>











				<h3 id="NewMockMongoIndexer">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=477:521#L18">NewMockMongoIndexer</a>
					<a class="permalink" href="index.html#NewMockMongoIndexer">&#xb6;</a>


				</h3>
				<pre>func NewMockMongoIndexer() *<a href="index.html#MockMongoIndexer">MockMongoIndexer</a></pre>







				<h3 id="MockMongoIndexer.Add">func (*MockMongoIndexer) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=559:617#L22">Add</a>
					<a class="permalink" href="index.html#MockMongoIndexer.Add">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoIndexer">MockMongoIndexer</a>) Add(batch *<a href="index.html#MockMongoIndexBatch">MockMongoIndexBatch</a>)</pre>






				<h3 id="MockMongoIndexer.Execute">func (*MockMongoIndexer) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer_mock.go?s=774:816#L32">Execute</a>
					<a class="permalink" href="index.html#MockMongoIndexer.Execute">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockMongoIndexer">MockMongoIndexer</a>) Execute() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="MockRequester">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=68:110#L1">MockRequester</a>
				<a class="permalink" href="index.html#MockRequester">&#xb6;</a>


			</h2>

			<pre>type MockRequester struct {
    <a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/">mock</a>.<a href="http://localhost:6060/pkg/github.com/stretchr/testify/mock/#Mock">Mock</a>
}
</pre>











				<h3 id="NewMockRequester">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=114:152#L1">NewMockRequester</a>
					<a class="permalink" href="index.html#NewMockRequester">&#xb6;</a>


				</h3>
				<pre>func NewMockRequester() *<a href="index.html#MockRequester">MockRequester</a></pre>







				<h3 id="MockRequester.Delete">func (*MockRequester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=439:534#L15">Delete</a>
					<a class="permalink" href="index.html#MockRequester.Delete">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockRequester">MockRequester</a>) Delete(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockRequester.Get">func (*MockRequester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=187:279#L5">Get</a>
					<a class="permalink" href="index.html#MockRequester.Get">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockRequester">MockRequester</a>) Get(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockRequester.Patch">func (*MockRequester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=1247:1359#L45">Patch</a>
					<a class="permalink" href="index.html#MockRequester.Patch">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockRequester">MockRequester</a>) Patch(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockRequester.Post">func (*MockRequester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=694:805#L25">Post</a>
					<a class="permalink" href="index.html#MockRequester.Post">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockRequester">MockRequester</a>) Post(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MockRequester.Put">func (*MockRequester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester_mock.go?s=971:1081#L35">Put</a>
					<a class="permalink" href="index.html#MockRequester.Put">&#xb6;</a>


				</h3>
				<pre>func (m *<a href="index.html#MockRequester">MockRequester</a>) Put(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="MongoDB">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=3563:3651#L80">MongoDB</a>
				<a class="permalink" href="index.html#MongoDB">&#xb6;</a>


			</h2>

			<pre>type MongoDB struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="MongoDB.Close">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=3871:3895#L93">Close</a>
					<a class="permalink" href="index.html#MongoDB.Close">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Close()</pre>






				<h3 id="MongoDB.Count">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=5025:5126#L140">Count</a>
					<a class="permalink" href="index.html#MongoDB.Count">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Count(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#CountOptions">CountOptions</a>) (<a href="http://localhost:6060/pkg/builtin/#int64">int64</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.Create">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=9555:9680#L306">Create</a>
					<a class="permalink" href="index.html#MongoDB.Create">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Create(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, document interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#InsertOneOptions">InsertOneOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#InsertOneResult">InsertOneResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.CreateIndex">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=9807:9932#L313">CreateIndex</a>
					<a class="permalink" href="index.html#MongoDB.CreateIndex">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) CreateIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, models []<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#IndexModel">IndexModel</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#CreateIndexesOptions">CreateIndexesOptions</a>) ([]<a href="http://localhost:6060/pkg/builtin/#string">string</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.DB">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=11336:11373#L373">DB</a>
					<a class="permalink" href="index.html#MongoDB.DB">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) DB() *<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#Database">Database</a></pre>






				<h3 id="MongoDB.DeleteMany">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=8997:9118#L288">DeleteMany</a>
					<a class="permalink" href="index.html#MongoDB.DeleteMany">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) DeleteMany(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.DeleteOne">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=8500:8620#L274">DeleteOne</a>
					<a class="permalink" href="index.html#MongoDB.DeleteOne">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) DeleteOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DeleteOptions">DeleteOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#DeleteResult">DeleteResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.Drop">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=8357:8397#L267">Drop</a>
					<a class="permalink" href="index.html#MongoDB.Drop">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Drop(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.DropAll">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=10500:10605#L338">DropAll</a>
					<a class="permalink" href="index.html#MongoDB.DropAll">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) DropAll(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DropIndexesOptions">DropIndexesOptions</a>) (*<a href="index.html#MongoDropIndexResult">MongoDropIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.DropIndex">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=10068:10188#L320">DropIndex</a>
					<a class="permalink" href="index.html#MongoDB.DropIndex">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) DropIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, name <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#DropIndexesOptions">DropIndexesOptions</a>) (*<a href="index.html#MongoDropIndexResult">MongoDropIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.Find">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=7388:7496#L232">Find</a>
					<a class="permalink" href="index.html#MongoDB.Find">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Find(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindAggregate">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=5256:5380#L147">FindAggregate</a>
					<a class="permalink" href="index.html#MongoDB.FindAggregate">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindAggregate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindAggregateOne">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=6974:7101#L215">FindAggregateOne</a>
					<a class="permalink" href="index.html#MongoDB.FindAggregateOne">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindAggregateOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindAggregatePagination">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=5599:5776#L159">FindAggregatePagination</a>
					<a class="permalink" href="index.html#MongoDB.FindAggregatePagination">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindAggregatePagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, pipeline interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#AggregateOptions">AggregateOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.FindOne">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=9244:9358#L295">FindOne</a>
					<a class="permalink" href="index.html#MongoDB.FindOne">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindOne(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneOptions">FindOneOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindOneAndDelete">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=8745:8859#L281">FindOneAndDelete</a>
					<a class="permalink" href="index.html#MongoDB.FindOneAndDelete">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindOneAndDelete(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndDeleteOptions">FindOneAndDeleteOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindOneAndUpdate">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=7985:8139#L253">FindOneAndUpdate</a>
					<a class="permalink" href="index.html#MongoDB.FindOneAndUpdate">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindOneAndUpdate(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{},
    opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOneAndUpdateOptions">FindOneAndUpdateOptions</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="MongoDB.FindPagination">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=4150:4311#L106">FindPagination</a>
					<a class="permalink" href="index.html#MongoDB.FindPagination">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) FindPagination(dest interface{}, coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, pageOptions *<a href="index.html#PageOptions">PageOptions</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#FindOptions">FindOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.Helper">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=3655:3695#L85">Helper</a>
					<a class="permalink" href="index.html#MongoDB.Helper">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) Helper() <a href="index.html#IMongoDBHelper">IMongoDBHelper</a></pre>






				<h3 id="MongoDB.ListIndex">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=10911:11019#L356">ListIndex</a>
					<a class="permalink" href="index.html#MongoDB.ListIndex">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) ListIndex(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#ListIndexesOptions">ListIndexesOptions</a>) ([]<a href="index.html#MongoListIndexResult">MongoListIndexResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="MongoDB.UpdateOne">func (MongoDB) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=7708:7850#L244">UpdateOne</a>
					<a class="permalink" href="index.html#MongoDB.UpdateOne">&#xb6;</a>


				</h3>
				<pre>func (m <a href="index.html#MongoDB">MongoDB</a>) UpdateOne(coll <a href="http://localhost:6060/pkg/builtin/#string">string</a>, filter interface{}, update interface{},
    opts ...*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/">options</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/options/#UpdateOptions">UpdateOptions</a>) (*<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/">mongo</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/mongo/#UpdateResult">UpdateResult</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="MongoDropIndexResult">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=647:741#L24">MongoDropIndexResult</a>
				<a class="permalink" href="index.html#MongoDropIndexResult">&#xb6;</a>


			</h2>

			<pre>type MongoDropIndexResult struct {
<span id="MongoDropIndexResult.DropCount"></span>    DropCount <a href="http://localhost:6060/pkg/builtin/#int64">int64</a> `json:&#34;drop_count&#34; bson:&#34;nIndexesWas&#34;`
}
</pre>















			<h2 id="MongoFilterOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_helper.go?s=649:741#L21">MongoFilterOptions</a>
				<a class="permalink" href="index.html#MongoFilterOptions">&#xb6;</a>


			</h2>

			<pre>type MongoFilterOptions struct {
<span id="MongoFilterOptions.Input"></span>    Input     <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MongoFilterOptions.As"></span>    As        <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MongoFilterOptions.Condition"></span>    Condition <a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/">bson</a>.<a href="http://localhost:6060/pkg/go.mongodb.org/mongo-driver/bson/#M">M</a>
}
</pre>















			<h2 id="MongoIndexer">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=194:271#L7">MongoIndexer</a>
				<a class="permalink" href="index.html#MongoIndexer">&#xb6;</a>


			</h2>

			<pre>type MongoIndexer struct {
<span id="MongoIndexer.Batches"></span>    Batches []<a href="index.html#IMongoIndexBatch">IMongoIndexBatch</a>
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="MongoIndexer.Add">func (*MongoIndexer) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=373:423#L18">Add</a>
					<a class="permalink" href="index.html#MongoIndexer.Add">&#xb6;</a>


				</h3>
				<pre>func (i *<a href="index.html#MongoIndexer">MongoIndexer</a>) Add(batch <a href="index.html#IMongoIndexBatch">IMongoIndexBatch</a>)</pre>






				<h3 id="MongoIndexer.Execute">func (*MongoIndexer) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_indexer.go?s=552:590#L26">Execute</a>
					<a class="permalink" href="index.html#MongoIndexer.Execute">&#xb6;</a>


				</h3>
				<pre>func (i *<a href="index.html#MongoIndexer">MongoIndexer</a>) Execute() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="MongoListIndexResult">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo.go?s=449:643#L18">MongoListIndexResult</a>
				<a class="permalink" href="index.html#MongoListIndexResult">&#xb6;</a>


			</h2>

			<pre>type MongoListIndexResult struct {
<span id="MongoListIndexResult.Key"></span>    Key     map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]<a href="http://localhost:6060/pkg/builtin/#int64">int64</a> `json:&#34;key&#34; bson:&#34;key&#34;`
<span id="MongoListIndexResult.Name"></span>    Name    <a href="http://localhost:6060/pkg/builtin/#string">string</a>           `json:&#34;name&#34; bson:&#34;name&#34;`
<span id="MongoListIndexResult.Version"></span>    Version <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>            `json:&#34;version&#34; bson:&#34;v&#34;`
}
</pre>















			<h2 id="MongoLookupOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database_mongo_helper.go?s=522:645#L14">MongoLookupOptions</a>
				<a class="permalink" href="index.html#MongoLookupOptions">&#xb6;</a>


			</h2>

			<pre>type MongoLookupOptions struct {
<span id="MongoLookupOptions.From"></span>    From         <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MongoLookupOptions.LocalField"></span>    LocalField   <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MongoLookupOptions.ForeignField"></span>    ForeignField <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="MongoLookupOptions.As"></span>    As           <a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>















			<h2 id="PageOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go?s=272:368#L1">PageOptions</a>
				<a class="permalink" href="index.html#PageOptions">&#xb6;</a>


			</h2>

			<pre>type PageOptions struct {
<span id="PageOptions.Q"></span>    Q       <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="PageOptions.Limit"></span>    Limit   <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageOptions.Page"></span>    Page    <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageOptions.OrderBy"></span>    OrderBy []<a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>













				<h3 id="PageOptions.SetOrderDefault">func (*PageOptions) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go?s=372:427#L8">SetOrderDefault</a>
					<a class="permalink" href="index.html#PageOptions.SetOrderDefault">&#xb6;</a>


				</h3>
				<pre>func (p *<a href="index.html#PageOptions">PageOptions</a>) SetOrderDefault(orders ...<a href="http://localhost:6060/pkg/builtin/#string">string</a>)</pre>








			<h2 id="PageOptionsOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/http_context.go?s=1784:1845#L62">PageOptionsOptions</a>
				<a class="permalink" href="index.html#PageOptionsOptions">&#xb6;</a>


			</h2>

			<pre>type PageOptionsOptions struct {
<span id="PageOptionsOptions.OrderByAllowed"></span>    OrderByAllowed []<a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>















			<h2 id="PageResponse">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go?s=489:618#L14">PageResponse</a>
				<a class="permalink" href="index.html#PageResponse">&#xb6;</a>


			</h2>

			<pre>type PageResponse struct {
<span id="PageResponse.Total"></span>    Total   <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageResponse.Limit"></span>    Limit   <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageResponse.Count"></span>    Count   <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageResponse.Page"></span>    Page    <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="PageResponse.Q"></span>    Q       <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="PageResponse.OrderBy"></span>    OrderBy []<a href="http://localhost:6060/pkg/builtin/#string">string</a>
}
</pre>











				<h3 id="Paginate">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/database.go?s=2636:2726#L110">Paginate</a>
					<a class="permalink" href="index.html#Paginate">&#xb6;</a>


				</h3>
				<pre>func Paginate(db *<a href="http://localhost:6060/pkg/gorm.io/gorm/">gorm</a>.<a href="http://localhost:6060/pkg/gorm.io/gorm/#DB">DB</a>, model interface{}, options *<a href="index.html#PageOptions">PageOptions</a>) (*<a href="index.html#PageResponse">PageResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>









			<h2 id="Pagination">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go?s=16:268#L1">Pagination</a>
				<a class="permalink" href="index.html#Pagination">&#xb6;</a>


			</h2>

			<pre>type Pagination struct {
<span id="Pagination.Page"></span>    Page  <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>       `json:&#34;page&#34; example:&#34;1&#34;`
<span id="Pagination.Total"></span>    Total <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>       `json:&#34;total&#34; example:&#34;45&#34;`
<span id="Pagination.Limit"></span>    Limit <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>       `json:&#34;limit&#34; example:&#34;30&#34;`
<span id="Pagination.Count"></span>    Count <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>       `json:&#34;count&#34; example:&#34;30&#34;`
<span id="Pagination.Items"></span>    Items interface{} `json:&#34;items&#34;`
}
</pre>











				<h3 id="NewPagination">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/pagination.model.go?s=622:694#L23">NewPagination</a>
					<a class="permalink" href="index.html#NewPagination">&#xb6;</a>


				</h3>
				<pre>func NewPagination(items interface{}, options *<a href="index.html#PageResponse">PageResponse</a>) *<a href="index.html#Pagination">Pagination</a></pre>









			<h2 id="RequestResponse">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=609:970#L24">RequestResponse</a>
				<a class="permalink" href="index.html#RequestResponse">&#xb6;</a>


			</h2>

			<pre>type RequestResponse struct {
<span id="RequestResponse.Data"></span>    Data             map[<a href="http://localhost:6060/pkg/builtin/#string">string</a>]interface{}
<span id="RequestResponse.RawData"></span>    RawData          []<a href="http://localhost:6060/pkg/builtin/#byte">byte</a>
<span id="RequestResponse.ErrorCode"></span>    ErrorCode        <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="RequestResponse.StatusCode"></span>    StatusCode       <a href="http://localhost:6060/pkg/builtin/#int">int</a>
<span id="RequestResponse.Header"></span>    Header           <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#Header">Header</a>
<span id="RequestResponse.ContentLength"></span>    ContentLength    <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="RequestResponse.TransferEncoding"></span>    TransferEncoding []<a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="RequestResponse.Uncompressed"></span>    Uncompressed     <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="RequestResponse.Trailer"></span>    Trailer          <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#Header">Header</a>
<span id="RequestResponse.Request"></span>    Request          *<a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#Request">Request</a>
<span id="RequestResponse.TLS"></span>    TLS              *<a href="http://localhost:6060/pkg/crypto/tls/">tls</a>.<a href="http://localhost:6060/pkg/crypto/tls/#ConnectionState">ConnectionState</a>
}
</pre>











				<h3 id="RequestWrapper">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=1840:1947#L63">RequestWrapper</a>
					<a class="permalink" href="index.html#RequestWrapper">&#xb6;</a>


				</h3>
				<pre>func RequestWrapper(dest interface{}, requester func() (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>Deprecated: RequestWrapper is deprecated, use RequestToStruct or RequestToStructPagination instead.









			<h2 id="Requester">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=1543:1615#L47">Requester</a>
				<a class="permalink" href="index.html#Requester">&#xb6;</a>


			</h2>

			<pre>type Requester struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="Requester.Create">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=4582:4718#L173">Create</a>
					<a class="permalink" href="index.html#Requester.Create">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Create(method <a href="index.html#RequesterMethodType">RequesterMethodType</a>, url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="Requester.Delete">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=3453:3543#L135">Delete</a>
					<a class="permalink" href="index.html#Requester.Delete">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Delete(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="Requester.Get">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=3232:3319#L129">Get</a>
					<a class="permalink" href="index.html#Requester.Get">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Get(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="Requester.Patch">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=5287:5394#L193">Patch</a>
					<a class="permalink" href="index.html#Requester.Patch">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Patch(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="Requester.Post">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=3680:3786#L141">Post</a>
					<a class="permalink" href="index.html#Requester.Post">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Post(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>






				<h3 id="Requester.Put">func (Requester) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=5018:5123#L187">Put</a>
					<a class="permalink" href="index.html#Requester.Put">&#xb6;</a>


				</h3>
				<pre>func (r <a href="index.html#Requester">Requester</a>) Put(url <a href="http://localhost:6060/pkg/builtin/#string">string</a>, body interface{}, options *<a href="index.html#RequesterOptions">RequesterOptions</a>) (*<a href="index.html#RequestResponse">RequestResponse</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="RequesterMethodType">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/request.const.go?s=37:68#L1">RequesterMethodType</a>
				<a class="permalink" href="index.html#RequesterMethodType">&#xb6;</a>


			</h2>

			<pre>type RequesterMethodType <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>



				<pre>const (
    <span id="RequesterMethodTypeGET">RequesterMethodTypeGET</span>     <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodGet">MethodGet</a>
    <span id="RequesterMethodTypeHEAD">RequesterMethodTypeHEAD</span>    <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodHead">MethodHead</a>
    <span id="RequesterMethodTypePOST">RequesterMethodTypePOST</span>    <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodPost">MethodPost</a>
    <span id="RequesterMethodTypePUT">RequesterMethodTypePUT</span>     <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodPut">MethodPut</a>
    <span id="RequesterMethodTypePATCH">RequesterMethodTypePATCH</span>   <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodPatch">MethodPatch</a>
    <span id="RequesterMethodTypeDELETE">RequesterMethodTypeDELETE</span>  <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodDelete">MethodDelete</a>
    <span id="RequesterMethodTypeOPTIONS">RequesterMethodTypeOPTIONS</span> <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodOptions">MethodOptions</a>
    <span id="RequesterMethodTypeTRACE">RequesterMethodTypeTRACE</span>   <a href="index.html#RequesterMethodType">RequesterMethodType</a> = <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#MethodTrace">MethodTrace</a>
)</pre>














			<h2 id="RequesterOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/requester.go?s=363:605#L13">RequesterOptions</a>
				<a class="permalink" href="index.html#RequesterOptions">&#xb6;</a>


			</h2>

			<pre>type RequesterOptions struct {
<span id="RequesterOptions.BaseURL"></span>    BaseURL         <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="RequesterOptions.Timeout"></span>    Timeout         *<a href="http://localhost:6060/pkg/time/">time</a>.<a href="http://localhost:6060/pkg/time/#Duration">Duration</a>
<span id="RequesterOptions.Headers"></span>    Headers         <a href="http://localhost:6060/pkg/net/http/">http</a>.<a href="http://localhost:6060/pkg/net/http/#Header">Header</a>
<span id="RequesterOptions.Params"></span>    Params          <a href="http://localhost:6060/pkg/net/url/">xurl</a>.<a href="http://localhost:6060/pkg/net/url/#Values">Values</a>
<span id="RequesterOptions.RetryCount"></span>    RetryCount      <a href="http://localhost:6060/pkg/builtin/#int">int</a>
<span id="RequesterOptions.IsMultipartForm"></span>    IsMultipartForm <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="RequesterOptions.IsURLEncode"></span>    IsURLEncode     <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
<span id="RequesterOptions.IsBodyRawByte"></span>    IsBodyRawByte   <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
}
</pre>















			<h2 id="S3Config">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go?s=375:512#L10">S3Config</a>
				<a class="permalink" href="index.html#S3Config">&#xb6;</a>


			</h2>

			<pre>type S3Config struct {
<span id="S3Config.Endpoint"></span>    Endpoint  <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="S3Config.AccessKey"></span>    AccessKey <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="S3Config.SecretKey"></span>    SecretKey <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="S3Config.Bucket"></span>    Bucket    <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="S3Config.Region"></span>    Region    <a href="http://localhost:6060/pkg/builtin/#string">string</a>
<span id="S3Config.IsHTTPS"></span>    IsHTTPS   <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>
}
</pre>











				<h3 id="NewS3">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go?s=1421:1457#L48">NewS3</a>
					<a class="permalink" href="index.html#NewS3">&#xb6;</a>


				</h3>
				<pre>func NewS3(env *<a href="index.html#ENVConfig">ENVConfig</a>) *<a href="index.html#S3Config">S3Config</a></pre>







				<h3 id="S3Config.Connect">func (*S3Config) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go?s=516:557#L19">Connect</a>
					<a class="permalink" href="index.html#S3Config.Connect">&#xb6;</a>


				</h3>
				<pre>func (r *<a href="index.html#S3Config">S3Config</a>) Connect() (<a href="index.html#IS3">IS3</a>, <a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>








			<h2 id="Seeder">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/seeder.go?s=59:82#L1">Seeder</a>
				<a class="permalink" href="index.html#Seeder">&#xb6;</a>


			</h2>

			<pre>type Seeder struct {
}
</pre>











				<h3 id="NewSeeder">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/seeder.go?s=86:110#L1">NewSeeder</a>
					<a class="permalink" href="index.html#NewSeeder">&#xb6;</a>


				</h3>
				<pre>func NewSeeder() *<a href="index.html#Seeder">Seeder</a></pre>







				<h3 id="Seeder.Add">func (Seeder) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/seeder.go?s=138:184#L4">Add</a>
					<a class="permalink" href="index.html#Seeder.Add">&#xb6;</a>


				</h3>
				<pre>func (receiver <a href="index.html#Seeder">Seeder</a>) Add(seeder <a href="index.html#ISeed">ISeed</a>) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="UploadOptions">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/s3.go?s=1667:1745#L59">UploadOptions</a>
				<a class="permalink" href="index.html#UploadOptions">&#xb6;</a>


			</h2>

			<pre>type UploadOptions struct {
<span id="UploadOptions.Width"></span>    Width   <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="UploadOptions.Height"></span>    Height  <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
<span id="UploadOptions.Quality"></span>    Quality <a href="http://localhost:6060/pkg/builtin/#int64">int64</a>
}
</pre>















			<h2 id="Valid">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1145:1184#L50">Valid</a>
				<a class="permalink" href="index.html#Valid">&#xb6;</a>


			</h2>
			<p>Validator type

			<pre>type Valid struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>











				<h3 id="NewValid">func <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1077:1099#L45">NewValid</a>
					<a class="permalink" href="index.html#NewValid">&#xb6;</a>


				</h3>
				<pre>func NewValid() *<a href="index.html#Valid">Valid</a></pre>
				<p>New creates new validator







				<h3 id="Valid.Add">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=2504:2537#L119">Add</a>
					<a class="permalink" href="index.html#Valid.Add">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) Add(err ...<a href="http://localhost:6060/pkg/builtin/#error">error</a>)</pre>
				<p>Add adds errors






				<h3 id="Valid.Error">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1225:1255#L55">Error</a>
					<a class="permalink" href="index.html#Valid.Error">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>
				<p>Error returns error if has error






				<h3 id="Valid.GetCode">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1386:1418#L63">GetCode</a>
					<a class="permalink" href="index.html#Valid.GetCode">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) GetCode() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="Valid.GetMessage">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1854:1894#L83">GetMessage</a>
					<a class="permalink" href="index.html#Valid.GetMessage">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) GetMessage() interface{}</pre>






				<h3 id="Valid.GetStatus">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1450:1481#L67">GetStatus</a>
					<a class="permalink" href="index.html#Valid.GetStatus">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) GetStatus() <a href="http://localhost:6060/pkg/builtin/#int">int</a></pre>






				<h3 id="Valid.JSON">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1521:1555#L71">JSON</a>
					<a class="permalink" href="index.html#Valid.JSON">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) JSON() interface{}</pre>






				<h3 id="Valid.Must">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=2178:2231#L100">Must</a>
					<a class="permalink" href="index.html#Valid.Must">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) Must(x <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>, msg *<a href="index.html#IValidMessage">IValidMessage</a>) <a href="http://localhost:6060/pkg/builtin/#bool">bool</a></pre>
				<p>Must checks x must not an error or true if bool
and return true if valid
<p>msg must be error or string






				<h3 id="Valid.OriginalError">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1309:1346#L59">OriginalError</a>
					<a class="permalink" href="index.html#Valid.OriginalError">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) OriginalError() <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>






				<h3 id="Valid.OriginalErrorMessage">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=2592:2637#L123">OriginalErrorMessage</a>
					<a class="permalink" href="index.html#Valid.OriginalErrorMessage">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) OriginalErrorMessage() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>






				<h3 id="Valid.Valid">func (*Valid) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=1961:1991#L88">Valid</a>
					<a class="permalink" href="index.html#Valid.Valid">&#xb6;</a>


				</h3>
				<pre>func (v *<a href="index.html#Valid">Valid</a>) Valid() <a href="index.html#IError">IError</a></pre>
				<p>Valid returns true if no error








			<h2 id="ValidError">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=352:396#L11">ValidError</a>
				<a class="permalink" href="index.html#ValidError">&#xb6;</a>


			</h2>
			<p>Error is the validate error

			<pre>type ValidError struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="ValidError.Error">func (*ValidError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=437:474#L16">Error</a>
					<a class="permalink" href="index.html#ValidError.Error">&#xb6;</a>


				</h3>
				<pre>func (err *<a href="index.html#ValidError">ValidError</a>) Error() <a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>
				<p>Error implements error interface






				<h3 id="ValidError.Errors">func (*ValidError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=552:591#L21">Errors</a>
					<a class="permalink" href="index.html#ValidError.Errors">&#xb6;</a>


				</h3>
				<pre>func (err *<a href="index.html#ValidError">ValidError</a>) Errors() []<a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>
				<p>Errors returns errors






				<h3 id="ValidError.Strings">func (*ValidError) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/valid.go?s=658:699#L26">Strings</a>
					<a class="permalink" href="index.html#ValidError.Strings">&#xb6;</a>


				</h3>
				<pre>func (err *<a href="index.html#ValidError">ValidError</a>) Strings() []<a href="http://localhost:6060/pkg/builtin/#string">string</a></pre>
				<p>Strings returns errors in strings








			<h2 id="Validator">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=105:128#L1">Validator</a>
				<a class="permalink" href="index.html#Validator">&#xb6;</a>


			</h2>

			<pre>type Validator struct{}
</pre>













				<h3 id="Validator.Validate">func (*Validator) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/validator.go?s=1259:1309#L53">Validate</a>
					<a class="permalink" href="index.html#Validator.Validate">&#xb6;</a>


				</h3>
				<pre>func (cv *<a href="index.html#Validator">Validator</a>) Validate(i interface{}) <a href="http://localhost:6060/pkg/builtin/#error">error</a></pre>








			<h2 id="WinRM">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go?s=267:304#L8">WinRM</a>
				<a class="permalink" href="index.html#WinRM">&#xb6;</a>


			</h2>

			<pre>type WinRM struct {
    <span class="comment">// contains filtered or unexported fields</span>
}
</pre>













				<h3 id="WinRM.Command">func (*WinRM) <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go?s=385:466#L18">Command</a>
					<a class="permalink" href="index.html#WinRM.Command">&#xb6;</a>


				</h3>
				<pre>func (w *<a href="index.html#WinRM">WinRM</a>) Command(command <a href="http://localhost:6060/pkg/builtin/#string">string</a>, isProduction <a href="http://localhost:6060/pkg/builtin/#bool">bool</a>) (*<a href="index.html#WinRMResult">WinRMResult</a>, <a href="index.html#IError">IError</a>)</pre>








			<h2 id="WinRMResult">type <a href="http://localhost:6060/src/gitlab.finema.co/finema/idin-core/winrm.go?s=105:165#L1">WinRMResult</a>
				<a class="permalink" href="index.html#WinRMResult">&#xb6;</a>


			</h2>

			<pre>type WinRMResult struct {
<span id="WinRMResult.Result"></span>    Result <a href="http://localhost:6060/pkg/builtin/#string">string</a> `json:&#34;result&#34;`
}
</pre>























		<h2 id="pkg-subdirectories">Subdirectories</h2>

	<div class="pkg-dir">
		<table>
			<tr>
				<th class="pkg-name">Name</th>
				<th class="pkg-synopsis">Synopsis</th>
			</tr>


			<tr>
				<td colspan="2"><a href="http://localhost:6060/pkg/gitlab.finema.co/finema/">..</a></td>
			</tr>



				<tr>

					<td class="pkg-name" style="padding-left: 0px;">
						<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/consts/">consts</a>
					</td>

					<td class="pkg-synopsis">

					</td>
				</tr>

				<tr>

					<td class="pkg-name" style="padding-left: 0px;">
						<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/errmsgs/">errmsgs</a>
					</td>

					<td class="pkg-synopsis">

					</td>
				</tr>

				<tr>

					<td class="pkg-name" style="padding-left: 0px;">
						<a href="http://localhost:6060/pkg/gitlab.finema.co/finema/idin-core/utils/">utils</a>
					</td>

					<td class="pkg-synopsis">

					</td>
				</tr>

		</table>
	</div>



<div id="footer">
Build version go1.20.4.<br>
Except as <a href="https://developers.google.com/site-policies#restrictions">noted</a>,
the content of this page is licensed under the
Creative Commons Attribution 3.0 License,
and code is licensed under a <a href="http://localhost:6060/LICENSE">BSD license</a>.<br>
<a href="https://golang.org/doc/tos.html">Terms of Service</a> |
<a href="https://www.google.com/intl/en/policies/privacy/">Privacy Policy</a>
</div>

</div><!-- .container -->
</div><!-- #page -->
</body>
</html>
