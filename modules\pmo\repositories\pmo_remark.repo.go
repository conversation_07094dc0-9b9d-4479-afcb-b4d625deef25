package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Remark Repository
var PMORemark = repository.Make[models.PMORemark]()

func PMORemarkOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMORemark] {
	return func(c repository.IRepository[models.PMORemark]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMORemarkWithRelations() repository.Option[models.PMORemark] {
	return func(c repository.IRepository[models.PMORemark]) {
		c.Preload("CreatedBy").Preload("UpdatedBy")
	}
}

func PMORemarkByProjectID(projectID string) repository.Option[models.PMORemark] {
	return func(c repository.IRepository[models.PMORemark]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMORemarkByTabKey(tabKey string) repository.Option[models.PMORemark] {
	return func(c repository.IRepository[models.PMORemark]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMORemarkByProjectIDAndTabKey(projectID, tabKey string) repository.Option[models.PMORemark] {
	return func(c repository.IRepository[models.PMORemark]) {
		if projectID != "" && tabKey != "" {
			c.Where("project_id = ? AND tab_key = ?", projectID, tabKey)
		}
	}
}
