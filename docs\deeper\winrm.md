# WinRM

Windows Remote Management (WinRM)

### Interfaces

```go
type IWinRM interface {
	Command(command string, isProduction bool) (*WinRMResult, IError)
}
```

## Example

Sure! Here's an example of how you can use the `WinRM` package:

```go
result, err := ctx.WinRM().Command(command, isProduction)
if err != nil {
  fmt.Printf("Error executing WinRM command: %s\n", err.Error())
  return
}

fmt.Printf("WinRM command result: %s\n", result.Result)
```
