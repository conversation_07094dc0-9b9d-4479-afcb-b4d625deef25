package handlers

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewMeHTTP(e *echo.Echo) {
	me := &MeController{}

	// Me routes - all require authentication
	e.GET("/me", core.WithHTTPContext(me.GetProfile), middleware.AuthMiddleware())
	e.PUT("/me", core.WithHTTPContext(me.UpdateProfile), middleware.AuthMiddleware())
	e.GET("/me/devices", core.WithHTTPContext(me.GetDevices), middleware.AuthMiddleware())
	e.DELETE("/me/devices/:id", core.WithHTTPContext(me.DeleteDevice), middleware.AuthMiddleware())
}
