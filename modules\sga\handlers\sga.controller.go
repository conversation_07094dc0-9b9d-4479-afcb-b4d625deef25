package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/modules/sga/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/sga/services"
	"gitlab.finema.co/finema/finework/finework-api/modules/sga/dto"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type SgaController struct {
}

func (m SgaController) Pagination(c core.IHTTPContext) error {
	sgaSvc := services.NewSgaService(c)
	res, ierr := sgaSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m SgaController) Find(c core.IHTTPContext) error {
	sgaSvc := services.NewSgaService(c)
	sga, err := sgaSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, sga)
}

func (m SgaController) Create(c core.IHTTPContext) error {
	input := &requests.SgaCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	sgaSvc := services.NewSgaService(c)
	payload := &dto.SgaCreatePayload{}
	_ = utils.Copy(payload, input)
	sga, err := sgaSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, sga)
}

func (m SgaController) Update(c core.IHTTPContext) error {
	input := &requests.SgaUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	sgaSvc := services.NewSgaService(c)
	payload := &dto.SgaUpdatePayload{}
	_ = utils.Copy(payload, input)
	sga, err := sgaSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, sga)
}

func (m SgaController) Delete(c core.IHTTPContext) error {
	sgaSvc := services.NewSgaService(c)
	err := sgaSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
