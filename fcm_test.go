//go:build e2e
// +build e2e

package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewFMCService(t *testing.T) {
	ctx := NewContext(&ContextOptions{
		ENV: NewEnv(),
	})

	fcmSvc := NewFMC(ctx)
	assert.NotNil(t, fcmSvc)

	payload := &IFMCMessage{}

	ierr := fcmSvc.SendSimpleMessage([]string{"fdsdfsd"}, payload)
	assert.Error(t, ierr)
}

func TestFmcService_SendSimpleMessageLegacy(t *testing.T) {
	ctx := NewContext(&ContextOptions{
		ENV: NewEnv(),
	})

	fcmSvc := NewFMC(ctx)
	assert.NotNil(t, fcmSvc)

	payload := &IFMCMessage{
		BaseValidator: BaseValidator{},
		Title:         "title-001",
		Body:          "body-001",
		Data: map[string]string{
			"id": "TEST",
		},
	}

	ierr := fcmSvc.SendSimpleMessageLegacy("dqL07B1MTsi87oa6ACmmBL:APA91bHt5YgM-P5tGjQkG3QLrmepk1x5dHJor0AR0Jm7jGKZ6CorvVL_bN2_swDdznQ5iYWNKbBNlPl7WgC9zvX9OY3L0-fEVKHIF209zlxpVLb68L816Cp-Ci1_-bWbg7TH5m4CUI12", payload)
	assert.NoError(t, ierr)
}
