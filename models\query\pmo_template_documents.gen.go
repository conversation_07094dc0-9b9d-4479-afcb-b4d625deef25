// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOTemplateDocument(db *gorm.DB, opts ...gen.DOOption) pMOTemplateDocument {
	_pMOTemplateDocument := pMOTemplateDocument{}

	_pMOTemplateDocument.pMOTemplateDocumentDo.UseDB(db, opts...)
	_pMOTemplateDocument.pMOTemplateDocumentDo.UseModel(&models.PMOTemplateDocument{})

	tableName := _pMOTemplateDocument.pMOTemplateDocumentDo.TableName()
	_pMOTemplateDocument.ALL = field.NewAsterisk(tableName)
	_pMOTemplateDocument.ID = field.NewString(tableName, "id")
	_pMOTemplateDocument.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOTemplateDocument.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOTemplateDocument.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOTemplateDocument.TabKey = field.NewString(tableName, "tab_key")
	_pMOTemplateDocument.Name = field.NewString(tableName, "name")
	_pMOTemplateDocument.SharepointURL = field.NewString(tableName, "sharepoint_url")
	_pMOTemplateDocument.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOTemplateDocument.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOTemplateDocument.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOTemplateDocument.CreatedBy = pMOTemplateDocumentBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Checkins.User", "models.User"),
			},
		},
	}

	_pMOTemplateDocument.UpdatedBy = pMOTemplateDocumentBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOTemplateDocument.fillFieldMap()

	return _pMOTemplateDocument
}

type pMOTemplateDocument struct {
	pMOTemplateDocumentDo

	ALL           field.Asterisk
	ID            field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	TabKey        field.String
	Name          field.String
	SharepointURL field.String
	CreatedByID   field.String
	UpdatedByID   field.String
	DeletedByID   field.String
	CreatedBy     pMOTemplateDocumentBelongsToCreatedBy

	UpdatedBy pMOTemplateDocumentBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOTemplateDocument) Table(newTableName string) *pMOTemplateDocument {
	p.pMOTemplateDocumentDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOTemplateDocument) As(alias string) *pMOTemplateDocument {
	p.pMOTemplateDocumentDo.DO = *(p.pMOTemplateDocumentDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOTemplateDocument) updateTableName(table string) *pMOTemplateDocument {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.TabKey = field.NewString(table, "tab_key")
	p.Name = field.NewString(table, "name")
	p.SharepointURL = field.NewString(table, "sharepoint_url")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOTemplateDocument) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOTemplateDocument) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 12)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["name"] = p.Name
	p.fieldMap["sharepoint_url"] = p.SharepointURL
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOTemplateDocument) clone(db *gorm.DB) pMOTemplateDocument {
	p.pMOTemplateDocumentDo.ReplaceConnPool(db.Statement.ConnPool)
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOTemplateDocument) replaceDB(db *gorm.DB) pMOTemplateDocument {
	p.pMOTemplateDocumentDo.ReplaceDB(db)
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOTemplateDocumentBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a pMOTemplateDocumentBelongsToCreatedBy) Where(conds ...field.Expr) *pMOTemplateDocumentBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOTemplateDocumentBelongsToCreatedBy) WithContext(ctx context.Context) *pMOTemplateDocumentBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOTemplateDocumentBelongsToCreatedBy) Session(session *gorm.Session) *pMOTemplateDocumentBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOTemplateDocumentBelongsToCreatedBy) Model(m *models.PMOTemplateDocument) *pMOTemplateDocumentBelongsToCreatedByTx {
	return &pMOTemplateDocumentBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOTemplateDocumentBelongsToCreatedBy) Unscoped() *pMOTemplateDocumentBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOTemplateDocumentBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOTemplateDocumentBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOTemplateDocumentBelongsToCreatedByTx) Unscoped() *pMOTemplateDocumentBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOTemplateDocumentBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOTemplateDocumentBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOTemplateDocumentBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOTemplateDocumentBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOTemplateDocumentBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOTemplateDocumentBelongsToUpdatedBy) Session(session *gorm.Session) *pMOTemplateDocumentBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOTemplateDocumentBelongsToUpdatedBy) Model(m *models.PMOTemplateDocument) *pMOTemplateDocumentBelongsToUpdatedByTx {
	return &pMOTemplateDocumentBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOTemplateDocumentBelongsToUpdatedBy) Unscoped() *pMOTemplateDocumentBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOTemplateDocumentBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOTemplateDocumentBelongsToUpdatedByTx) Unscoped() *pMOTemplateDocumentBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOTemplateDocumentDo struct{ gen.DO }

type IPMOTemplateDocumentDo interface {
	gen.SubQuery
	Debug() IPMOTemplateDocumentDo
	WithContext(ctx context.Context) IPMOTemplateDocumentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOTemplateDocumentDo
	WriteDB() IPMOTemplateDocumentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOTemplateDocumentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOTemplateDocumentDo
	Not(conds ...gen.Condition) IPMOTemplateDocumentDo
	Or(conds ...gen.Condition) IPMOTemplateDocumentDo
	Select(conds ...field.Expr) IPMOTemplateDocumentDo
	Where(conds ...gen.Condition) IPMOTemplateDocumentDo
	Order(conds ...field.Expr) IPMOTemplateDocumentDo
	Distinct(cols ...field.Expr) IPMOTemplateDocumentDo
	Omit(cols ...field.Expr) IPMOTemplateDocumentDo
	Join(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo
	Group(cols ...field.Expr) IPMOTemplateDocumentDo
	Having(conds ...gen.Condition) IPMOTemplateDocumentDo
	Limit(limit int) IPMOTemplateDocumentDo
	Offset(offset int) IPMOTemplateDocumentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOTemplateDocumentDo
	Unscoped() IPMOTemplateDocumentDo
	Create(values ...*models.PMOTemplateDocument) error
	CreateInBatches(values []*models.PMOTemplateDocument, batchSize int) error
	Save(values ...*models.PMOTemplateDocument) error
	First() (*models.PMOTemplateDocument, error)
	Take() (*models.PMOTemplateDocument, error)
	Last() (*models.PMOTemplateDocument, error)
	Find() ([]*models.PMOTemplateDocument, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOTemplateDocument, err error)
	FindInBatches(result *[]*models.PMOTemplateDocument, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOTemplateDocument) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOTemplateDocumentDo
	Assign(attrs ...field.AssignExpr) IPMOTemplateDocumentDo
	Joins(fields ...field.RelationField) IPMOTemplateDocumentDo
	Preload(fields ...field.RelationField) IPMOTemplateDocumentDo
	FirstOrInit() (*models.PMOTemplateDocument, error)
	FirstOrCreate() (*models.PMOTemplateDocument, error)
	FindByPage(offset int, limit int) (result []*models.PMOTemplateDocument, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOTemplateDocumentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOTemplateDocumentDo) Debug() IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOTemplateDocumentDo) WithContext(ctx context.Context) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOTemplateDocumentDo) ReadDB() IPMOTemplateDocumentDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOTemplateDocumentDo) WriteDB() IPMOTemplateDocumentDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOTemplateDocumentDo) Session(config *gorm.Session) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOTemplateDocumentDo) Clauses(conds ...clause.Expression) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOTemplateDocumentDo) Returning(value interface{}, columns ...string) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOTemplateDocumentDo) Not(conds ...gen.Condition) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOTemplateDocumentDo) Or(conds ...gen.Condition) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOTemplateDocumentDo) Select(conds ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOTemplateDocumentDo) Where(conds ...gen.Condition) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOTemplateDocumentDo) Order(conds ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOTemplateDocumentDo) Distinct(cols ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOTemplateDocumentDo) Omit(cols ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOTemplateDocumentDo) Join(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOTemplateDocumentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOTemplateDocumentDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOTemplateDocumentDo) Group(cols ...field.Expr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOTemplateDocumentDo) Having(conds ...gen.Condition) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOTemplateDocumentDo) Limit(limit int) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOTemplateDocumentDo) Offset(offset int) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOTemplateDocumentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOTemplateDocumentDo) Unscoped() IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOTemplateDocumentDo) Create(values ...*models.PMOTemplateDocument) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOTemplateDocumentDo) CreateInBatches(values []*models.PMOTemplateDocument, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOTemplateDocumentDo) Save(values ...*models.PMOTemplateDocument) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOTemplateDocumentDo) First() (*models.PMOTemplateDocument, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateDocument), nil
	}
}

func (p pMOTemplateDocumentDo) Take() (*models.PMOTemplateDocument, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateDocument), nil
	}
}

func (p pMOTemplateDocumentDo) Last() (*models.PMOTemplateDocument, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateDocument), nil
	}
}

func (p pMOTemplateDocumentDo) Find() ([]*models.PMOTemplateDocument, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOTemplateDocument), err
}

func (p pMOTemplateDocumentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOTemplateDocument, err error) {
	buf := make([]*models.PMOTemplateDocument, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOTemplateDocumentDo) FindInBatches(result *[]*models.PMOTemplateDocument, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOTemplateDocumentDo) Attrs(attrs ...field.AssignExpr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOTemplateDocumentDo) Assign(attrs ...field.AssignExpr) IPMOTemplateDocumentDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOTemplateDocumentDo) Joins(fields ...field.RelationField) IPMOTemplateDocumentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOTemplateDocumentDo) Preload(fields ...field.RelationField) IPMOTemplateDocumentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOTemplateDocumentDo) FirstOrInit() (*models.PMOTemplateDocument, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateDocument), nil
	}
}

func (p pMOTemplateDocumentDo) FirstOrCreate() (*models.PMOTemplateDocument, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateDocument), nil
	}
}

func (p pMOTemplateDocumentDo) FindByPage(offset int, limit int) (result []*models.PMOTemplateDocument, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOTemplateDocumentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOTemplateDocumentDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOTemplateDocumentDo) Delete(models ...*models.PMOTemplateDocument) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOTemplateDocumentDo) withDO(do gen.Dao) *pMOTemplateDocumentDo {
	p.DO = *do.(*gen.DO)
	return p
}
