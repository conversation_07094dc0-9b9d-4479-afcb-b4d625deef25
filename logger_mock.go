package core

import (
	"github.com/stretchr/testify/mock"
)

type Mock<PERSON>ogger struct {
	mock.Mock
}

func NewMockLogger() *MockLogger {
	return &MockLogger{}
}

func (m *MockLogger) Info(args ...interface{}) {
	m.Called(args)
}

func (m *<PERSON>ckLogger) Warn(args ...interface{}) {
	m.Called(args)
}

func (m *<PERSON>ckLogger) Debug(args ...interface{}) {
	m.Called(args)
}

func (m *<PERSON>ckLogger) Error(message error, args ...interface{}) {
	m.Called(message, args)
}
