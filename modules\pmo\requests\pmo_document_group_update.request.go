package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentGroupUpdate struct {
	core.BaseValidator
	TabKey        *string `json:"tab_key"`
	GroupName     *string `json:"group_name"`
	SharepointURL *string `json:"sharepoint_url"`
}

func (r *PMODocumentGroupUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Tab<PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))

	return r.Error()
}
