package core

import (
  "fmt"
  "gitlab.finema.co/finema/idin-core/utils"
  "net/http"
  "time"
)

type WinRMResult struct {
  Result string `json:"result"`
}

type IWinRM interface {
  Command(command string, isProduction bool) (*WinRMResult, IError)
}

type WinRM struct {
  ctx IContext
}

func NewWinRM(ctx IContext) IWinRM {
  return &WinRM{
    ctx: ctx,
  }
}

func (w *WinRM) Command(command string, isProduction bool) (*WinRMResult, IError) {
  result := &WinRMResult{}
  token := utils.Base64Encode(fmt.Sprintf("%s:%s", w.ctx.ENV().Config().WinRMUser, w.ctx.ENV().Config().WinRMPassword))

  timeout := time.Minute * 3
  ierr := RequesterToStruct(&result, func() (*RequestResponse, error) {
    return w.ctx.Requester().Post("/command", map[string]interface{}{
      "command":       command,
      "is_production": isProduction,
    }, &RequesterOptions{
      BaseURL: w.ctx.ENV().Config().WinRMHost,
      Headers: http.Header{
        "Content-Type":  []string{"application/json"},
        "Authorization": []string{fmt.Sprintf("Basic %s", token)},
      },
      Timeout: &timeout,
    })
  })
  if ierr != nil {
    return nil, ierr
  }

  return result, nil
}
