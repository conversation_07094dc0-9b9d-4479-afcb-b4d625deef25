package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOCommentPaginationRequest struct {
	core.BaseValidator
	Channel  *string `json:"channel" query:"channel"`
	ParentID *string `json:"parent_id" query:"parent_id"`
}

func (r *PMOCommentPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Channel, strings.Join(models.PMOCommentChannels, "|"), "channel"))
	r.Must(r.IsExists(ctx, r.ParentID, models.PMOComment{}.TableName(), "id", "parent_id"))

	return r.<PERSON>r()
}
