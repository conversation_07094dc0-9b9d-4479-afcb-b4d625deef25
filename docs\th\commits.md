# Conventional Commits

Conventional Commits เป็นกฎเล็กน้อยที่ใช้สำหรับข้อความในการ Commit โค้ด มันจะให้รูปแบบที่สม่ำเสมอและอ่านง่ายสำหรับประวัติการควบคุมเวอร์ชัน ในเอกสารนี้จะอธิบายข้อกำหนดของ Conventional Commits เวอร์ชัน 1.0.0

อ้างอิงจาก https://www.conventionalcommits.org/en/v1.0.0/

## รูปแบบ

รูปแบบของ Conventional Commits ประกอบด้วยส่วนที่สาม: ชนิด (type), ขอบเขต (scope) ที่ไม่บังคับ, และข้อความ (message) แต่ละส่วนจะถูกแยกด้วยเครื่องหมายคอลอนและช่องว่าง (`: `)

```markdown
<type>(<scope>): <message>
```

นี่คือการอธิบายส่วนแต่ละส่วน:

- **ชนิด (Type)**: อธิบายวัตถุประสงค์ของการ Commit มีได้ดังนี้:

  - `feat`: คุณลักษณะหรือการปรับปรุงใหม่
  - `fix`: การแก้ไขข้อบกพร่อง
  - `docs`: การเปลี่ยนแปลงเอกสาร
  - `style`: การเปลี่ยนแปลงรูปแบบโค้ด (เช่น การจัดรูปแบบ)
  - `refactor`: การเปลี่ยนแปลงโค้ดที่ไม่เพิ่มคุณลักษณะหรือแก้ไขข้อบกพร่อง
  - `test`: เพิ่มหรือปรับเปลี่ยนการทดสอบ
  - `chore`: งานบำรุงรักษา, เปลี่ยนแปลงการสร้าง, หรือการเปลี่ยนแปลงอื่น ๆ ที่ไม่ได้เกี่ยวกับผู้ใช้

- **ขอบเขต (Scope)** (ไม่บังคับ): แทนส่วนประกอบหรือโมดูลที่ได้รับผลกระทบจากการ Commit

- **ข้อความ (Message)**: สรุปและอธิบายการเปลี่ยนแปลงโดยสั้น และมีคำอธิบายอย่างชัดเจน

## ตัวอย่าง

นี่คือตัวอย่างของข้อความในการ Commit ตามรูปแบบ Conventional Commits:

- `feat(auth): เพิ่มฟังก์ชันการเข้าสู่ระบบ`
- `fix(api): แก้ไขกรณีพิเศษในเส้นทางการค้นหา`
- `docs(readme): ปรับปรุงคำสั่งติดตั้ง`

## กฎการ Commit Message

Conventional Commits ยังมีการบังคับกฎของข้อความในการ Commit เพื่อทำให้เป็นไปอัตโนมัติในการเปลี่ยนเวอร์ชันและสร้าง changelog โดยการปฏิบัติตามกฎนี้ เครื่องมือสามารถวิเคราะห์ประวัติการ Commit และกำหนดระดับเวอร์ชันสัมพันธ์ที่ถูกต้องได้

กฎของข้อความในการ Commit ขึ้นอยู่กับคำนำหน้าที่ถูกเพิ่มในส่วนของชนิด (type):

- `BREAKING CHANGE`: ใช้สำหรับการ Commit ที่มีการเปลี่ยนแปลงที่ไม่สามารถเข้ากันได้กับเวอร์ชันก่อนหน้า ควรวางไว้ที่จุดเริ่มต้นของข้อความในการ Commit ตามด้วยช่องว่างและคำอธิบาย

นี่คือตัวอย่างข้อความในการ Commit ที่มีการเปลี่ยนแปลงที่ไม่เข้ากันได้:

```
feat(api): เพิ่มเส้นทาง API ใหม่

BREAKING CHANGE: รูปแบบการตอบสนองเปลี่ยนแล้วและผู้ใช้ต้องอัปเดตรหัสของตนตาม
```
## ทำไมถึงใช้ Conventional Commits

- สร้าง CHANGELOG โดยอัตโนมัติ
- กำหนดระดับเวอร์ชันโดยอัตโนมัติ (โดยอิงจากประเภทของการ Commit ที่มี)
- สื่อสารลักษณะของการเปลี่ยนแปลงให้ทีมงาน เป็นสาธารณะ และผู้มีส่วนได้ส่วนเสียอื่น ๆ
- เรียกใช้กระบวนการสร้างและเผยแพร่
- ทำให้ง่ายต่อการร่วมเขียนโปรเจคของผู้อื่น โดยอนุญาตให้สำรวจประวัติการ Commit ที่มีโครงสร้างอย่างมีระเบียบเรียบร้อยกว่าเดิม

## สรุป

การใช้งาน Conventional Commits จะให้วิธีการที่มีโครงสร้างและสม่ำเสมอในการเขียนข้อความในการ Commit นี้ช่วยให้กระบวนการสร้าง changelog และการเวอร์ชันอัตโนมัติเป็นเรื่องง่ายขึ้น โดยการปฏิบัติตามข้อกำหนด Conventional Commits เวอร์ชัน 1.0.0 ทีมสามารถปรับปรุงการทำงานร่วมกันได้และรักษาประวัติการควบคุมเวอร์ชันที่เป็นระเบียบเรียบร้อย
