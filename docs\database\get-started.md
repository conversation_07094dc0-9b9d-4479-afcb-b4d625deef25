# Get Started

This document provides a guide on how to get started with the database connection.

## Database relational

A relational database is a structured system for organizing and storing data using tables. It establishes relationships between tables based on common attributes, enabling efficient data management and retrieval. It ensures data integrity and supports flexible querying through the use of SQL.

In this section, we will use MySQL as our database since it is a versatile and widely adopted database solution that can handle a wide range of applications, from small-scale projects to enterprise-level systems. Its performance, scalability, and robust feature set make it a popular choice for developers and organizations around the world.

## Database connection

to simply connect to MySQL, let's update our `.env` file.

## update .env

```

DB_DRIVER=mysql
DB_USER=my_user
DB_PASSWORD=my_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=my_db
```

## To create a new Database instance

```go{7-11,18}

// APIRun is the main function to start the application.
func APIRun() {
    // Create a new environment configuration
	env := core.NewEnv()

    // Connect to Database
	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "MySQL: %v", err)
		os.Exit(1)
	}

    fmt.Println("Database connected successfully!")

	// Create a new HTTP server
	e := core.NewHTTPServer(&core.HTTPContextOptions{
		ContextOptions: &core.ContextOptions{
			DB:  db,
			ENV: env,
		},
	})

	// Start the HTTP server
    core.StartHTTPServer(e, env)
}

```

After that, you can start server with

```bash
$ go run main.go
```

You should see the following output in the terminal:

```
Database connected successfully!
HTTP Service: API
⇨ http server started on [::]:3000
```
