package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentGroupCreate struct {
	core.BaseValidator
	Tab<PERSON>ey        *string `json:"tab_key"`
	GroupName     *string `json:"group_name"`
	SharepointURL *string `json:"sharepoint_url"`
}

func (r *PMODocumentGroupCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.<PERSON>b<PERSON><PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.<PERSON>ame, "group_name"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))
	r.Must(r.IsStrIn(r.Tab<PERSON>ey, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
