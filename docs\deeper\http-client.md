# HTTP Client

http client package for your http requests. You can send requests quickly with this package

Base on [gojek/heimdall: An enhanced HTTP client for Go (github.com)](https://github.com/gojek/heimdall)

## Interface

```go
type IRequester interface {
    Get(url string, options *RequesterOptions) (*RequestResponse, error)
    Delete(url string, options *RequesterOptions) (*RequestResponse, error)
    Post(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)
    Create(method RequesterMethodType, url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)
    Put(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)
    Patch(url string, body interface{}, options *RequesterOptions) (*RequestResponse, error)
}
```

This is an interface that defines the methods for making HTTP requests. It includes the following methods:

- `Get`: Sends a GET request.
- `Delete`: Sends a DELETE request.
- `Post`: Sends a POST request.
- `Create`: Sends a request with the specified method.
- `Put`: Sends a PUT request.
- `Patch`: Sends a PATCH request.


## RequesterOptions

```go
type RequesterOptions struct {
    BaseURL         string
    Timeout         *time.Duration
    Headers         http.Header
    Params          xurl.Values
    RetryCount      int
    IsMultipartForm bool
    IsURLEncode     bool
    IsBodyRawByte   bool
}
```

This is a struct that represents the options for making a request. It contains the following fields:

- `BaseURL`: The base URL for the request.
- `Timeout`: The timeout duration for the request.
- `Headers`: The HTTP headers for the request.
- `Params`: The URL parameters for the request.
- `RetryCount`: The number of times to retry the request in case of failure.
- `IsMultipartForm`: A flag indicating whether the request is a multipart form.
- `IsURLEncode`: A flag indicating whether the request body should be URL-encoded.
- `IsBodyRawByte`: A flag indicating whether the request body is a raw byte.

## RequestResponse

```go
type RequestResponse struct {
    Data             map[string]interface{}
    RawData          []byte
    ErrorCode        string
    StatusCode       int
    Header           http.Header
    ContentLength    int64
    TransferEncoding []string
    Uncompressed     bool
    Trailer          http.Header
    Request          *http.Request
    TLS              *tls.ConnectionState
}
```

This is a struct that represents the response of a request. It contains the following fields:

- `Data`: A map of string keys to interface{} values representing the response data.
- `RawData`: The raw byte data of the response.
- `ErrorCode`: The error code of the response, if any.
- `StatusCode`: The HTTP status code of the response.
- `Header`: The HTTP headers of the response.
- `ContentLength`: The length of the response content.
- `TransferEncoding`: The transfer encoding used for the response.
- `Uncompressed`: A flag indicating whether the response is uncompressed.
- `Trailer`: The HTTP trailer headers of the response.
- `Request`: The original HTTP request associated with the response.
- `TLS`: The TLS connection state, if applicable.


## Example

```go
import (
  "fmt"
  "time"
  xurl "net/url"
)

func (s someService) SomeFunc() {
	// Get the requester from the context
	requester := s.ctx.Requester()

	// Create the options for the request
	options := &RequesterOptions{
		BaseURL: "https://api.example.com",
		Timeout: time.Second * 10,
		Headers: http.Header{
			"Authorization": []string{"Bearer token123"},
		},
		Params: xurl.Values{
			"param1": []string{"value1"},
			"param2": []string{"value2"},
		},
		RetryCount:      3,
		IsMultipartForm: false,
		IsURLEncode:     true,
		IsBodyRawByte:   false,
	}

	// Make a GET request using the requester and options
	response, err := requester.Get("/users", options)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// Print the response status code, data, and raw data
	fmt.Println("Response Status Code:", response.StatusCode)
	fmt.Println("Response Data:", response.Data)
	fmt.Println("Response Raw Data:", string(response.RawData))
}
```

In this example, we make a GET request using the `Get` method of the `IRequester` interface, passing in the endpoint and the options. The response and any error are stored in variables.

Finally, we print out the response status code, response data, and raw data for demonstration purposes.
