idin-core for golang

# get started

-   ```export GOPRIVATE=gitlab.finema.co/finema/*```
-   ```git config --global url."********************:".insteadOf "https://gitlab.finema.co/"```

## Documentation

- [x] Get Started (/get-started)
- [x] Architecture Concepts (/architecture)
- The Basics
  - [x] Routing (/basics/routing)
  - [x] Middleware (/basics/middleware)
  - [x] Controller (/basics/controller)
  - [x] Request (/basics/request)
  - [x] Response (/basics/response)
  - [ ] Views (/basics/views)
  - [x] Service (/basics/service)
  - [x] Validation (/basics/validation)
  - [x] Error Handling (/basics/error-handling)
  - [x] Logging (/basics/logging)
  - [x] Context (/basics/context)
  - [x] Environment (.env) (/basics/env)
- Digging Deeper
  - [x] HTTP Client (/deeper/http-client)
  - [x] Email (/deeper/email)
  - [x] CSV (/deeper/csv)
  - [x] File Storage (s3) (/deeper/s3)
  - [x] Archiver (zip) (/deeper/archiver)
  - [x] Cache (redis) (/deeper/redis)
  - [ ] Queues (rabbitmq) (/deeper/queues)
  - [x] Task Scheduling (cronjob) (/deeper/cronjob)
  - [x] Helpers (/deeper/helpers)
  - [x] Mocking API (/deeper/mocking-api)
  - [x] WinRM (/deeper/winrm)
  - [x] Faker (/deeper/faker)
- Security
  - [ ] Authentication (/security/authentication)
  - [ ] Authorization (/security/authorization)
  - [ ] Hashing (/security/hashing)
-  Database (Relational)
  - [ ] Get Started (/database/get-started)
  - [ ] Pagination (/database/pagination)
  - [ ] Repository (/database/repository)
  - [ ] Migrations (/database/migrations)
  - [ ] Seeding (/database/seeding)
- Database (MongoDB)
  - [ ] Get Started (/mongodb/get-started)
  - [ ] Pagination (/mongodb/pagination)
  - [ ] Repository (/mongodb/repository)
  - [ ] Migrations (/mongodb/migrations)
  - [ ] Seeding (/mongodb/seeding)
- Testing
  - [x] Get Started (/testing/get-started)
  - [ ] Unit Testing (/testing/unit-testing)
  - [ ] Integration Testing (/testing/integration-testing)
  - [ ] End-to-End Testing (/testing/end-to-end-testing)
  - [ ] Mocking (/testing/mocking)
- Deployment
  - [x] Get Started (/deploy/get-started)
  - [x] Docker (/deploy/docker)
  - [x] Kubernetes (/deploy/kubernetes)
  - [x] CI/CD (/deploy/ci-cd)
