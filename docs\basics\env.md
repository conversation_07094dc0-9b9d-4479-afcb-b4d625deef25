# Environment (.env)
When it comes to creating a production-grade application, using the environment variable in the application is de facto.

Base on [spf13/viper: Go configuration with fangs (github.com)](https://github.com/spf13/viper)

## Why should we use the environment variable?

Suppose you have an application with many features and each feature needs to access the Database. You configured all the DB information like `DBUR<PERSON>`, `DBNAME`, `USERNAME` and `PASSWORD` in each feature.

There are a few major disadvantages to this approach, but there can be many.

## Security Issue:

-   You're entering all the information in the code. Now, all unauthorized person also has access to the DB.
-   If you're using a code versioning tool `git` then the details of your DB will go public once you push the code.

## Code Management:

-   If you are changing a single variable then you have to change all the features. There is a high possibility that you'll miss one or two. 😌 been there
-   You can categorize the environment variables like `PROD`, `DEV`, or `TEST`. Just prefix the variable with the environment.

At the start, it might look like some extra work, but this will reward you a lot in your project.

::: warning
Just don't forget to include your .env files in the `.gitignore`.
:::

It is time for some action.

## Interface

```go
type IENV interface {
	Config() *ENVConfig
	IsDev() bool
	IsTest() bool
	IsMock() bool
	IsProd() bool
	Bool(key string) bool
	Int(key string) int
	String(key string) string
	All() map[string]string
}
```

## Usage
### 1. Use from default ENV

  
```go{5}
// SomeFunc is a method of the `someService` type.
// It prints the hostname of the database configured in the ENV.
func (s someService) SomeFunc() {
  // Retrieve the database hostname from the ENV configuration using the context
  dbHost := s.ctx.ENV().Config().DBHost
  // Print the database hostname
  fmt.Println("ENV Database hostname: ", dbHost)
}

```

### 2. Create new ENV
  
#### 2.1 Update .env file

```env
CUSTOM_SOMETHING=custom_env
```
#### 2.2 Add env key to constant file (consts/env.const.go)

```go
const (
  ENVCustomSomething = "CUSTOM_SOMETHING"
)
```
#### 2.3 Use new ENV

```go
// SomeFunc is a method of the `someService` type.
// It retrieves a custom environment variable called "ENVCustomSomething" and prints its value.
func (s someService) SomeFunc() {
  // Retrieve the value of the custom environment variable "ENVCustomSomething"
  somethingENV := s.ctx.ENV().String(consts.ENVCustomSomething)
  // Print the value of the custom environment variable
  fmt.Println("something value: ", somethingENV)
}

```

## Default ENV Key

### Application:
- `HOST` // Host for the application
- `ENV` // Environment (e.g., `dev`, `prod`, `mock`, `test`)
- `SERVICE` // Service name (e.g., `api`, `seed`, `cronjob`, `migration`)

### Logging:
- `LOG_LEVEL` // Log level (e.g., `debug`, `info`, `warn`, `error`)
- `LOG_HOST` // Host for logging service (graylog)
- `LOG_PORT` // UDP Port for logging service (graylog)

### Error Tracking:
- `SENTRY_DSN` // DSN for Sentry error tracking service

### Database (Relational):
- `DB_DRIVER` // Database driver (e.g., `mysql`, `postgres`, `mssql`)
- `DB_HOST` // Database host
- `DB_HOST` // Database host
- `DB_NAME` // Database name
- `DB_USER` // Database username
- `DB_PASSWORD` // Database password
- `DB_PORT` // Database port

### Database (MongoDB):
- `DB_MONGO_HOST` // MongoDB host
- `DB_MONGO_NAME` // MongoDB database name
- `DB_MONGO_USERNAME` // MongoDB username
- `DB_MONGO_PASSWORD` // MongoDB password
- `DB_MONGO_PORT` // MongoDB port

### Message Queue:
- `MQ_HOST` // Message Queue host
- `MQ_USER` // Message Queue username
- `MQ_PASSWORD` // Message Queue password
- `MQ_PORT` // Message Queue port


### Cache Service (Redis):
- `CACHE_PORT` // Cache service port
- `CACHE_HOST` // Cache service host

### Amazon S3:
- `S3_ENDPOINT` // S3 endpoint
- `S3_ACCESS_KEY` // S3 access key
- `S3_SECRET_KEY` // S3 secret key
- `S3_BUCKET` // S3 bucket name
- `S3_HTTPS` // Use HTTPS for S3 (boolean value)
- `S3_REGION` // S3 region

### Email SMTP Server:
- `EMAIL_SERVER` // Email server
- `EMAIL_PORT` // Email server port
- `EMAIL_USERNAME` // Email server username
- `EMAIL_PASSWORD` // Email server password
- `EMAIL_SENDER` // Email sender address

### Windows Remote Management (WinRM):
- `WINRM_HOST` // Windows Remote Management (WinRM) host
- `WINRM_USER` // WinRM username
- `WINRM_PASSWORD` // WinRM password
- `WINRM_PORT` // WinRM port

### ABCI:
- `ABCI_ENDPOINT` // ABCI endpoint

### Decentralized Identifiers (DID):
- `DID_METHOD_DEFAULT` // Default DID method
- `DID_KEY_TYPE_DEFAULT` // Default DID key type
