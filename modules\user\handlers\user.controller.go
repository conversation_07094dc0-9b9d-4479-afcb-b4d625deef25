package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type UserController struct {
}

func (m UserController) Pagination(c core.IHTTPContext) error {
	input := &requests.UserPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := emsgs.InvalidParamsError
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	userSvc := services.NewUserService(c)
	res, ierr := userSvc.Pagination(c.GetPageOptions(), &dto.UserPaginationOptions{
		TeamCode: input.TeamCode,
		IsActive: input.IsActive,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m UserController) Find(c core.IHTTPContext) error {
	userSvc := services.NewUserService(c)
	user, err := userSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, user)
}

func (m UserController) Create(c core.IHTTPContext) error {
	input := &requests.UserCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	userSvc := services.NewUserService(c)
	payload := &dto.UserCreatePayload{}
	_ = utils.Copy(payload, input)
	user, err := userSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, user)
}

func (m UserController) Update(c core.IHTTPContext) error {
	input := &requests.UserUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	userSvc := services.NewUserService(c)
	payload := &dto.UserUpdatePayload{}
	_ = utils.Copy(payload, input)
	user, err := userSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, user)
}

func (m UserController) UpdateAccessLevel(c core.IHTTPContext) error {
	input := &requests.UserAccessLevelUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	userSvc := services.NewUserService(c)
	payload := &dto.UserAccessLevelUpdatePayload{}
	_ = utils.Copy(payload, input)
	user, err := userSvc.UpdateAccessLevel(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, user)
}

func (m UserController) Delete(c core.IHTTPContext) error {
	userSvc := services.NewUserService(c)
	err := userSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
