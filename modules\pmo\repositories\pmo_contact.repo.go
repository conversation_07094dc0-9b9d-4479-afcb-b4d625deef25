package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Contact Repository
var PMOContact = repository.Make[models.PMOContact]()

func PMOContactOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOContact] {
	return func(c repository.IRepository[models.PMOContact]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOContactWithRelations() repository.Option[models.PMOContact] {
	return func(c repository.IRepository[models.PMOContact]) {
		c.Preload("CreatedBy").Preload("UpdatedBy")
	}
}

func PMOContactByProjectID(projectID string) repository.Option[models.PMOContact] {
	return func(c repository.IRepository[models.PMOContact]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}
