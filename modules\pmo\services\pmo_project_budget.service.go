package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm/clause"
)

type IPMOProjectBudgetService interface {
	Create(input *dto.PMOBudgetCreatePayload) (*models.PMOBudgetInfo, core.IError)
	Find(projectID string) (*models.PMOBudgetInfo, core.IError)
	BudgetVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBudgetInfoVersion], core.IError)
}

type pmoProjectBudgetService struct {
	ctx core.IContext
}

// PMO Budget methods implementation
func (s pmoProjectBudgetService) Create(input *dto.PMOBudgetCreatePayload) (*models.PMOBudgetInfo, core.IError) {
	// Check if budget already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	budget := &models.PMOBudgetInfo{
		BaseModel:    models.NewBaseModel(),
		ProjectID:    input.ProjectID,
		FundType:     input.FundType,
		ProjectValue: input.ProjectValue,
		BidbondValue: input.BidbondValue,
		Partner:      input.Partner,
		CreatedByID:  utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOBudgetInfo(s.ctx).Create(budget)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOBudgetInfoVersion{
		PMOBudgetInfo: *budget,
		OriginalID:    budget.ID,
	}

	ierr = repositories.PMOBudgetInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(budget.ProjectID)
}

func (s pmoProjectBudgetService) update(existing *models.PMOBudgetInfo, input *dto.PMOBudgetCreatePayload) (*models.PMOBudgetInfo, core.IError) {
	// Update the main record
	existing.FundType = input.FundType
	existing.ProjectValue = input.ProjectValue
	existing.BidbondValue = input.BidbondValue
	existing.Partner = input.Partner
	existing.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)
	existing.UpdatedAt = utils.GetCurrentDateTime()

	ierr := repositories.PMOBudgetInfo(s.ctx).Where("id = ?", existing.ID).Updates(existing)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOBudgetInfoVersion{
		PMOBudgetInfo: *existing,
		OriginalID:    existing.ID,
	}

	ierr = repositories.PMOBudgetInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectBudgetService) Find(projectID string) (*models.PMOBudgetInfo, core.IError) {
	return repositories.PMOBudgetInfo(s.ctx,
		repositories.PMOBudgetInfoWithRelations(),
	).FindOne("project_id = ?", projectID)
}

func (s pmoProjectBudgetService) BudgetVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBudgetInfoVersion], core.IError) {
	// First get the budget info to get the original ID
	budget, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOBudgetInfoVersion(s.ctx,
		repositories.PMOBudgetInfoVersionOrderBy(pageOptions),
		repositories.PMOBudgetInfoVersionWithRelations(),
		repositories.PMOBudgetInfoVersionByBudgetInfoID(budget.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectBudgetService(ctx core.IContext) IPMOProjectBudgetService {
	return &pmoProjectBudgetService{ctx: ctx}
}
