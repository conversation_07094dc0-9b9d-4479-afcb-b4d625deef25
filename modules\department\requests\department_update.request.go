package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/department/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DepartmentUpdate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentUpdate) Valid(ctx core.IContext) core.IError {
	cc := ctx.(core.IHTTPContext)
	oldNameTh := ""
	oldNameEn := ""

	department, _ := repositories.Department(cc).FindOne("id = ?", cc.Param("id"))
	if department != nil {
		oldNameTh = department.NameTh
		if department.NameEn != nil {
			oldNameEn = utils.ToNonPointer(department.NameEn)
		}
	}

	if utils.ToNonPointer(r.MinistryID) != "" {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	if utils.ToNonPointer(r.NameTh) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameTh, models.Department{}.TableName(), "name_th", oldNameTh, "name_th"))
	}

	if utils.ToNonPointer(r.NameEn) != "" {
		r.Must(r.IsStrUnique(ctx, r.NameEn, models.Department{}.TableName(), "name_en", oldNameEn, "name_en"))
	}

	return r.Error()
}
