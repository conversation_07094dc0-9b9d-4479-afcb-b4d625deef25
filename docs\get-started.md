# Get Started

This document provides a guide on how to get started with the API.

## Installation

To install IDIN Core Go v1.19 or higher is required. Make sure your project folder is outside
your ```$GOPATH```.

```bash
# Create a new directory and navigate into it
mkdir myapp && cd myapp

# Initialize a new Go module with the name "myapp"
go mod init myapp

# Set the GOPRIVATE environment variable to include the private repository URL
export GOPRIVATE=gitlab.finema.co/finema/*

# Configure Git to use SSH instead of HTTPS for the private repository URL
git config --global url."********************:".insteadOf "https://gitlab.finema.co/"

# Install the "idin-core" package from the private repository
go get gitlab.finema.co/finema/idin-core
```

## Hello, World!

#### Create .env

```env
HOST=0.0.0.0:3000
ENV=dev
LOG_LEVEL=debug
```

#### Create main.go

```go
package main

import (
  "github.com/labstack/echo/v4"
  core "gitlab.finema.co/finema/idin-core"
  "net/http"
)

func main() {
  // Create a new environment configuration
  env := core.NewEnv()

  // Create a new Echo HTTP server
  e := core.NewHTTPServer(&core.HTTPContextOptions{
    ContextOptions: &core.ContextOptions{
      ENV: env,
    },
  })

  // Define a route for the root path ("/")
  e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
    // Return a JSON response with a "message" field
    return c.JSON(http.StatusOK, core.Map{
      "message": "Hello, World!",
    })
  }))

  // Start the HTTP server
  core.StartHTTPServer(e, env)
}

```

#### Start server

```bash 
$ go run main.go
```

Browse to [http://localhost:3000](http://localhost:3000) and you should see Hello, World! on the page.
