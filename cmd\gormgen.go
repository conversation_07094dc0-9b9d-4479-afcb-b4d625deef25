package cmd

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gorm.io/gen"
)

func GormGenRun() {

	// env := core.NewEnv()
	// db, err := core.NewDatabase(env.Config()).Connect()
	// if err != nil {
	// 	fmt.Fprintf(os.<PERSON>derr, "Postgres: %v \n", err)
	// 	os.Exit(1)
	// }

	g := gen.NewGenerator(gen.Config{
		OutPath: "./models/query",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	g.ApplyBasic(
		models.User{},
		models.UserAccessLevel{},
		models.Team{},
		models.Holiday{},
		models.Sga{},
		models.Project{},
		models.Timesheet{},
		models.Checkin{},
		models.Ministry{},
		models.Department{},
		models.PMOProject{},
		models.PMOTemplateChecklistItem{},
		models.PMOTemplateDocument{},
		models.PMOCollaborator{},
		models.PMOContact{},
		models.PMOCompetitor{},
		models.PMOPartner{},
		models.PMOBudgetInfo{},
		models.PMOBiddingInfo{},
		models.PMOContractInfo{},
		models.PMOBidbondInfo{},
		models.PMOLGInfo{},
		models.PMOVendorItem{},
		models.PMOChecklistItem{},
		models.PMOComment{},
		models.PMORemark{},
		models.PMODocumentGroup{},
		models.PMODocumentItem{},
		models.PMOCommentVersion{},
		models.PMORemarkVersion{},
		models.PMODocumentItemVersion{},
		models.PMOBudgetInfoVersion{},
		models.PMOBiddingInfoVersion{},
		models.PMOContractInfoVersion{},
		models.PMOBidbondInfoVersion{},
		models.PMOLGInfoVersion{},
	)

	// Generate the code
	g.Execute()
}
