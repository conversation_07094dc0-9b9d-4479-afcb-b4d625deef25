# PMO Project Checklist Items API

This document describes the API endpoints for managing PMO Project Checklist Items.

## Overview

PMO Project Checklist Items are project-specific checklist items that can be used to track tasks, requirements, or milestones for individual PMO projects. Each checklist item belongs to a specific project and can be categorized by tab keys.

## Base URL

All endpoints are prefixed with `/pmo/projects/:id/checklist-items`

## Authentication

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header.

## Endpoints

### 1. Get Project Checklist Items (Pagination)

**GET** `/pmo/projects/:id/checklist-items`

**Query Parameters:**
- `tab_key` (optional): Filter by PMO tab key (INFO, SALES, PRESALES, BIDDING, PMO, BIZCO)
- `is_checked` (optional): Filter by checked status (true/false)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search query for detail field

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "project_id": "uuid",
      "tab_key": "INFO",
      "detail": "Checklist item detail",
      "is_checked": false,
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "created_by_id": "uuid",
      "updated_by_id": "uuid",
      "project": {
        "id": "uuid",
        "name": "Project Name",
        "slug": "project-slug"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

### 2. Get Checklist Item by ID

**GET** `/pmo/projects/:id/checklist-items/:item_id`

**Response:**
```json
{
  "id": "uuid",
  "project_id": "uuid",
  "tab_key": "INFO",
  "detail": "Checklist item detail",
  "is_checked": false,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "created_by_id": "uuid",
  "updated_by_id": "uuid",
  "project": {
    "id": "uuid",
    "name": "Project Name",
    "slug": "project-slug"
  }
}
```

### 3. Create Checklist Item

**POST** `/pmo/projects/:id/checklist-items`

**Request Body:**
```json
{
  "tab_key": "INFO",
  "detail": "Checklist item detail",
  "is_checked": false
}
```

**Validation Rules:**
- `tab_key`: Required, must be one of the valid PMO tab keys (INFO, SALES, PRESALES, BIDDING, PMO, BIZCO)
- `detail`: Required, string
- `is_checked`: Optional, boolean (default: false)

**Response:** Same as Get Checklist Item by ID (201 Created)

### 4. Update Checklist Item

**PUT** `/pmo/projects/:id/checklist-items/:item_id`

**Request Body:**
```json
{
  "tab_key": "SALES",
  "detail": "Updated checklist item detail",
  "is_checked": true
}
```

**Validation Rules:**
- `tab_key`: Optional, must be one of the valid PMO tab keys if provided
- `detail`: Optional, string
- `is_checked`: Optional, boolean

**Response:** Same as Get Checklist Item by ID (200 OK)

### 5. Delete Checklist Item

**DELETE** `/pmo/projects/:id/checklist-items/:item_id`

**Response:** 204 No Content

## Error Responses

All endpoints may return the following error responses:

- `400 Bad Request`: Invalid request parameters or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Project or checklist item not found
- `500 Internal Server Error`: Server error

## PMO Tab Keys

Valid tab keys for checklist items:
- `INFO`: Information/General
- `SALES`: Sales related
- `PRESALES`: Pre-sales activities
- `BIDDING`: Bidding process
- `PMO`: Project Management Office
- `BIZCO`: Business Coordination
