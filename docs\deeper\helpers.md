# Helpers

**Base64 Encoding/Decoding:**
- Base64Decode
- Base64Encode

**Byte Conversion:**
- BytesToHexString
- BytesToString

**Password Management:**
- ComparePassword
- HashPassword

**Data Copying:**
- Copy

**ECDSA Key Generation and Management:**
- CreateECDSAPrivateKey
- CreateECDSAPublicKey
- EncodeKeyPair
- LoadPrivateKey
- LoadPublicKey
- SignMessage
- SignMessageWithOption

**RSA Key Generation and Management:**
- EncodeRSAKeyPair
- LoadRSAPrivateKey
- LoadRSAPublicKey

**Message Encryption/Decryption:**
- DecryptCipherText
- EncryptMessage
- MessageEncryptionOptions

**DID (Decentralized Identifier) Operations:**
- GenerateDID
- ExtractDID

**Key Pair Generation:**
- GenerateKeyPair
- GenerateKeyPairWithOption
- GenerateKeyPairOption

**Type Conversion and Parsing:**
- GetArrayString
- GetBigNumber
- GetBool
- GetBoolFalse
- GetBoolTrue
- GetCurrentDateTime
- GetFloat64
- GetInt
- GetInt64
- GetJSON
- GetMD5Hash
- GetString
- GetUUID
- HexToBytesString
- JSONParse
- JSONToString
- StringToBytes
- StructToHexString
- StructToMap
- StructToString
- StructToStringNoPretty
- ToUUID
- ToPointer
- ToNonPointer

**Data Existence Check:**
- IsEmpty
- IsExists
- IsExistsWithCondition

**String Operations:**
- IsStrIn
- IsUUID

**Digital Signature Verification:**
- VerifySignature
- VerifySignatureWithOption
- VerifySignatureOption

**Other Utility Functions:**
- LogStruct
- MockExplorer
- NewSha256
- NewSha384
- NewSha512
- RootDir

**Data Type Conversion:**
- ConvertConsensus
- ConvertNewKey

**Structures:**
- Consensus
- DIDDetail
- ECDSASignature
- NewKey
- RSAKeyPair
- RSAKeySize
- KeyPair
- MessageEncryptionOptions
- SignMessageOption
- VerifySignatureOption

## Example
```go
// NewDefaultModel creates a new instance of DefaultDependencyModel with default values.
// It generates a UUID for the ID, sets the RequestID, CreatedAt, CreatedBy, UpdatedAt, and UpdatedBy fields.
// The RequestID and createdBy parameters are used to initialize the corresponding fields.
func NewDefaultModel(requestID string, createdBy string) *DefaultDependencyModel {
  return &DefaultDependencyModel{
    ID:        utils.GetUUID(),
    RequestID: requestID,
    CreatedAt: utils.GetCurrentDateTime(),
    CreatedBy: createdBy,
    UpdatedAt: utils.GetCurrentDateTime(),
    UpdatedBy: createdBy,
  }
}

```
