package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Contract Info Version Repository
var PMOContractInfoVersion = repository.Make[models.PMOContractInfoVersion]()

func PMOContractInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOContractInfoVersionByContractInfoID(contractInfoID string) repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		if contractInfoID != "" {
			c.Where("contract_info_id = ?", contractInfoID)
		}
	}
}

func PMOContractInfoVersionWithRelations() repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		c.Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}
