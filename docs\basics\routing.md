# Routing

In this guide, we'll learn how to define basic routes.

## file structure for user module

```

├── modules
│   └── user
│       ├── user.controller.go     # User module controller handling
│       └── user.http.go           # User module HTTP routes and handlers
├── .env                           # Configuration file for environment variables
├── main.go                        # Main application file
```

## Path Matching

- `Static`: Exact matches of fixed segments in the URL.
- `Param`: Matching dynamic segments indicated by placeholders, such as ":id" or "{username}".
- `Wildcard`: Matching multiple segments or any part of the URL using wildcards, such as "*", "\*\*", or "{*path}".

## Example

```go

// Routes
e.GET("/user/john", core.WithHTTPContext(func(c core.IHTTPContext) error {
	return c.JSON(http.StatusOK, "/user/john")
}))
e.GET("/user/:id", core.WithHTTPContext(func(c core.IHTTPContext) error {
	return c.JSON(http.StatusOK, "/user/:id")
}))
e.GET("/user/john/*", core.WithHTTPContext(func(c core.IHTTPContext) error {
	return c.JSON(http.StatusOK, "/user/john/*")
}))

```

Above routes would response in the following order

- /user/john
- /user/:id
- /user/john/\*

::: tip
Routes can be written in any order.
:::

## Basic route

To register routes, you can specify the HTTP method, path, and a corresponding controller. This allows you to define how your application handles incoming requests. For instance, the following code snippet demonstrates the registration of a route that responds to a GET request with the path "/user" and sends the HTTP response "Hello, World!" and a status code of 200 (OK).

```go

// Controller
func hello(c core.IHTTPContext) error {
    return c.String(http.StatusOK, "Hello, World!")
}
// Route
e.GET("/user", core.WithHTTPContext(hello))


```

Core defines handler function as <span style="color: lightgreen;">func(core.IHTTPContext) error</span> where <span style="color: lightgreen;">IHTTPContext</span> primarily holds HTTP request and response interfaces.

## Common routes

there are several common routes or endpoints that are often used in applications. These routes typically represent common actions performed on resources. Here are some examples of common routes:

- `GET "/users/:id"`- This route is used to fetch a specific user resource from the server based on its unique ID.

- `POST "/users"` - This route is used to submit data to the server for creating a new user resource.

- `PUT "/users/:id"` - This route is used to update an existing user resource on the server. The updated user data is sent in the request payload, and the user identified by the ID is modified accordingly.

- `DELETE "/users/:id"` - This route is used to delete an existing user resource from the server based on its ID.

## Example

### main.go file

```go
package main

import (
    "github.com/labstack/echo/v4"
    core "gitlab.finema.co/finema/idin-core"
    "net/http"
)

func main() {
    // Create a new environment configuration
    env := core.NewEnv()

    // Create a new HTTP server
    e := core.NewHTTPServer(&core.HTTPContextOptions{
        ContextOptions: &core.ContextOptions{
            ENV: env,
        },
    })

    // Define NewUserHTTP
    user.NewUserHTTP(e)

    // Start the HTTP server
    core.StartHTTPServer(e, env)
}

```

### user.http.go file

```go
package user

import (
	"github.com/labstack/echo/v4"
	core "gitlab.finema.co/finema/idin-core"
)

func NewUserHTTP(e *echo.Echo) {
    // Controller instance
	controller := &UserController{}

    // Routes
    e.GET("/users/:id", core.WithHTTPContext(controller.Find))
    e.POST("/users", core.WithHTTPContext(controller.Create))
    e.PUT("/users/:id", core.WithHTTPContext(controller.Update))
    e.DELETE("/users/:id", core.WithHTTPContext(controller.Delete))
}



```

### user.controller.go file

```go
package user

import (
	"net/http"
	"gitlab.finema.co/finema/finesign/finesign-api/requests"
	"gitlab.finema.co/finema/finesign/finesign-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type UserController struct {
}
// Find retrieves a user by ID
func Find(c core.IHTTPContext) error {
    userService := services.NewUserService(c)
	user, err := userService.Find(c.Param("id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, "Error! can not find specific user")
	}

	return c.JSON(http.StatusOK, user)
}

// Create creates a new user
func Create(c core.IHTTPContext) error {
    // implement
}

// Update updates an existing user by ID
func Update(c core.IHTTPContext) error {
    // implement
}

// Delete deletes a user by ID
func Delete(c core.IHTTPContext) error {
    // implement
}

```

Let's examine the `GET "/users/:id"` route in the `user.http.go` file. In this route, we define a parameter called "id" which allows us to match a specific user ID from the request URL.

Moving to the `user.controller.go` file, we find the `Find(c core.IHTTPContext)` function. Inside this function, with the help of the userService, we can invoke the `Find()` function and provide the value of `c.Param("id")` as an argument, allowing us to find the user with a specific ID and a status code of 200 (OK).
