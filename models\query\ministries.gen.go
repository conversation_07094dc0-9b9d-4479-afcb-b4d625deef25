// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newMinistry(db *gorm.DB, opts ...gen.DOOption) ministry {
	_ministry := ministry{}

	_ministry.ministryDo.UseDB(db, opts...)
	_ministry.ministryDo.UseModel(&models.Ministry{})

	tableName := _ministry.ministryDo.TableName()
	_ministry.ALL = field.NewAsterisk(tableName)
	_ministry.ID = field.NewString(tableName, "id")
	_ministry.CreatedAt = field.NewTime(tableName, "created_at")
	_ministry.UpdatedAt = field.NewTime(tableName, "updated_at")
	_ministry.DeletedAt = field.NewField(tableName, "deleted_at")
	_ministry.NameTh = field.NewString(tableName, "name_th")
	_ministry.NameEn = field.NewString(tableName, "name_en")
	_ministry.CreatedByID = field.NewString(tableName, "created_by_id")
	_ministry.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_ministry.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_ministry.Departments = ministryHasManyDepartments{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Departments", "models.Department"),
		Ministry: struct {
			field.RelationField
			Departments struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Departments.Ministry", "models.Ministry"),
			Departments: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Departments.Ministry.Departments", "models.Department"),
			},
		},
	}

	_ministry.fillFieldMap()

	return _ministry
}

type ministry struct {
	ministryDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	NameTh      field.String
	NameEn      field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	Departments ministryHasManyDepartments

	fieldMap map[string]field.Expr
}

func (m ministry) Table(newTableName string) *ministry {
	m.ministryDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m ministry) As(alias string) *ministry {
	m.ministryDo.DO = *(m.ministryDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *ministry) updateTableName(table string) *ministry {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.NameTh = field.NewString(table, "name_th")
	m.NameEn = field.NewString(table, "name_en")
	m.CreatedByID = field.NewString(table, "created_by_id")
	m.UpdatedByID = field.NewString(table, "updated_by_id")
	m.DeletedByID = field.NewString(table, "deleted_by_id")

	m.fillFieldMap()

	return m
}

func (m *ministry) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *ministry) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 10)
	m.fieldMap["id"] = m.ID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["name_th"] = m.NameTh
	m.fieldMap["name_en"] = m.NameEn
	m.fieldMap["created_by_id"] = m.CreatedByID
	m.fieldMap["updated_by_id"] = m.UpdatedByID
	m.fieldMap["deleted_by_id"] = m.DeletedByID

}

func (m ministry) clone(db *gorm.DB) ministry {
	m.ministryDo.ReplaceConnPool(db.Statement.ConnPool)
	m.Departments.db = db.Session(&gorm.Session{Initialized: true})
	m.Departments.db.Statement.ConnPool = db.Statement.ConnPool
	return m
}

func (m ministry) replaceDB(db *gorm.DB) ministry {
	m.ministryDo.ReplaceDB(db)
	m.Departments.db = db.Session(&gorm.Session{})
	return m
}

type ministryHasManyDepartments struct {
	db *gorm.DB

	field.RelationField

	Ministry struct {
		field.RelationField
		Departments struct {
			field.RelationField
		}
	}
}

func (a ministryHasManyDepartments) Where(conds ...field.Expr) *ministryHasManyDepartments {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a ministryHasManyDepartments) WithContext(ctx context.Context) *ministryHasManyDepartments {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a ministryHasManyDepartments) Session(session *gorm.Session) *ministryHasManyDepartments {
	a.db = a.db.Session(session)
	return &a
}

func (a ministryHasManyDepartments) Model(m *models.Ministry) *ministryHasManyDepartmentsTx {
	return &ministryHasManyDepartmentsTx{a.db.Model(m).Association(a.Name())}
}

func (a ministryHasManyDepartments) Unscoped() *ministryHasManyDepartments {
	a.db = a.db.Unscoped()
	return &a
}

type ministryHasManyDepartmentsTx struct{ tx *gorm.Association }

func (a ministryHasManyDepartmentsTx) Find() (result []*models.Department, err error) {
	return result, a.tx.Find(&result)
}

func (a ministryHasManyDepartmentsTx) Append(values ...*models.Department) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a ministryHasManyDepartmentsTx) Replace(values ...*models.Department) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a ministryHasManyDepartmentsTx) Delete(values ...*models.Department) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a ministryHasManyDepartmentsTx) Clear() error {
	return a.tx.Clear()
}

func (a ministryHasManyDepartmentsTx) Count() int64 {
	return a.tx.Count()
}

func (a ministryHasManyDepartmentsTx) Unscoped() *ministryHasManyDepartmentsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type ministryDo struct{ gen.DO }

type IMinistryDo interface {
	gen.SubQuery
	Debug() IMinistryDo
	WithContext(ctx context.Context) IMinistryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMinistryDo
	WriteDB() IMinistryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMinistryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMinistryDo
	Not(conds ...gen.Condition) IMinistryDo
	Or(conds ...gen.Condition) IMinistryDo
	Select(conds ...field.Expr) IMinistryDo
	Where(conds ...gen.Condition) IMinistryDo
	Order(conds ...field.Expr) IMinistryDo
	Distinct(cols ...field.Expr) IMinistryDo
	Omit(cols ...field.Expr) IMinistryDo
	Join(table schema.Tabler, on ...field.Expr) IMinistryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMinistryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMinistryDo
	Group(cols ...field.Expr) IMinistryDo
	Having(conds ...gen.Condition) IMinistryDo
	Limit(limit int) IMinistryDo
	Offset(offset int) IMinistryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMinistryDo
	Unscoped() IMinistryDo
	Create(values ...*models.Ministry) error
	CreateInBatches(values []*models.Ministry, batchSize int) error
	Save(values ...*models.Ministry) error
	First() (*models.Ministry, error)
	Take() (*models.Ministry, error)
	Last() (*models.Ministry, error)
	Find() ([]*models.Ministry, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Ministry, err error)
	FindInBatches(result *[]*models.Ministry, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Ministry) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMinistryDo
	Assign(attrs ...field.AssignExpr) IMinistryDo
	Joins(fields ...field.RelationField) IMinistryDo
	Preload(fields ...field.RelationField) IMinistryDo
	FirstOrInit() (*models.Ministry, error)
	FirstOrCreate() (*models.Ministry, error)
	FindByPage(offset int, limit int) (result []*models.Ministry, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMinistryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m ministryDo) Debug() IMinistryDo {
	return m.withDO(m.DO.Debug())
}

func (m ministryDo) WithContext(ctx context.Context) IMinistryDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m ministryDo) ReadDB() IMinistryDo {
	return m.Clauses(dbresolver.Read)
}

func (m ministryDo) WriteDB() IMinistryDo {
	return m.Clauses(dbresolver.Write)
}

func (m ministryDo) Session(config *gorm.Session) IMinistryDo {
	return m.withDO(m.DO.Session(config))
}

func (m ministryDo) Clauses(conds ...clause.Expression) IMinistryDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m ministryDo) Returning(value interface{}, columns ...string) IMinistryDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m ministryDo) Not(conds ...gen.Condition) IMinistryDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m ministryDo) Or(conds ...gen.Condition) IMinistryDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m ministryDo) Select(conds ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m ministryDo) Where(conds ...gen.Condition) IMinistryDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m ministryDo) Order(conds ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m ministryDo) Distinct(cols ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m ministryDo) Omit(cols ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m ministryDo) Join(table schema.Tabler, on ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m ministryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m ministryDo) RightJoin(table schema.Tabler, on ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m ministryDo) Group(cols ...field.Expr) IMinistryDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m ministryDo) Having(conds ...gen.Condition) IMinistryDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m ministryDo) Limit(limit int) IMinistryDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m ministryDo) Offset(offset int) IMinistryDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m ministryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMinistryDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m ministryDo) Unscoped() IMinistryDo {
	return m.withDO(m.DO.Unscoped())
}

func (m ministryDo) Create(values ...*models.Ministry) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m ministryDo) CreateInBatches(values []*models.Ministry, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m ministryDo) Save(values ...*models.Ministry) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m ministryDo) First() (*models.Ministry, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Ministry), nil
	}
}

func (m ministryDo) Take() (*models.Ministry, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Ministry), nil
	}
}

func (m ministryDo) Last() (*models.Ministry, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Ministry), nil
	}
}

func (m ministryDo) Find() ([]*models.Ministry, error) {
	result, err := m.DO.Find()
	return result.([]*models.Ministry), err
}

func (m ministryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Ministry, err error) {
	buf := make([]*models.Ministry, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m ministryDo) FindInBatches(result *[]*models.Ministry, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m ministryDo) Attrs(attrs ...field.AssignExpr) IMinistryDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m ministryDo) Assign(attrs ...field.AssignExpr) IMinistryDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m ministryDo) Joins(fields ...field.RelationField) IMinistryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m ministryDo) Preload(fields ...field.RelationField) IMinistryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m ministryDo) FirstOrInit() (*models.Ministry, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Ministry), nil
	}
}

func (m ministryDo) FirstOrCreate() (*models.Ministry, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Ministry), nil
	}
}

func (m ministryDo) FindByPage(offset int, limit int) (result []*models.Ministry, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m ministryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m ministryDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m ministryDo) Delete(models ...*models.Ministry) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *ministryDo) withDO(do gen.Dao) *ministryDo {
	m.DO = *do.(*gen.DO)
	return m
}
