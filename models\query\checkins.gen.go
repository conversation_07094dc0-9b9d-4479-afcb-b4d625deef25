// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newCheckin(db *gorm.DB, opts ...gen.DOOption) checkin {
	_checkin := checkin{}

	_checkin.checkinDo.UseDB(db, opts...)
	_checkin.checkinDo.UseModel(&models.Checkin{})

	tableName := _checkin.checkinDo.TableName()
	_checkin.ALL = field.NewAsterisk(tableName)
	_checkin.ID = field.NewString(tableName, "id")
	_checkin.CreatedAt = field.NewTime(tableName, "created_at")
	_checkin.UpdatedAt = field.NewTime(tableName, "updated_at")
	_checkin.UserId = field.NewString(tableName, "user_id")
	_checkin.Type = field.NewString(tableName, "type")
	_checkin.LeaveType = field.NewString(tableName, "leave_type")
	_checkin.Period = field.NewString(tableName, "period")
	_checkin.Location = field.NewString(tableName, "location")
	_checkin.Remarks = field.NewString(tableName, "remarks")
	_checkin.IsUnused = field.NewBool(tableName, "is_unused")
	_checkin.Date = field.NewTime(tableName, "date")
	_checkin.FirstCheckinAt = field.NewTime(tableName, "first_checkin_at")
	_checkin.User = checkinBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Checkins.User", "models.User"),
			},
		},
	}

	_checkin.fillFieldMap()

	return _checkin
}

type checkin struct {
	checkinDo

	ALL            field.Asterisk
	ID             field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time
	UserId         field.String
	Type           field.String
	LeaveType      field.String
	Period         field.String
	Location       field.String
	Remarks        field.String
	IsUnused       field.Bool
	Date           field.Time
	FirstCheckinAt field.Time
	User           checkinBelongsToUser

	fieldMap map[string]field.Expr
}

func (c checkin) Table(newTableName string) *checkin {
	c.checkinDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c checkin) As(alias string) *checkin {
	c.checkinDo.DO = *(c.checkinDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *checkin) updateTableName(table string) *checkin {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.UserId = field.NewString(table, "user_id")
	c.Type = field.NewString(table, "type")
	c.LeaveType = field.NewString(table, "leave_type")
	c.Period = field.NewString(table, "period")
	c.Location = field.NewString(table, "location")
	c.Remarks = field.NewString(table, "remarks")
	c.IsUnused = field.NewBool(table, "is_unused")
	c.Date = field.NewTime(table, "date")
	c.FirstCheckinAt = field.NewTime(table, "first_checkin_at")

	c.fillFieldMap()

	return c
}

func (c *checkin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *checkin) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["user_id"] = c.UserId
	c.fieldMap["type"] = c.Type
	c.fieldMap["leave_type"] = c.LeaveType
	c.fieldMap["period"] = c.Period
	c.fieldMap["location"] = c.Location
	c.fieldMap["remarks"] = c.Remarks
	c.fieldMap["is_unused"] = c.IsUnused
	c.fieldMap["date"] = c.Date
	c.fieldMap["first_checkin_at"] = c.FirstCheckinAt

}

func (c checkin) clone(db *gorm.DB) checkin {
	c.checkinDo.ReplaceConnPool(db.Statement.ConnPool)
	c.User.db = db.Session(&gorm.Session{Initialized: true})
	c.User.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c checkin) replaceDB(db *gorm.DB) checkin {
	c.checkinDo.ReplaceDB(db)
	c.User.db = db.Session(&gorm.Session{})
	return c
}

type checkinBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a checkinBelongsToUser) Where(conds ...field.Expr) *checkinBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a checkinBelongsToUser) WithContext(ctx context.Context) *checkinBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a checkinBelongsToUser) Session(session *gorm.Session) *checkinBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a checkinBelongsToUser) Model(m *models.Checkin) *checkinBelongsToUserTx {
	return &checkinBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a checkinBelongsToUser) Unscoped() *checkinBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type checkinBelongsToUserTx struct{ tx *gorm.Association }

func (a checkinBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a checkinBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a checkinBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a checkinBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a checkinBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a checkinBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a checkinBelongsToUserTx) Unscoped() *checkinBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type checkinDo struct{ gen.DO }

type ICheckinDo interface {
	gen.SubQuery
	Debug() ICheckinDo
	WithContext(ctx context.Context) ICheckinDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICheckinDo
	WriteDB() ICheckinDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICheckinDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICheckinDo
	Not(conds ...gen.Condition) ICheckinDo
	Or(conds ...gen.Condition) ICheckinDo
	Select(conds ...field.Expr) ICheckinDo
	Where(conds ...gen.Condition) ICheckinDo
	Order(conds ...field.Expr) ICheckinDo
	Distinct(cols ...field.Expr) ICheckinDo
	Omit(cols ...field.Expr) ICheckinDo
	Join(table schema.Tabler, on ...field.Expr) ICheckinDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICheckinDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICheckinDo
	Group(cols ...field.Expr) ICheckinDo
	Having(conds ...gen.Condition) ICheckinDo
	Limit(limit int) ICheckinDo
	Offset(offset int) ICheckinDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICheckinDo
	Unscoped() ICheckinDo
	Create(values ...*models.Checkin) error
	CreateInBatches(values []*models.Checkin, batchSize int) error
	Save(values ...*models.Checkin) error
	First() (*models.Checkin, error)
	Take() (*models.Checkin, error)
	Last() (*models.Checkin, error)
	Find() ([]*models.Checkin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Checkin, err error)
	FindInBatches(result *[]*models.Checkin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Checkin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICheckinDo
	Assign(attrs ...field.AssignExpr) ICheckinDo
	Joins(fields ...field.RelationField) ICheckinDo
	Preload(fields ...field.RelationField) ICheckinDo
	FirstOrInit() (*models.Checkin, error)
	FirstOrCreate() (*models.Checkin, error)
	FindByPage(offset int, limit int) (result []*models.Checkin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICheckinDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c checkinDo) Debug() ICheckinDo {
	return c.withDO(c.DO.Debug())
}

func (c checkinDo) WithContext(ctx context.Context) ICheckinDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c checkinDo) ReadDB() ICheckinDo {
	return c.Clauses(dbresolver.Read)
}

func (c checkinDo) WriteDB() ICheckinDo {
	return c.Clauses(dbresolver.Write)
}

func (c checkinDo) Session(config *gorm.Session) ICheckinDo {
	return c.withDO(c.DO.Session(config))
}

func (c checkinDo) Clauses(conds ...clause.Expression) ICheckinDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c checkinDo) Returning(value interface{}, columns ...string) ICheckinDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c checkinDo) Not(conds ...gen.Condition) ICheckinDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c checkinDo) Or(conds ...gen.Condition) ICheckinDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c checkinDo) Select(conds ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c checkinDo) Where(conds ...gen.Condition) ICheckinDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c checkinDo) Order(conds ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c checkinDo) Distinct(cols ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c checkinDo) Omit(cols ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c checkinDo) Join(table schema.Tabler, on ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c checkinDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c checkinDo) RightJoin(table schema.Tabler, on ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c checkinDo) Group(cols ...field.Expr) ICheckinDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c checkinDo) Having(conds ...gen.Condition) ICheckinDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c checkinDo) Limit(limit int) ICheckinDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c checkinDo) Offset(offset int) ICheckinDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c checkinDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICheckinDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c checkinDo) Unscoped() ICheckinDo {
	return c.withDO(c.DO.Unscoped())
}

func (c checkinDo) Create(values ...*models.Checkin) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c checkinDo) CreateInBatches(values []*models.Checkin, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c checkinDo) Save(values ...*models.Checkin) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c checkinDo) First() (*models.Checkin, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Checkin), nil
	}
}

func (c checkinDo) Take() (*models.Checkin, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Checkin), nil
	}
}

func (c checkinDo) Last() (*models.Checkin, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Checkin), nil
	}
}

func (c checkinDo) Find() ([]*models.Checkin, error) {
	result, err := c.DO.Find()
	return result.([]*models.Checkin), err
}

func (c checkinDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Checkin, err error) {
	buf := make([]*models.Checkin, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c checkinDo) FindInBatches(result *[]*models.Checkin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c checkinDo) Attrs(attrs ...field.AssignExpr) ICheckinDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c checkinDo) Assign(attrs ...field.AssignExpr) ICheckinDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c checkinDo) Joins(fields ...field.RelationField) ICheckinDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c checkinDo) Preload(fields ...field.RelationField) ICheckinDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c checkinDo) FirstOrInit() (*models.Checkin, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Checkin), nil
	}
}

func (c checkinDo) FirstOrCreate() (*models.Checkin, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Checkin), nil
	}
}

func (c checkinDo) FindByPage(offset int, limit int) (result []*models.Checkin, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c checkinDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c checkinDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c checkinDo) Delete(models ...*models.Checkin) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *checkinDo) withDO(do gen.Dao) *checkinDo {
	c.DO = *do.(*gen.DO)
	return c
}
