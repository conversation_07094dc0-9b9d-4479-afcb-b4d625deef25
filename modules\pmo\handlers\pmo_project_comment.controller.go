package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectCommentController struct {
}

// PMO Project Comments methods
func (m PMOProjectCommentController) CommentsPagination(c core.IHTTPContext) error {
	input := &requests.PMOCommentPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	res, ierr := pmoProjectSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOCommentPaginationOptions{
		Channel:  input.Channel,
		ParentID: input.ParentID,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectCommentController) CommentsFind(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	comment, err := pmoProjectSvc.Find(c.Param("comment_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, comment)
}

func (m PMOProjectCommentController) CommentsCreate(c core.IHTTPContext) error {
	input := &requests.PMOCommentCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	payload := &dto.PMOCommentCreatePayload{
		ProjectID:       c.Param("id"), // Set project ID from URL parameter
		Channel:         models.PMOCommentChannel(utils.ToNonPointer(input.Channel)),
		Detail:          utils.ToNonPointer(input.Detail),
		IsClientFlag:    utils.ToNonPointer(input.IsClientFlag),
		ParentCommentID: input.ParentCommentID,
	}

	comment, err := pmoProjectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, comment)
}

func (m PMOProjectCommentController) CommentsUpdate(c core.IHTTPContext) error {
	input := &requests.PMOCommentUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	payload := &dto.PMOCommentUpdatePayload{
		Detail:       utils.ToNonPointer(input.Detail),
		IsClientFlag: input.IsClientFlag,
	}

	comment, err := pmoProjectSvc.Update(c.Param("comment_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, comment)
}

func (m PMOProjectCommentController) CommentsDelete(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	err := pmoProjectSvc.Delete(c.Param("comment_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m PMOProjectCommentController) CommentsVersionsPagination(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	res, ierr := pmoProjectSvc.VersionsPagination(c.Param("comment_id"), c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectCommentController) CommentsVersionsFind(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectCommentService(c)
	version, err := pmoProjectSvc.VersionsFind(c.Param("version_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, version)
}
