// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBiddingInfo(db *gorm.DB, opts ...gen.DOOption) pMOBiddingInfo {
	_pMOBiddingInfo := pMOBiddingInfo{}

	_pMOBiddingInfo.pMOBiddingInfoDo.UseDB(db, opts...)
	_pMOBiddingInfo.pMOBiddingInfoDo.UseModel(&models.PMOBiddingInfo{})

	tableName := _pMOBiddingInfo.pMOBiddingInfoDo.TableName()
	_pMOBiddingInfo.ALL = field.NewAsterisk(tableName)
	_pMOBiddingInfo.ID = field.NewString(tableName, "id")
	_pMOBiddingInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBiddingInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBiddingInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBiddingInfo.ProjectID = field.NewString(tableName, "project_id")
	_pMOBiddingInfo.BiddingType = field.NewString(tableName, "bidding_type")
	_pMOBiddingInfo.BiddingValue = field.NewFloat64(tableName, "bidding_value")
	_pMOBiddingInfo.TenderDate = field.NewTime(tableName, "tender_date")
	_pMOBiddingInfo.TenderEntity = field.NewString(tableName, "tender_entity")
	_pMOBiddingInfo.AnnounceDate = field.NewTime(tableName, "announce_date")
	_pMOBiddingInfo.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBiddingInfo.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBiddingInfo.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBiddingInfo.Project = pMOBiddingInfoHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBiddingInfo.CreatedBy = pMOBiddingInfoBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBiddingInfo.UpdatedBy = pMOBiddingInfoBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBiddingInfo.fillFieldMap()

	return _pMOBiddingInfo
}

type pMOBiddingInfo struct {
	pMOBiddingInfoDo

	ALL          field.Asterisk
	ID           field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ProjectID    field.String
	BiddingType  field.String
	BiddingValue field.Float64
	TenderDate   field.Time
	TenderEntity field.String
	AnnounceDate field.Time
	CreatedByID  field.String
	UpdatedByID  field.String
	DeletedByID  field.String
	Project      pMOBiddingInfoHasOneProject

	CreatedBy pMOBiddingInfoBelongsToCreatedBy

	UpdatedBy pMOBiddingInfoBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBiddingInfo) Table(newTableName string) *pMOBiddingInfo {
	p.pMOBiddingInfoDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBiddingInfo) As(alias string) *pMOBiddingInfo {
	p.pMOBiddingInfoDo.DO = *(p.pMOBiddingInfoDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBiddingInfo) updateTableName(table string) *pMOBiddingInfo {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.BiddingType = field.NewString(table, "bidding_type")
	p.BiddingValue = field.NewFloat64(table, "bidding_value")
	p.TenderDate = field.NewTime(table, "tender_date")
	p.TenderEntity = field.NewString(table, "tender_entity")
	p.AnnounceDate = field.NewTime(table, "announce_date")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBiddingInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBiddingInfo) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["bidding_type"] = p.BiddingType
	p.fieldMap["bidding_value"] = p.BiddingValue
	p.fieldMap["tender_date"] = p.TenderDate
	p.fieldMap["tender_entity"] = p.TenderEntity
	p.fieldMap["announce_date"] = p.AnnounceDate
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOBiddingInfo) clone(db *gorm.DB) pMOBiddingInfo {
	p.pMOBiddingInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBiddingInfo) replaceDB(db *gorm.DB) pMOBiddingInfo {
	p.pMOBiddingInfoDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBiddingInfoHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBiddingInfoHasOneProject) Where(conds ...field.Expr) *pMOBiddingInfoHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoHasOneProject) WithContext(ctx context.Context) *pMOBiddingInfoHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoHasOneProject) Session(session *gorm.Session) *pMOBiddingInfoHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoHasOneProject) Model(m *models.PMOBiddingInfo) *pMOBiddingInfoHasOneProjectTx {
	return &pMOBiddingInfoHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoHasOneProject) Unscoped() *pMOBiddingInfoHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoHasOneProjectTx) Unscoped() *pMOBiddingInfoHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBiddingInfoBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBiddingInfoBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBiddingInfoBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoBelongsToCreatedBy) Session(session *gorm.Session) *pMOBiddingInfoBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoBelongsToCreatedBy) Model(m *models.PMOBiddingInfo) *pMOBiddingInfoBelongsToCreatedByTx {
	return &pMOBiddingInfoBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoBelongsToCreatedBy) Unscoped() *pMOBiddingInfoBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoBelongsToCreatedByTx) Unscoped() *pMOBiddingInfoBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBiddingInfoBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBiddingInfoBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBiddingInfoBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBiddingInfoBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBiddingInfoBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBiddingInfoBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBiddingInfoBelongsToUpdatedBy) Model(m *models.PMOBiddingInfo) *pMOBiddingInfoBelongsToUpdatedByTx {
	return &pMOBiddingInfoBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBiddingInfoBelongsToUpdatedBy) Unscoped() *pMOBiddingInfoBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBiddingInfoBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBiddingInfoBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBiddingInfoBelongsToUpdatedByTx) Unscoped() *pMOBiddingInfoBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBiddingInfoDo struct{ gen.DO }

type IPMOBiddingInfoDo interface {
	gen.SubQuery
	Debug() IPMOBiddingInfoDo
	WithContext(ctx context.Context) IPMOBiddingInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBiddingInfoDo
	WriteDB() IPMOBiddingInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBiddingInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBiddingInfoDo
	Not(conds ...gen.Condition) IPMOBiddingInfoDo
	Or(conds ...gen.Condition) IPMOBiddingInfoDo
	Select(conds ...field.Expr) IPMOBiddingInfoDo
	Where(conds ...gen.Condition) IPMOBiddingInfoDo
	Order(conds ...field.Expr) IPMOBiddingInfoDo
	Distinct(cols ...field.Expr) IPMOBiddingInfoDo
	Omit(cols ...field.Expr) IPMOBiddingInfoDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo
	Group(cols ...field.Expr) IPMOBiddingInfoDo
	Having(conds ...gen.Condition) IPMOBiddingInfoDo
	Limit(limit int) IPMOBiddingInfoDo
	Offset(offset int) IPMOBiddingInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBiddingInfoDo
	Unscoped() IPMOBiddingInfoDo
	Create(values ...*models.PMOBiddingInfo) error
	CreateInBatches(values []*models.PMOBiddingInfo, batchSize int) error
	Save(values ...*models.PMOBiddingInfo) error
	First() (*models.PMOBiddingInfo, error)
	Take() (*models.PMOBiddingInfo, error)
	Last() (*models.PMOBiddingInfo, error)
	Find() ([]*models.PMOBiddingInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBiddingInfo, err error)
	FindInBatches(result *[]*models.PMOBiddingInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBiddingInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBiddingInfoDo
	Assign(attrs ...field.AssignExpr) IPMOBiddingInfoDo
	Joins(fields ...field.RelationField) IPMOBiddingInfoDo
	Preload(fields ...field.RelationField) IPMOBiddingInfoDo
	FirstOrInit() (*models.PMOBiddingInfo, error)
	FirstOrCreate() (*models.PMOBiddingInfo, error)
	FindByPage(offset int, limit int) (result []*models.PMOBiddingInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBiddingInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBiddingInfoDo) Debug() IPMOBiddingInfoDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBiddingInfoDo) WithContext(ctx context.Context) IPMOBiddingInfoDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBiddingInfoDo) ReadDB() IPMOBiddingInfoDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBiddingInfoDo) WriteDB() IPMOBiddingInfoDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBiddingInfoDo) Session(config *gorm.Session) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBiddingInfoDo) Clauses(conds ...clause.Expression) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBiddingInfoDo) Returning(value interface{}, columns ...string) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBiddingInfoDo) Not(conds ...gen.Condition) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBiddingInfoDo) Or(conds ...gen.Condition) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBiddingInfoDo) Select(conds ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBiddingInfoDo) Where(conds ...gen.Condition) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBiddingInfoDo) Order(conds ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBiddingInfoDo) Distinct(cols ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBiddingInfoDo) Omit(cols ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBiddingInfoDo) Join(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBiddingInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBiddingInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBiddingInfoDo) Group(cols ...field.Expr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBiddingInfoDo) Having(conds ...gen.Condition) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBiddingInfoDo) Limit(limit int) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBiddingInfoDo) Offset(offset int) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBiddingInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBiddingInfoDo) Unscoped() IPMOBiddingInfoDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBiddingInfoDo) Create(values ...*models.PMOBiddingInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBiddingInfoDo) CreateInBatches(values []*models.PMOBiddingInfo, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBiddingInfoDo) Save(values ...*models.PMOBiddingInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBiddingInfoDo) First() (*models.PMOBiddingInfo, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfo), nil
	}
}

func (p pMOBiddingInfoDo) Take() (*models.PMOBiddingInfo, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfo), nil
	}
}

func (p pMOBiddingInfoDo) Last() (*models.PMOBiddingInfo, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfo), nil
	}
}

func (p pMOBiddingInfoDo) Find() ([]*models.PMOBiddingInfo, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBiddingInfo), err
}

func (p pMOBiddingInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBiddingInfo, err error) {
	buf := make([]*models.PMOBiddingInfo, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBiddingInfoDo) FindInBatches(result *[]*models.PMOBiddingInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBiddingInfoDo) Attrs(attrs ...field.AssignExpr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBiddingInfoDo) Assign(attrs ...field.AssignExpr) IPMOBiddingInfoDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBiddingInfoDo) Joins(fields ...field.RelationField) IPMOBiddingInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBiddingInfoDo) Preload(fields ...field.RelationField) IPMOBiddingInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBiddingInfoDo) FirstOrInit() (*models.PMOBiddingInfo, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfo), nil
	}
}

func (p pMOBiddingInfoDo) FirstOrCreate() (*models.PMOBiddingInfo, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBiddingInfo), nil
	}
}

func (p pMOBiddingInfoDo) FindByPage(offset int, limit int) (result []*models.PMOBiddingInfo, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBiddingInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBiddingInfoDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBiddingInfoDo) Delete(models ...*models.PMOBiddingInfo) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBiddingInfoDo) withDO(do gen.Dao) *pMOBiddingInfoDo {
	p.DO = *do.(*gen.DO)
	return p
}
