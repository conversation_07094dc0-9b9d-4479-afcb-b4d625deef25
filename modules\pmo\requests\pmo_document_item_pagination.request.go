package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentItemPaginationRequest struct {
	core.BaseValidator
	TabKey  *string `json:"tab_key" query:"tab_key"`
	GroupID *string `json:"group_id" query:"group_id"`
	Type    *string `json:"type" query:"type"`
}

func (r *PMODocumentItemPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Tab<PERSON>ey, strings.Join(models.PMOTabKeys, "|"), "tab_key"))
	r.Must(r.IsStrIn(r.Type, strings.Join(models.PMODocTypes, "|"), "type"))

	r.Must(r.IsExists(ctx, r.GroupID, models.PMODocumentGroup{}.TableName(), "id", "group_id"))

	return r.<PERSON>()
}
