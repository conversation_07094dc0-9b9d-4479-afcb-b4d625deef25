package core

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/messaging"
	"google.golang.org/api/option"
)

var FCMServiceConnectError = Error{
	Status:  http.StatusInternalServerError,
	Code:    "FCM_CONNECT_ERROR",
	Message: "failure to connect to fcm service",
}

func FCMServiceSendError(err error) Error {
	return Error{
		Status:  http.StatusInternalServerError,
		Code:    "FCM_SEND_ERROR",
		Message: err,
	}
}

type IFMCMessage struct {
	BaseValidator
	Title    string `json:"title"`
	Body     string `json:"body"`
	ImageURL string `json:"image_url"`

	Data map[string]string `json:"data"`
}

func (r *IFMCMessage) Valid(ctx IContext) IError {
	r.Must(r.IsStrRequired(&r.Title, "title"))
	r.Must(r.<PERSON>equired(&r.<PERSON>, "body"))
	r.Must(r.<PERSON>equired(&r.<PERSON>, "data"))

	return r.Error()
}

type IFMCPayload struct {
	Token   string       `json:"token"`
	Message *IFMCMessage `json:"payload"`
}

type IFMCResponse struct {
	MulticastID  int64                `json:"multicast_id"`
	Success      int                  `json:"success"`
	Failure      int                  `json:"failure"`
	CanonicalIDs int                  `json:"canonical_ids"`
	Results      []IFMCResponseResult `json:"results"`
}

type IFMCResponseResult struct {
	MessageID string `json:"message_id"`
	Error     string `json:"error"`
}

type IFMC interface {
	SendSimpleMessage(tokens []string, payload *IFMCMessage) IError
	SendSimpleMessages(payload []IFMCPayload) IError
	SendTopic(topic string, payload map[string]string) IError
	SendSimpleMessageLegacy(token string, payload *IFMCMessage) IError
}

type fmcService struct {
	ctx    IContext
	path   string
	client *messaging.Client
}

func NewFMC(ctx IContext) IFMC {
	return &fmcService{
		ctx:  ctx,
		path: fmt.Sprintf(`/%s`, "firebase.json"),
	}
}

func NewFMCWithPath(ctx IContext, path string) IFMC {
	return &fmcService{
		ctx:  ctx,
		path: path,
	}
}

func (s *fmcService) connect() IError {
	if s.client != nil {
		return nil
	}

	var opt option.ClientOption
	if len(s.ctx.ENV().Config().FirebaseCredential) > 0 {
		opt = option.WithCredentialsJSON([]byte(s.ctx.ENV().Config().FirebaseCredential))
	} else {
		log.Println("firebase path", s.path)
		opt = option.WithCredentialsFile(s.path)
	}

	ctx, cancel := s.getContext()
	defer cancel()

	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		return s.ctx.NewError(err, FCMServiceConnectError)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		return s.ctx.NewError(err, FCMServiceConnectError)
	}

	s.client = client
	return nil
}

func (s *fmcService) SendSimpleMessage(tokens []string, message *IFMCMessage) IError {
	ierr := s.connect()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	_message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title:    message.Title,
			Body:     message.Body,
			ImageURL: message.ImageURL,
		},
		Data:   message.Data,
		Tokens: tokens,
	}

	ctx, cancel := s.getContext()
	defer cancel()

	batch, err := s.client.SendMulticast(ctx, _message)
	if err != nil {
		return s.ctx.NewError(err, FCMServiceSendError(err))
	}

	for i := range batch.Responses {
		if !batch.Responses[i].Success {
			return s.ctx.NewError(batch.Responses[i].Error, FCMServiceSendError(batch.Responses[i].Error))
		}
	}

	return nil
}

func (s *fmcService) SendSimpleMessages(payload []IFMCPayload) IError {
	ierr := s.connect()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	messages := make([]*messaging.Message, 0)
	for _, p := range payload {
		messages = append(messages, &messaging.Message{
			Notification: &messaging.Notification{
				Title:    p.Message.Title,
				Body:     p.Message.Body,
				ImageURL: p.Message.ImageURL,
			},
			Data:  p.Message.Data,
			Token: p.Token,
		})

	}

	ctx, cancel := s.getContext()
	defer cancel()

	batch, err := s.client.SendAll(ctx, messages)
	if err != nil {
		return s.ctx.NewError(err, FCMServiceSendError(err))
	}

	for i := range batch.Responses {
		if !batch.Responses[i].Success {
			return s.ctx.NewError(batch.Responses[i].Error, FCMServiceSendError(batch.Responses[i].Error))
		}
	}

	return nil
}

func (s *fmcService) SendTopic(topic string, payload map[string]string) IError {
	ierr := s.connect()
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	message := &messaging.Message{
		Data:  payload,
		Topic: topic,
	}

	ctx, cancel := s.getContext()
	defer cancel()

	_, err := s.client.Send(ctx, message)
	if err != nil {
		return s.ctx.NewError(err, FCMServiceSendError(err))
	}

	return nil
}
func (s *fmcService) SendSimpleMessageLegacy(token string, payload *IFMCMessage) IError {
	if ierr := payload.Valid(s.ctx); ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	result := &IFMCResponse{}
	payload.Data["title"] = payload.Title
	payload.Data["description"] = payload.Body

	ierr := RequesterToStruct(&result, func() (*RequestResponse, error) {
		return s.ctx.Requester().Post("/fcm/send", map[string]interface{}{
			"to":   token,
			"data": payload.Data,
		}, &RequesterOptions{
			BaseURL: "https://fcm.googleapis.com",
			Headers: map[string][]string{
				"Content-Type":  {"application/json"},
				"Authorization": {fmt.Sprintf(`key=%s`, "AAAA7OP2MGI:APA91bGBiEqUCR2shxwGswgTqzuordFT6EmS5039EwAdtzm7OIS9AFAZe68T8E8kgvYWWsAvxBlvu5heteZaRSRtOyu6EpEb-TQCYTcm2NEfD6Xpavd8OduWO_DKtnYmu1I_6bdncXEV")},
			},
		})
	})
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	if result.Failure > 0 {
		for i := range result.Results {
			if result.Results[i].Error != "" {
				return s.ctx.NewError(errors.New(result.Results[i].Error), FCMServiceSendError(errors.New(result.Results[i].Error)))
			}
		}
	}

	return nil
}

func (s *fmcService) getContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), 10*time.Second)
}
