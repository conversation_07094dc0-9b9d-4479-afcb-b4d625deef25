// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOLGInfoVersion(db *gorm.DB, opts ...gen.DOOption) pMOLGInfoVersion {
	_pMOLGInfoVersion := pMOLGInfoVersion{}

	_pMOLGInfoVersion.pMOLGInfoVersionDo.UseDB(db, opts...)
	_pMOLGInfoVersion.pMOLGInfoVersionDo.UseModel(&models.PMOLGInfoVersion{})

	tableName := _pMOLGInfoVersion.pMOLGInfoVersionDo.TableName()
	_pMOLGInfoVersion.ALL = field.NewAsterisk(tableName)
	_pMOLGInfoVersion.ID = field.NewString(tableName, "id")
	_pMOLGInfoVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOLGInfoVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOLGInfoVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOLGInfoVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMOLGInfoVersion.Value = field.NewFloat64(tableName, "value")
	_pMOLGInfoVersion.StartDate = field.NewTime(tableName, "start_date")
	_pMOLGInfoVersion.EndDate = field.NewTime(tableName, "end_date")
	_pMOLGInfoVersion.Fee = field.NewFloat64(tableName, "fee")
	_pMOLGInfoVersion.Interest = field.NewFloat64(tableName, "interest")
	_pMOLGInfoVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOLGInfoVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOLGInfoVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOLGInfoVersion.OriginalID = field.NewString(tableName, "lg_info_id")
	_pMOLGInfoVersion.Project = pMOLGInfoVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOLGInfoVersion.CreatedBy = pMOLGInfoVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOLGInfoVersion.UpdatedBy = pMOLGInfoVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOLGInfoVersion.fillFieldMap()

	return _pMOLGInfoVersion
}

type pMOLGInfoVersion struct {
	pMOLGInfoVersionDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	ProjectID   field.String
	Value       field.Float64
	StartDate   field.Time
	EndDate     field.Time
	Fee         field.Float64
	Interest    field.Float64
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	OriginalID  field.String
	Project     pMOLGInfoVersionHasOneProject

	CreatedBy pMOLGInfoVersionBelongsToCreatedBy

	UpdatedBy pMOLGInfoVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOLGInfoVersion) Table(newTableName string) *pMOLGInfoVersion {
	p.pMOLGInfoVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOLGInfoVersion) As(alias string) *pMOLGInfoVersion {
	p.pMOLGInfoVersionDo.DO = *(p.pMOLGInfoVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOLGInfoVersion) updateTableName(table string) *pMOLGInfoVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.Value = field.NewFloat64(table, "value")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.Fee = field.NewFloat64(table, "fee")
	p.Interest = field.NewFloat64(table, "interest")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "lg_info_id")

	p.fillFieldMap()

	return p
}

func (p *pMOLGInfoVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOLGInfoVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 17)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["value"] = p.Value
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["fee"] = p.Fee
	p.fieldMap["interest"] = p.Interest
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["lg_info_id"] = p.OriginalID

}

func (p pMOLGInfoVersion) clone(db *gorm.DB) pMOLGInfoVersion {
	p.pMOLGInfoVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOLGInfoVersion) replaceDB(db *gorm.DB) pMOLGInfoVersion {
	p.pMOLGInfoVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOLGInfoVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOLGInfoVersionHasOneProject) Where(conds ...field.Expr) *pMOLGInfoVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoVersionHasOneProject) WithContext(ctx context.Context) *pMOLGInfoVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoVersionHasOneProject) Session(session *gorm.Session) *pMOLGInfoVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoVersionHasOneProject) Model(m *models.PMOLGInfoVersion) *pMOLGInfoVersionHasOneProjectTx {
	return &pMOLGInfoVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoVersionHasOneProject) Unscoped() *pMOLGInfoVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOLGInfoVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoVersionHasOneProjectTx) Unscoped() *pMOLGInfoVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOLGInfoVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMOLGInfoVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMOLGInfoVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMOLGInfoVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoVersionBelongsToCreatedBy) Model(m *models.PMOLGInfoVersion) *pMOLGInfoVersionBelongsToCreatedByTx {
	return &pMOLGInfoVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoVersionBelongsToCreatedBy) Unscoped() *pMOLGInfoVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOLGInfoVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoVersionBelongsToCreatedByTx) Unscoped() *pMOLGInfoVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOLGInfoVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOLGInfoVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOLGInfoVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOLGInfoVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOLGInfoVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMOLGInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOLGInfoVersionBelongsToUpdatedBy) Model(m *models.PMOLGInfoVersion) *pMOLGInfoVersionBelongsToUpdatedByTx {
	return &pMOLGInfoVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOLGInfoVersionBelongsToUpdatedBy) Unscoped() *pMOLGInfoVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOLGInfoVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOLGInfoVersionBelongsToUpdatedByTx) Unscoped() *pMOLGInfoVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOLGInfoVersionDo struct{ gen.DO }

type IPMOLGInfoVersionDo interface {
	gen.SubQuery
	Debug() IPMOLGInfoVersionDo
	WithContext(ctx context.Context) IPMOLGInfoVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOLGInfoVersionDo
	WriteDB() IPMOLGInfoVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOLGInfoVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOLGInfoVersionDo
	Not(conds ...gen.Condition) IPMOLGInfoVersionDo
	Or(conds ...gen.Condition) IPMOLGInfoVersionDo
	Select(conds ...field.Expr) IPMOLGInfoVersionDo
	Where(conds ...gen.Condition) IPMOLGInfoVersionDo
	Order(conds ...field.Expr) IPMOLGInfoVersionDo
	Distinct(cols ...field.Expr) IPMOLGInfoVersionDo
	Omit(cols ...field.Expr) IPMOLGInfoVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo
	Group(cols ...field.Expr) IPMOLGInfoVersionDo
	Having(conds ...gen.Condition) IPMOLGInfoVersionDo
	Limit(limit int) IPMOLGInfoVersionDo
	Offset(offset int) IPMOLGInfoVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOLGInfoVersionDo
	Unscoped() IPMOLGInfoVersionDo
	Create(values ...*models.PMOLGInfoVersion) error
	CreateInBatches(values []*models.PMOLGInfoVersion, batchSize int) error
	Save(values ...*models.PMOLGInfoVersion) error
	First() (*models.PMOLGInfoVersion, error)
	Take() (*models.PMOLGInfoVersion, error)
	Last() (*models.PMOLGInfoVersion, error)
	Find() ([]*models.PMOLGInfoVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOLGInfoVersion, err error)
	FindInBatches(result *[]*models.PMOLGInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOLGInfoVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOLGInfoVersionDo
	Assign(attrs ...field.AssignExpr) IPMOLGInfoVersionDo
	Joins(fields ...field.RelationField) IPMOLGInfoVersionDo
	Preload(fields ...field.RelationField) IPMOLGInfoVersionDo
	FirstOrInit() (*models.PMOLGInfoVersion, error)
	FirstOrCreate() (*models.PMOLGInfoVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMOLGInfoVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOLGInfoVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOLGInfoVersionDo) Debug() IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOLGInfoVersionDo) WithContext(ctx context.Context) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOLGInfoVersionDo) ReadDB() IPMOLGInfoVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOLGInfoVersionDo) WriteDB() IPMOLGInfoVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOLGInfoVersionDo) Session(config *gorm.Session) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOLGInfoVersionDo) Clauses(conds ...clause.Expression) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOLGInfoVersionDo) Returning(value interface{}, columns ...string) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOLGInfoVersionDo) Not(conds ...gen.Condition) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOLGInfoVersionDo) Or(conds ...gen.Condition) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOLGInfoVersionDo) Select(conds ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOLGInfoVersionDo) Where(conds ...gen.Condition) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOLGInfoVersionDo) Order(conds ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOLGInfoVersionDo) Distinct(cols ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOLGInfoVersionDo) Omit(cols ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOLGInfoVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOLGInfoVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOLGInfoVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOLGInfoVersionDo) Group(cols ...field.Expr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOLGInfoVersionDo) Having(conds ...gen.Condition) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOLGInfoVersionDo) Limit(limit int) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOLGInfoVersionDo) Offset(offset int) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOLGInfoVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOLGInfoVersionDo) Unscoped() IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOLGInfoVersionDo) Create(values ...*models.PMOLGInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOLGInfoVersionDo) CreateInBatches(values []*models.PMOLGInfoVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOLGInfoVersionDo) Save(values ...*models.PMOLGInfoVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOLGInfoVersionDo) First() (*models.PMOLGInfoVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfoVersion), nil
	}
}

func (p pMOLGInfoVersionDo) Take() (*models.PMOLGInfoVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfoVersion), nil
	}
}

func (p pMOLGInfoVersionDo) Last() (*models.PMOLGInfoVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfoVersion), nil
	}
}

func (p pMOLGInfoVersionDo) Find() ([]*models.PMOLGInfoVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOLGInfoVersion), err
}

func (p pMOLGInfoVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOLGInfoVersion, err error) {
	buf := make([]*models.PMOLGInfoVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOLGInfoVersionDo) FindInBatches(result *[]*models.PMOLGInfoVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOLGInfoVersionDo) Attrs(attrs ...field.AssignExpr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOLGInfoVersionDo) Assign(attrs ...field.AssignExpr) IPMOLGInfoVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOLGInfoVersionDo) Joins(fields ...field.RelationField) IPMOLGInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOLGInfoVersionDo) Preload(fields ...field.RelationField) IPMOLGInfoVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOLGInfoVersionDo) FirstOrInit() (*models.PMOLGInfoVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfoVersion), nil
	}
}

func (p pMOLGInfoVersionDo) FirstOrCreate() (*models.PMOLGInfoVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOLGInfoVersion), nil
	}
}

func (p pMOLGInfoVersionDo) FindByPage(offset int, limit int) (result []*models.PMOLGInfoVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOLGInfoVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOLGInfoVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOLGInfoVersionDo) Delete(models ...*models.PMOLGInfoVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOLGInfoVersionDo) withDO(do gen.Dao) *pMOLGInfoVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
