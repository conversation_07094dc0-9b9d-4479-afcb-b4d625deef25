// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOCollaborator(db *gorm.DB, opts ...gen.DOOption) pMOCollaborator {
	_pMOCollaborator := pMOCollaborator{}

	_pMOCollaborator.pMOCollaboratorDo.UseDB(db, opts...)
	_pMOCollaborator.pMOCollaboratorDo.UseModel(&models.PMOCollaborator{})

	tableName := _pMOCollaborator.pMOCollaboratorDo.TableName()
	_pMOCollaborator.ALL = field.NewAsterisk(tableName)
	_pMOCollaborator.ID = field.NewString(tableName, "id")
	_pMOCollaborator.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOCollaborator.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOCollaborator.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOCollaborator.ProjectID = field.NewString(tableName, "project_id")
	_pMOCollaborator.UserID = field.NewString(tableName, "user_id")
	_pMOCollaborator.ConfidentialPermission = field.NewString(tableName, "confidential_permission")
	_pMOCollaborator.ConfidentialMain = field.NewBool(tableName, "confidential_main")
	_pMOCollaborator.SalesPermission = field.NewString(tableName, "sales_permission")
	_pMOCollaborator.SalesMain = field.NewBool(tableName, "sales_main")
	_pMOCollaborator.PresalesPermission = field.NewString(tableName, "presales_permission")
	_pMOCollaborator.PresalesMain = field.NewBool(tableName, "presales_main")
	_pMOCollaborator.BiddingPermission = field.NewString(tableName, "bidding_permission")
	_pMOCollaborator.BiddingMain = field.NewBool(tableName, "bidding_main")
	_pMOCollaborator.PMOPermission = field.NewString(tableName, "pmo_permission")
	_pMOCollaborator.PMOMain = field.NewBool(tableName, "pmo_main")
	_pMOCollaborator.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOCollaborator.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOCollaborator.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOCollaborator.Project = pMOCollaboratorHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOCollaborator.CreatedBy = pMOCollaboratorBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOCollaborator.UpdatedBy = pMOCollaboratorBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOCollaborator.User = pMOCollaboratorBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
	}

	_pMOCollaborator.fillFieldMap()

	return _pMOCollaborator
}

type pMOCollaborator struct {
	pMOCollaboratorDo

	ALL                    field.Asterisk
	ID                     field.String
	CreatedAt              field.Time
	UpdatedAt              field.Time
	DeletedAt              field.Field
	ProjectID              field.String
	UserID                 field.String
	ConfidentialPermission field.String
	ConfidentialMain       field.Bool
	SalesPermission        field.String
	SalesMain              field.Bool
	PresalesPermission     field.String
	PresalesMain           field.Bool
	BiddingPermission      field.String
	BiddingMain            field.Bool
	PMOPermission          field.String
	PMOMain                field.Bool
	CreatedByID            field.String
	UpdatedByID            field.String
	DeletedByID            field.String
	Project                pMOCollaboratorHasOneProject

	CreatedBy pMOCollaboratorBelongsToCreatedBy

	UpdatedBy pMOCollaboratorBelongsToUpdatedBy

	User pMOCollaboratorBelongsToUser

	fieldMap map[string]field.Expr
}

func (p pMOCollaborator) Table(newTableName string) *pMOCollaborator {
	p.pMOCollaboratorDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOCollaborator) As(alias string) *pMOCollaborator {
	p.pMOCollaboratorDo.DO = *(p.pMOCollaboratorDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOCollaborator) updateTableName(table string) *pMOCollaborator {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.UserID = field.NewString(table, "user_id")
	p.ConfidentialPermission = field.NewString(table, "confidential_permission")
	p.ConfidentialMain = field.NewBool(table, "confidential_main")
	p.SalesPermission = field.NewString(table, "sales_permission")
	p.SalesMain = field.NewBool(table, "sales_main")
	p.PresalesPermission = field.NewString(table, "presales_permission")
	p.PresalesMain = field.NewBool(table, "presales_main")
	p.BiddingPermission = field.NewString(table, "bidding_permission")
	p.BiddingMain = field.NewBool(table, "bidding_main")
	p.PMOPermission = field.NewString(table, "pmo_permission")
	p.PMOMain = field.NewBool(table, "pmo_main")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOCollaborator) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOCollaborator) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 23)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["confidential_permission"] = p.ConfidentialPermission
	p.fieldMap["confidential_main"] = p.ConfidentialMain
	p.fieldMap["sales_permission"] = p.SalesPermission
	p.fieldMap["sales_main"] = p.SalesMain
	p.fieldMap["presales_permission"] = p.PresalesPermission
	p.fieldMap["presales_main"] = p.PresalesMain
	p.fieldMap["bidding_permission"] = p.BiddingPermission
	p.fieldMap["bidding_main"] = p.BiddingMain
	p.fieldMap["pmo_permission"] = p.PMOPermission
	p.fieldMap["pmo_main"] = p.PMOMain
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOCollaborator) clone(db *gorm.DB) pMOCollaborator {
	p.pMOCollaboratorDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.User.db = db.Session(&gorm.Session{Initialized: true})
	p.User.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOCollaborator) replaceDB(db *gorm.DB) pMOCollaborator {
	p.pMOCollaboratorDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	p.User.db = db.Session(&gorm.Session{})
	return p
}

type pMOCollaboratorHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOCollaboratorHasOneProject) Where(conds ...field.Expr) *pMOCollaboratorHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCollaboratorHasOneProject) WithContext(ctx context.Context) *pMOCollaboratorHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCollaboratorHasOneProject) Session(session *gorm.Session) *pMOCollaboratorHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCollaboratorHasOneProject) Model(m *models.PMOCollaborator) *pMOCollaboratorHasOneProjectTx {
	return &pMOCollaboratorHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCollaboratorHasOneProject) Unscoped() *pMOCollaboratorHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCollaboratorHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOCollaboratorHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCollaboratorHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCollaboratorHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCollaboratorHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCollaboratorHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCollaboratorHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCollaboratorHasOneProjectTx) Unscoped() *pMOCollaboratorHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCollaboratorBelongsToCreatedBy) Where(conds ...field.Expr) *pMOCollaboratorBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCollaboratorBelongsToCreatedBy) WithContext(ctx context.Context) *pMOCollaboratorBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCollaboratorBelongsToCreatedBy) Session(session *gorm.Session) *pMOCollaboratorBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCollaboratorBelongsToCreatedBy) Model(m *models.PMOCollaborator) *pMOCollaboratorBelongsToCreatedByTx {
	return &pMOCollaboratorBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCollaboratorBelongsToCreatedBy) Unscoped() *pMOCollaboratorBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOCollaboratorBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCollaboratorBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCollaboratorBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCollaboratorBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCollaboratorBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCollaboratorBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCollaboratorBelongsToCreatedByTx) Unscoped() *pMOCollaboratorBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCollaboratorBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOCollaboratorBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCollaboratorBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOCollaboratorBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCollaboratorBelongsToUpdatedBy) Session(session *gorm.Session) *pMOCollaboratorBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCollaboratorBelongsToUpdatedBy) Model(m *models.PMOCollaborator) *pMOCollaboratorBelongsToUpdatedByTx {
	return &pMOCollaboratorBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCollaboratorBelongsToUpdatedBy) Unscoped() *pMOCollaboratorBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOCollaboratorBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCollaboratorBelongsToUpdatedByTx) Unscoped() *pMOCollaboratorBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOCollaboratorBelongsToUser) Where(conds ...field.Expr) *pMOCollaboratorBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOCollaboratorBelongsToUser) WithContext(ctx context.Context) *pMOCollaboratorBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOCollaboratorBelongsToUser) Session(session *gorm.Session) *pMOCollaboratorBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOCollaboratorBelongsToUser) Model(m *models.PMOCollaborator) *pMOCollaboratorBelongsToUserTx {
	return &pMOCollaboratorBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOCollaboratorBelongsToUser) Unscoped() *pMOCollaboratorBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type pMOCollaboratorBelongsToUserTx struct{ tx *gorm.Association }

func (a pMOCollaboratorBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOCollaboratorBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOCollaboratorBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOCollaboratorBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOCollaboratorBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOCollaboratorBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOCollaboratorBelongsToUserTx) Unscoped() *pMOCollaboratorBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOCollaboratorDo struct{ gen.DO }

type IPMOCollaboratorDo interface {
	gen.SubQuery
	Debug() IPMOCollaboratorDo
	WithContext(ctx context.Context) IPMOCollaboratorDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOCollaboratorDo
	WriteDB() IPMOCollaboratorDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOCollaboratorDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOCollaboratorDo
	Not(conds ...gen.Condition) IPMOCollaboratorDo
	Or(conds ...gen.Condition) IPMOCollaboratorDo
	Select(conds ...field.Expr) IPMOCollaboratorDo
	Where(conds ...gen.Condition) IPMOCollaboratorDo
	Order(conds ...field.Expr) IPMOCollaboratorDo
	Distinct(cols ...field.Expr) IPMOCollaboratorDo
	Omit(cols ...field.Expr) IPMOCollaboratorDo
	Join(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo
	Group(cols ...field.Expr) IPMOCollaboratorDo
	Having(conds ...gen.Condition) IPMOCollaboratorDo
	Limit(limit int) IPMOCollaboratorDo
	Offset(offset int) IPMOCollaboratorDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCollaboratorDo
	Unscoped() IPMOCollaboratorDo
	Create(values ...*models.PMOCollaborator) error
	CreateInBatches(values []*models.PMOCollaborator, batchSize int) error
	Save(values ...*models.PMOCollaborator) error
	First() (*models.PMOCollaborator, error)
	Take() (*models.PMOCollaborator, error)
	Last() (*models.PMOCollaborator, error)
	Find() ([]*models.PMOCollaborator, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOCollaborator, err error)
	FindInBatches(result *[]*models.PMOCollaborator, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOCollaborator) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOCollaboratorDo
	Assign(attrs ...field.AssignExpr) IPMOCollaboratorDo
	Joins(fields ...field.RelationField) IPMOCollaboratorDo
	Preload(fields ...field.RelationField) IPMOCollaboratorDo
	FirstOrInit() (*models.PMOCollaborator, error)
	FirstOrCreate() (*models.PMOCollaborator, error)
	FindByPage(offset int, limit int) (result []*models.PMOCollaborator, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOCollaboratorDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOCollaboratorDo) Debug() IPMOCollaboratorDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOCollaboratorDo) WithContext(ctx context.Context) IPMOCollaboratorDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOCollaboratorDo) ReadDB() IPMOCollaboratorDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOCollaboratorDo) WriteDB() IPMOCollaboratorDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOCollaboratorDo) Session(config *gorm.Session) IPMOCollaboratorDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOCollaboratorDo) Clauses(conds ...clause.Expression) IPMOCollaboratorDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOCollaboratorDo) Returning(value interface{}, columns ...string) IPMOCollaboratorDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOCollaboratorDo) Not(conds ...gen.Condition) IPMOCollaboratorDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOCollaboratorDo) Or(conds ...gen.Condition) IPMOCollaboratorDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOCollaboratorDo) Select(conds ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOCollaboratorDo) Where(conds ...gen.Condition) IPMOCollaboratorDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOCollaboratorDo) Order(conds ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOCollaboratorDo) Distinct(cols ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOCollaboratorDo) Omit(cols ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOCollaboratorDo) Join(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOCollaboratorDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOCollaboratorDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOCollaboratorDo) Group(cols ...field.Expr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOCollaboratorDo) Having(conds ...gen.Condition) IPMOCollaboratorDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOCollaboratorDo) Limit(limit int) IPMOCollaboratorDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOCollaboratorDo) Offset(offset int) IPMOCollaboratorDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOCollaboratorDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOCollaboratorDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOCollaboratorDo) Unscoped() IPMOCollaboratorDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOCollaboratorDo) Create(values ...*models.PMOCollaborator) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOCollaboratorDo) CreateInBatches(values []*models.PMOCollaborator, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOCollaboratorDo) Save(values ...*models.PMOCollaborator) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOCollaboratorDo) First() (*models.PMOCollaborator, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCollaborator), nil
	}
}

func (p pMOCollaboratorDo) Take() (*models.PMOCollaborator, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCollaborator), nil
	}
}

func (p pMOCollaboratorDo) Last() (*models.PMOCollaborator, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCollaborator), nil
	}
}

func (p pMOCollaboratorDo) Find() ([]*models.PMOCollaborator, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOCollaborator), err
}

func (p pMOCollaboratorDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOCollaborator, err error) {
	buf := make([]*models.PMOCollaborator, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOCollaboratorDo) FindInBatches(result *[]*models.PMOCollaborator, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOCollaboratorDo) Attrs(attrs ...field.AssignExpr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOCollaboratorDo) Assign(attrs ...field.AssignExpr) IPMOCollaboratorDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOCollaboratorDo) Joins(fields ...field.RelationField) IPMOCollaboratorDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOCollaboratorDo) Preload(fields ...field.RelationField) IPMOCollaboratorDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOCollaboratorDo) FirstOrInit() (*models.PMOCollaborator, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCollaborator), nil
	}
}

func (p pMOCollaboratorDo) FirstOrCreate() (*models.PMOCollaborator, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOCollaborator), nil
	}
}

func (p pMOCollaboratorDo) FindByPage(offset int, limit int) (result []*models.PMOCollaborator, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOCollaboratorDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOCollaboratorDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOCollaboratorDo) Delete(models ...*models.PMOCollaborator) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOCollaboratorDo) withDO(do gen.Dao) *pMOCollaboratorDo {
	p.DO = *do.(*gen.DO)
	return p
}
