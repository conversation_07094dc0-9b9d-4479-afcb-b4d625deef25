// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMODocumentItem(db *gorm.DB, opts ...gen.DOOption) pMODocumentItem {
	_pMODocumentItem := pMODocumentItem{}

	_pMODocumentItem.pMODocumentItemDo.UseDB(db, opts...)
	_pMODocumentItem.pMODocumentItemDo.UseModel(&models.PMODocumentItem{})

	tableName := _pMODocumentItem.pMODocumentItemDo.TableName()
	_pMODocumentItem.ALL = field.NewAsterisk(tableName)
	_pMODocumentItem.ID = field.NewString(tableName, "id")
	_pMODocumentItem.CreatedAt = field.NewTime(tableName, "created_at")
	_pMODocumentItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMODocumentItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMODocumentItem.ProjectID = field.NewString(tableName, "project_id")
	_pMODocumentItem.TabKey = field.NewString(tableName, "tab_key")
	_pMODocumentItem.GroupID = field.NewString(tableName, "group_id")
	_pMODocumentItem.Name = field.NewString(tableName, "name")
	_pMODocumentItem.SharepointURL = field.NewString(tableName, "sharepoint_url")
	_pMODocumentItem.Date = field.NewTime(tableName, "date")
	_pMODocumentItem.Type = field.NewString(tableName, "type")
	_pMODocumentItem.FileID = field.NewString(tableName, "file_id")
	_pMODocumentItem.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMODocumentItem.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMODocumentItem.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMODocumentItem.Project = pMODocumentItemHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMODocumentItem.CreatedBy = pMODocumentItemBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMODocumentItem.UpdatedBy = pMODocumentItemBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMODocumentItem.Group = pMODocumentItemBelongsToGroup{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Group", "models.PMODocumentGroup"),
	}

	_pMODocumentItem.File = pMODocumentItemBelongsToFile{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("File", "models.File"),
	}

	_pMODocumentItem.fillFieldMap()

	return _pMODocumentItem
}

type pMODocumentItem struct {
	pMODocumentItemDo

	ALL           field.Asterisk
	ID            field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	ProjectID     field.String
	TabKey        field.String
	GroupID       field.String
	Name          field.String
	SharepointURL field.String
	Date          field.Time
	Type          field.String
	FileID        field.String
	CreatedByID   field.String
	UpdatedByID   field.String
	DeletedByID   field.String
	Project       pMODocumentItemHasOneProject

	CreatedBy pMODocumentItemBelongsToCreatedBy

	UpdatedBy pMODocumentItemBelongsToUpdatedBy

	Group pMODocumentItemBelongsToGroup

	File pMODocumentItemBelongsToFile

	fieldMap map[string]field.Expr
}

func (p pMODocumentItem) Table(newTableName string) *pMODocumentItem {
	p.pMODocumentItemDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMODocumentItem) As(alias string) *pMODocumentItem {
	p.pMODocumentItemDo.DO = *(p.pMODocumentItemDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMODocumentItem) updateTableName(table string) *pMODocumentItem {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.TabKey = field.NewString(table, "tab_key")
	p.GroupID = field.NewString(table, "group_id")
	p.Name = field.NewString(table, "name")
	p.SharepointURL = field.NewString(table, "sharepoint_url")
	p.Date = field.NewTime(table, "date")
	p.Type = field.NewString(table, "type")
	p.FileID = field.NewString(table, "file_id")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMODocumentItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMODocumentItem) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 20)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["group_id"] = p.GroupID
	p.fieldMap["name"] = p.Name
	p.fieldMap["sharepoint_url"] = p.SharepointURL
	p.fieldMap["date"] = p.Date
	p.fieldMap["type"] = p.Type
	p.fieldMap["file_id"] = p.FileID
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMODocumentItem) clone(db *gorm.DB) pMODocumentItem {
	p.pMODocumentItemDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.Group.db = db.Session(&gorm.Session{Initialized: true})
	p.Group.db.Statement.ConnPool = db.Statement.ConnPool
	p.File.db = db.Session(&gorm.Session{Initialized: true})
	p.File.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMODocumentItem) replaceDB(db *gorm.DB) pMODocumentItem {
	p.pMODocumentItemDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	p.Group.db = db.Session(&gorm.Session{})
	p.File.db = db.Session(&gorm.Session{})
	return p
}

type pMODocumentItemHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMODocumentItemHasOneProject) Where(conds ...field.Expr) *pMODocumentItemHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemHasOneProject) WithContext(ctx context.Context) *pMODocumentItemHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemHasOneProject) Session(session *gorm.Session) *pMODocumentItemHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemHasOneProject) Model(m *models.PMODocumentItem) *pMODocumentItemHasOneProjectTx {
	return &pMODocumentItemHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemHasOneProject) Unscoped() *pMODocumentItemHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemHasOneProjectTx struct{ tx *gorm.Association }

func (a pMODocumentItemHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemHasOneProjectTx) Unscoped() *pMODocumentItemHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemBelongsToCreatedBy) Where(conds ...field.Expr) *pMODocumentItemBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemBelongsToCreatedBy) WithContext(ctx context.Context) *pMODocumentItemBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemBelongsToCreatedBy) Session(session *gorm.Session) *pMODocumentItemBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemBelongsToCreatedBy) Model(m *models.PMODocumentItem) *pMODocumentItemBelongsToCreatedByTx {
	return &pMODocumentItemBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemBelongsToCreatedBy) Unscoped() *pMODocumentItemBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMODocumentItemBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemBelongsToCreatedByTx) Unscoped() *pMODocumentItemBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemBelongsToUpdatedBy) Where(conds ...field.Expr) *pMODocumentItemBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemBelongsToUpdatedBy) WithContext(ctx context.Context) *pMODocumentItemBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemBelongsToUpdatedBy) Session(session *gorm.Session) *pMODocumentItemBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemBelongsToUpdatedBy) Model(m *models.PMODocumentItem) *pMODocumentItemBelongsToUpdatedByTx {
	return &pMODocumentItemBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemBelongsToUpdatedBy) Unscoped() *pMODocumentItemBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMODocumentItemBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemBelongsToUpdatedByTx) Unscoped() *pMODocumentItemBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemBelongsToGroup struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemBelongsToGroup) Where(conds ...field.Expr) *pMODocumentItemBelongsToGroup {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemBelongsToGroup) WithContext(ctx context.Context) *pMODocumentItemBelongsToGroup {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemBelongsToGroup) Session(session *gorm.Session) *pMODocumentItemBelongsToGroup {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemBelongsToGroup) Model(m *models.PMODocumentItem) *pMODocumentItemBelongsToGroupTx {
	return &pMODocumentItemBelongsToGroupTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemBelongsToGroup) Unscoped() *pMODocumentItemBelongsToGroup {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemBelongsToGroupTx struct{ tx *gorm.Association }

func (a pMODocumentItemBelongsToGroupTx) Find() (result *models.PMODocumentGroup, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemBelongsToGroupTx) Append(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemBelongsToGroupTx) Replace(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemBelongsToGroupTx) Delete(values ...*models.PMODocumentGroup) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemBelongsToGroupTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemBelongsToGroupTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemBelongsToGroupTx) Unscoped() *pMODocumentItemBelongsToGroupTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemBelongsToFile struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentItemBelongsToFile) Where(conds ...field.Expr) *pMODocumentItemBelongsToFile {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentItemBelongsToFile) WithContext(ctx context.Context) *pMODocumentItemBelongsToFile {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentItemBelongsToFile) Session(session *gorm.Session) *pMODocumentItemBelongsToFile {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentItemBelongsToFile) Model(m *models.PMODocumentItem) *pMODocumentItemBelongsToFileTx {
	return &pMODocumentItemBelongsToFileTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentItemBelongsToFile) Unscoped() *pMODocumentItemBelongsToFile {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentItemBelongsToFileTx struct{ tx *gorm.Association }

func (a pMODocumentItemBelongsToFileTx) Find() (result *models.File, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentItemBelongsToFileTx) Append(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentItemBelongsToFileTx) Replace(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentItemBelongsToFileTx) Delete(values ...*models.File) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentItemBelongsToFileTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentItemBelongsToFileTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentItemBelongsToFileTx) Unscoped() *pMODocumentItemBelongsToFileTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentItemDo struct{ gen.DO }

type IPMODocumentItemDo interface {
	gen.SubQuery
	Debug() IPMODocumentItemDo
	WithContext(ctx context.Context) IPMODocumentItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMODocumentItemDo
	WriteDB() IPMODocumentItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMODocumentItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMODocumentItemDo
	Not(conds ...gen.Condition) IPMODocumentItemDo
	Or(conds ...gen.Condition) IPMODocumentItemDo
	Select(conds ...field.Expr) IPMODocumentItemDo
	Where(conds ...gen.Condition) IPMODocumentItemDo
	Order(conds ...field.Expr) IPMODocumentItemDo
	Distinct(cols ...field.Expr) IPMODocumentItemDo
	Omit(cols ...field.Expr) IPMODocumentItemDo
	Join(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo
	Group(cols ...field.Expr) IPMODocumentItemDo
	Having(conds ...gen.Condition) IPMODocumentItemDo
	Limit(limit int) IPMODocumentItemDo
	Offset(offset int) IPMODocumentItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentItemDo
	Unscoped() IPMODocumentItemDo
	Create(values ...*models.PMODocumentItem) error
	CreateInBatches(values []*models.PMODocumentItem, batchSize int) error
	Save(values ...*models.PMODocumentItem) error
	First() (*models.PMODocumentItem, error)
	Take() (*models.PMODocumentItem, error)
	Last() (*models.PMODocumentItem, error)
	Find() ([]*models.PMODocumentItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentItem, err error)
	FindInBatches(result *[]*models.PMODocumentItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMODocumentItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMODocumentItemDo
	Assign(attrs ...field.AssignExpr) IPMODocumentItemDo
	Joins(fields ...field.RelationField) IPMODocumentItemDo
	Preload(fields ...field.RelationField) IPMODocumentItemDo
	FirstOrInit() (*models.PMODocumentItem, error)
	FirstOrCreate() (*models.PMODocumentItem, error)
	FindByPage(offset int, limit int) (result []*models.PMODocumentItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMODocumentItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMODocumentItemDo) Debug() IPMODocumentItemDo {
	return p.withDO(p.DO.Debug())
}

func (p pMODocumentItemDo) WithContext(ctx context.Context) IPMODocumentItemDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMODocumentItemDo) ReadDB() IPMODocumentItemDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMODocumentItemDo) WriteDB() IPMODocumentItemDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMODocumentItemDo) Session(config *gorm.Session) IPMODocumentItemDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMODocumentItemDo) Clauses(conds ...clause.Expression) IPMODocumentItemDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMODocumentItemDo) Returning(value interface{}, columns ...string) IPMODocumentItemDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMODocumentItemDo) Not(conds ...gen.Condition) IPMODocumentItemDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMODocumentItemDo) Or(conds ...gen.Condition) IPMODocumentItemDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMODocumentItemDo) Select(conds ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMODocumentItemDo) Where(conds ...gen.Condition) IPMODocumentItemDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMODocumentItemDo) Order(conds ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMODocumentItemDo) Distinct(cols ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMODocumentItemDo) Omit(cols ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMODocumentItemDo) Join(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMODocumentItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMODocumentItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMODocumentItemDo) Group(cols ...field.Expr) IPMODocumentItemDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMODocumentItemDo) Having(conds ...gen.Condition) IPMODocumentItemDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMODocumentItemDo) Limit(limit int) IPMODocumentItemDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMODocumentItemDo) Offset(offset int) IPMODocumentItemDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMODocumentItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentItemDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMODocumentItemDo) Unscoped() IPMODocumentItemDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMODocumentItemDo) Create(values ...*models.PMODocumentItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMODocumentItemDo) CreateInBatches(values []*models.PMODocumentItem, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMODocumentItemDo) Save(values ...*models.PMODocumentItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMODocumentItemDo) First() (*models.PMODocumentItem, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItem), nil
	}
}

func (p pMODocumentItemDo) Take() (*models.PMODocumentItem, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItem), nil
	}
}

func (p pMODocumentItemDo) Last() (*models.PMODocumentItem, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItem), nil
	}
}

func (p pMODocumentItemDo) Find() ([]*models.PMODocumentItem, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMODocumentItem), err
}

func (p pMODocumentItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentItem, err error) {
	buf := make([]*models.PMODocumentItem, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMODocumentItemDo) FindInBatches(result *[]*models.PMODocumentItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMODocumentItemDo) Attrs(attrs ...field.AssignExpr) IPMODocumentItemDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMODocumentItemDo) Assign(attrs ...field.AssignExpr) IPMODocumentItemDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMODocumentItemDo) Joins(fields ...field.RelationField) IPMODocumentItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMODocumentItemDo) Preload(fields ...field.RelationField) IPMODocumentItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMODocumentItemDo) FirstOrInit() (*models.PMODocumentItem, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItem), nil
	}
}

func (p pMODocumentItemDo) FirstOrCreate() (*models.PMODocumentItem, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentItem), nil
	}
}

func (p pMODocumentItemDo) FindByPage(offset int, limit int) (result []*models.PMODocumentItem, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMODocumentItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMODocumentItemDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMODocumentItemDo) Delete(models ...*models.PMODocumentItem) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMODocumentItemDo) withDO(do gen.Dao) *pMODocumentItemDo {
	p.DO = *do.(*gen.DO)
	return p
}
