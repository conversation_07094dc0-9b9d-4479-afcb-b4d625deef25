-- CreateTable
CREATE TABLE "public"."files" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "checksum" TEXT NOT NULL,
    "app" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "files_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "files_path_key" ON "public"."files"("path");

-- CreateIndex
CREATE UNIQUE INDEX "files_url_key" ON "public"."files"("url");

-- CreateIndex
CREATE INDEX "files_name_path_url_idx" ON "public"."files"("name", "path", "url");

-- AddF<PERSON>ignKey
ALTER TABLE "public"."files" ADD CONSTRAINT "files_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
