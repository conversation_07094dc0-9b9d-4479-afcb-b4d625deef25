package core

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestArchiver_FromUrls(t *testing.T) {
	// Create a new archiver
	ctx := NewContext(&ContextOptions{
		ENV: NewEnv(),
	})
	arch := NewArchiver(ctx)

	// Define some test URLs
	urls := []string{
		"https://i.pinimg.com/236x/d2/0c/7c/d20c7cd11604ef0ea16fd6bc8d1ad1e9.jpg",
		"https://i.pinimg.com/474x/40/36/7a/40367a6344ac3913cec39cfeed4238c3.jpg",
	}

	// Call the FromURLs function to create a zip archive
	zipData, err := arch.FromURLs("test", urls, nil)

	// Check that the function did not return an error
	require.NoError(t, err)
	require.NotEmpty(t, zipData)
}

func TestArchiver_FromBytes(t *testing.T) {
	ctx := NewContext(&ContextOptions{
		ENV: NewEnv(),
	})

	arch := NewArchiver(ctx)

	body := []ArchiveByteBody{
		{
			Name: "test.txt",
			File: []byte{80, 117, 114, 101, 10},
		},
		{
			Name: "test2.txt",
			File: []byte{80, 117, 114, 101, 10},
		},
	}

	zipData, err := arch.FromBytes("test", body, nil)

	// Check that the function did not return an error
	require.NoError(t, err)
	require.NotEmpty(t, zipData)

}
