package models

// PMOChecklistItem represents project-specific checklist items
type PMOChecklistItem struct {
	BaseModel
	ProjectID string    `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey    PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail    string    `json:"detail" gorm:"column:detail"`
	IsChecked bool      `json:"is_checked" gorm:"column:is_checked"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOChecklistItem) TableName() string {
	return "pmo_checklist_items"
}
