image: docker:stable
services:
  - docker:24.0.5-dind
variables:
  # DOCKER_HOST: tcp://localhost:2375
  # DOCKER_TLS_CERTDIR: ""
  # TAG_LATEST: $AWS_ECR_REGISTRY/$PROJECT_NAME:latest-$APP_NAME
  # TAG_COMMIT: $AWS_ECR_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHORT_SHA-$APP_NAME
  TAG_LATEST: $CI_REGISTRY_IMAGE:doc-latest
  TAG_COMMIT: $CI_REGISTRY_IMAGE:doc-$CI_COMMIT_SHORT_SHA
stages:
  - test
  - tag
  - release
  - build
  - deploy

test:
  stage: test
  only:
    - release
  image: golang:1.24.5-alpine3.22
  script:
    - go get
    - go test ./...
  tags:
    - latest-runner

tagging:
  stage: tag
  only:
    - release
  image: alpine/git
  before_script:
    # Clone the repository via HTTPS inside a new directory
    - git clone "https://gitlab-ci-token:${RELEASE_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git" "${CI_COMMIT_SHA}"

    # Set the displayed user with the commits that are about to be made
    - git config --global user.email "${GIT_USER_EMAIL:-$GITLAB_USER_EMAIL}"
    - git config --global user.name "${GIT_USER_NAME:-$GITLAB_USER_NAME}"
  script:
    - cd "${CI_COMMIT_SHA}"
    - ./tag
  tags:
    - latest-runner

release:
  stage: release
  only:
    - release
  image: goreleaser/goreleaser:latest
  script:
    - GITLAB_TOKEN=$RELEASE_TOKEN goreleaser release --clean
  tags:
    - latest-runner

update-changelog:
  stage: release
  only:
    - release
  image: node:18.14
  before_script:
    # Clone the repository via HTTPS inside a new directory
    - git clone "https://gitlab-ci-token:${RELEASE_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git" "${CI_COMMIT_SHA}"

    # Set the displayed user with the commits that are about to be made
    - git config --global user.email "${GIT_USER_EMAIL:-$GITLAB_USER_EMAIL}"
    - git config --global user.name "${GIT_USER_NAME:-$GITLAB_USER_NAME}"
  script:
    - cd "${CI_COMMIT_SHA}"
    - npm install -g conventional-changelog-cli
    - conventional-changelog -p angular -i CHANGELOG.md -s -r 0
    - git add .
    - git commit -m "update changelog [skip ci]"
    - git push
  tags:
    - latest-runner

build-doc:
  stage: build
  only:
    - release-doc
  tags:
    - latest-runner
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build --build-arg IDIN_CORE_VERSION=1.6.0 -f doc.Dockerfile  -t $TAG_COMMIT -t $TAG_LATEST .
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    - docker rmi $TAG_COMMIT $TAG_LATEST

deploy-doc:
  stage: deploy
  only:
    - release-doc
  image: alpine/git
  tags:
    - latest-runner
  before_script:
    - apk add --no-cache curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - mv kustomize /usr/local/bin/
    - git remote set-url origin https://gitlab-ci-token:${RELEASE_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    - git config --global user.email "${GIT_USER_EMAIL:-$GITLAB_USER_EMAIL}"
    - git config --global user.name "${GIT_USER_NAME:-$GITLAB_USER_NAME}"
  script:
    - git checkout -B master
    - git pull origin master
    - cd _k8s
    - kustomize edit set image $CI_REGISTRY_IMAGE=$TAG_COMMIT
    - cat kustomization.yml
    - git commit -am '[skip ci] document image update'
    - git push origin master
