# Get Started

To create a GoLang deployment with the provided Dockerfile and environment variables, you can follow these steps:

Step 1: Create a Dockerfile
Create a file named `Dockerfile` and copy the following contents into it:

```Dockerfile
FROM registry.finema.co/finema/idin-core:1.4.2

# Copy Go module files to the build environment
ADD go.mod go.sum /app/

# Download Go module dependencies
RUN go mod download

# Add the entire local directory to the build environment
ADD . /app/

# Build the Go application inside the build environment
RUN go build -o main

# Create the production image
FROM alpine:3.16

# Copy the executable from the build environment to the production image
COPY --from=0 /app/main /main

# Set the default command to run the executable
CMD ./main

```

Step 2: Create a .env file
Create a file named `.env` and copy the provided environment variables into it:

```plaintext
HOST=0.0.0.0:3000
ENV=dev
LOG_LEVEL=debug
```

Step 3: Build the Docker image
Open a terminal, navigate to the directory containing the Dockerfile and .env file, and run the following command to build the Docker image:

```shell
docker build -t my-go-app .
```

Step 4: Run the Docker container
Once the image is built, you can run a container based on it with the following command:

```shell
docker run -d --name my-go-container -p 3000:3000 --env-file .env my-go-app
```

Explanation:
- `-d` flag runs the container in detached mode (in the background).
- `--name my-go-container` specifies a name for the container.
- `-p 3000:3000` maps port 3000 from the container to port 3000 on the host machine.
- `--env-file .env` loads environment variables from the `.env` file.
- `my-go-app` is the name of the Docker image built in Step 3.

Now, your GoLang application should be deployed and accessible on `http://localhost:3000`.

Please note that the provided Dockerfile assumes that your Go application listens on port 3000. If your application uses a different port, make sure to update the port mapping (`-p`) in the `docker run` command accordingly.
