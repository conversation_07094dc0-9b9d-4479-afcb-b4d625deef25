package models

import (
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOBudgetInfo represents project budget information
type PMOBudgetInfo struct {
	BaseModel
	ProjectID    string  `json:"project_id" gorm:"column:project_id;type:uuid"`
	FundType     string  `json:"fund_type" gorm:"column:fund_type"`
	ProjectValue float64 `json:"project_value" gorm:"column:project_value"`
	BidbondValue float64 `json:"bidbond_value" gorm:"column:bidbond_value"`
	Partner      string  `json:"partner" gorm:"column:partner"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBudgetInfo) TableName() string {
	return "pmo_budget_info"
}

// PMOBudgetInfoVersion represents versioned budget information
type PMOBudgetInfoVersion struct {
	PMOBudgetInfo
	OriginalID string `json:"budget_info_id" gorm:"column:budget_info_id;type:uuid;index"`
}

func (PMOBudgetInfoVersion) TableName() string {
	return "pmo_budget_info_versions"
}

func (u *PMOBudgetInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
