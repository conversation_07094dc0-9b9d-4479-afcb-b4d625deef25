// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newSga(db *gorm.DB, opts ...gen.DOOption) sga {
	_sga := sga{}

	_sga.sgaDo.UseDB(db, opts...)
	_sga.sgaDo.UseModel(&models.Sga{})

	tableName := _sga.sgaDo.TableName()
	_sga.ALL = field.NewAsterisk(tableName)
	_sga.ID = field.NewString(tableName, "id")
	_sga.CreatedAt = field.NewTime(tableName, "created_at")
	_sga.UpdatedAt = field.NewTime(tableName, "updated_at")
	_sga.DeletedAt = field.NewField(tableName, "deleted_at")
	_sga.Name = field.NewString(tableName, "name")
	_sga.Description = field.NewString(tableName, "description")
	_sga.CreatedByID = field.NewString(tableName, "created_by_id")
	_sga.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_sga.DeletedByID = field.NewString(tableName, "deleted_by_id")

	_sga.fillFieldMap()

	return _sga
}

type sga struct {
	sgaDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Name        field.String
	Description field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String

	fieldMap map[string]field.Expr
}

func (s sga) Table(newTableName string) *sga {
	s.sgaDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sga) As(alias string) *sga {
	s.sgaDo.DO = *(s.sgaDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sga) updateTableName(table string) *sga {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewString(table, "id")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.CreatedByID = field.NewString(table, "created_by_id")
	s.UpdatedByID = field.NewString(table, "updated_by_id")
	s.DeletedByID = field.NewString(table, "deleted_by_id")

	s.fillFieldMap()

	return s
}

func (s *sga) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sga) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["created_by_id"] = s.CreatedByID
	s.fieldMap["updated_by_id"] = s.UpdatedByID
	s.fieldMap["deleted_by_id"] = s.DeletedByID
}

func (s sga) clone(db *gorm.DB) sga {
	s.sgaDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sga) replaceDB(db *gorm.DB) sga {
	s.sgaDo.ReplaceDB(db)
	return s
}

type sgaDo struct{ gen.DO }

type ISgaDo interface {
	gen.SubQuery
	Debug() ISgaDo
	WithContext(ctx context.Context) ISgaDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISgaDo
	WriteDB() ISgaDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISgaDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISgaDo
	Not(conds ...gen.Condition) ISgaDo
	Or(conds ...gen.Condition) ISgaDo
	Select(conds ...field.Expr) ISgaDo
	Where(conds ...gen.Condition) ISgaDo
	Order(conds ...field.Expr) ISgaDo
	Distinct(cols ...field.Expr) ISgaDo
	Omit(cols ...field.Expr) ISgaDo
	Join(table schema.Tabler, on ...field.Expr) ISgaDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISgaDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISgaDo
	Group(cols ...field.Expr) ISgaDo
	Having(conds ...gen.Condition) ISgaDo
	Limit(limit int) ISgaDo
	Offset(offset int) ISgaDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISgaDo
	Unscoped() ISgaDo
	Create(values ...*models.Sga) error
	CreateInBatches(values []*models.Sga, batchSize int) error
	Save(values ...*models.Sga) error
	First() (*models.Sga, error)
	Take() (*models.Sga, error)
	Last() (*models.Sga, error)
	Find() ([]*models.Sga, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Sga, err error)
	FindInBatches(result *[]*models.Sga, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Sga) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISgaDo
	Assign(attrs ...field.AssignExpr) ISgaDo
	Joins(fields ...field.RelationField) ISgaDo
	Preload(fields ...field.RelationField) ISgaDo
	FirstOrInit() (*models.Sga, error)
	FirstOrCreate() (*models.Sga, error)
	FindByPage(offset int, limit int) (result []*models.Sga, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISgaDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sgaDo) Debug() ISgaDo {
	return s.withDO(s.DO.Debug())
}

func (s sgaDo) WithContext(ctx context.Context) ISgaDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sgaDo) ReadDB() ISgaDo {
	return s.Clauses(dbresolver.Read)
}

func (s sgaDo) WriteDB() ISgaDo {
	return s.Clauses(dbresolver.Write)
}

func (s sgaDo) Session(config *gorm.Session) ISgaDo {
	return s.withDO(s.DO.Session(config))
}

func (s sgaDo) Clauses(conds ...clause.Expression) ISgaDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sgaDo) Returning(value interface{}, columns ...string) ISgaDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sgaDo) Not(conds ...gen.Condition) ISgaDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sgaDo) Or(conds ...gen.Condition) ISgaDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sgaDo) Select(conds ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sgaDo) Where(conds ...gen.Condition) ISgaDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sgaDo) Order(conds ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sgaDo) Distinct(cols ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sgaDo) Omit(cols ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sgaDo) Join(table schema.Tabler, on ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sgaDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISgaDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sgaDo) RightJoin(table schema.Tabler, on ...field.Expr) ISgaDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sgaDo) Group(cols ...field.Expr) ISgaDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sgaDo) Having(conds ...gen.Condition) ISgaDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sgaDo) Limit(limit int) ISgaDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sgaDo) Offset(offset int) ISgaDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sgaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISgaDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sgaDo) Unscoped() ISgaDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sgaDo) Create(values ...*models.Sga) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sgaDo) CreateInBatches(values []*models.Sga, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sgaDo) Save(values ...*models.Sga) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sgaDo) First() (*models.Sga, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Sga), nil
	}
}

func (s sgaDo) Take() (*models.Sga, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Sga), nil
	}
}

func (s sgaDo) Last() (*models.Sga, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Sga), nil
	}
}

func (s sgaDo) Find() ([]*models.Sga, error) {
	result, err := s.DO.Find()
	return result.([]*models.Sga), err
}

func (s sgaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Sga, err error) {
	buf := make([]*models.Sga, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sgaDo) FindInBatches(result *[]*models.Sga, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sgaDo) Attrs(attrs ...field.AssignExpr) ISgaDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sgaDo) Assign(attrs ...field.AssignExpr) ISgaDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sgaDo) Joins(fields ...field.RelationField) ISgaDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sgaDo) Preload(fields ...field.RelationField) ISgaDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sgaDo) FirstOrInit() (*models.Sga, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Sga), nil
	}
}

func (s sgaDo) FirstOrCreate() (*models.Sga, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Sga), nil
	}
}

func (s sgaDo) FindByPage(offset int, limit int) (result []*models.Sga, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sgaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sgaDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sgaDo) Delete(models ...*models.Sga) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sgaDo) withDO(do gen.Dao) *sgaDo {
	s.DO = *do.(*gen.DO)
	return s
}
