package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectBudgetController struct {
}

// PMO Project Budget methods
func (m PMOProjectBudgetController) BudgetFind(c core.IHTTPContext) error {
	input := &requests.PMOBudgetFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoBudgetSvc := services.NewPMOProjectBudgetService(c)
	budget, err := pmoBudgetSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, budget)
}

func (m PMOProjectBudgetController) BudgetCreate(c core.IHTTPContext) error {
	input := &requests.PMOBudgetCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoBudgetSvc := services.NewPMOProjectBudgetService(c)
	payload := &dto.PMOBudgetCreatePayload{
		ProjectID:    c.Param("id"),
		FundType:     utils.ToNonPointer(input.FundType),
		ProjectValue: utils.ToNonPointer(input.ProjectValue),
		BidbondValue: utils.ToNonPointer(input.BidbondValue),
		Partner:      utils.ToNonPointer(input.Partner),
	}

	budget, err := pmoBudgetSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, budget)
}

func (m PMOProjectBudgetController) BudgetVersionsFind(c core.IHTTPContext) error {
	pmoBudgetSvc := services.NewPMOProjectBudgetService(c)
	res, err := pmoBudgetSvc.BudgetVersionsFind(c.Param("id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
