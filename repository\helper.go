package repository

// MapPaginatedItems maps a slice of type T to a slice of type R within a paginated result.
// It takes a pagination object of type *repository.Pagination[T] and a mapping function mapFunc of type func(T) R.
// The function returns a new pagination object of type *repository.Pagination[R] with the same pagination metadata
// but with the Items field transformed by applying the mapFunc to each item in the original slice.
func MapPaginatedItems[T any, R any](pagination *Pagination[T], mapFunc func(T) R) *Pagination[R] {
	items := make([]R, len(pagination.Items))
	for i, item := range pagination.Items {
		items[i] = mapFunc(item)
	}

	return &Pagination[R]{
		Page:  pagination.Page,
		Total: pagination.Total,
		Limit: pagination.Limit,
		Count: pagination.Count,
		Items: items,
	}
}
