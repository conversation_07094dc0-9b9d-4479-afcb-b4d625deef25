/*
  Warnings:

  - You are about to drop the column `tab_key` on the `pmo_collaborators` table. All the data in the column will be lost.
  - You are about to drop the column `tab_main_response` on the `pmo_collaborators` table. All the data in the column will be lost.
  - You are about to drop the column `tab_permission` on the `pmo_collaborators` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "public"."pmo_collaborators_project_id_user_id_tab_key_idx";

-- AlterTable
ALTER TABLE "public"."pmo_collaborators" DROP COLUMN "tab_key",
DROP COLUMN "tab_main_response",
DROP COLUMN "tab_permission",
ADD COLUMN     "bidding_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "bidding_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE',
ADD COLUMN     "bizco_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "bizco_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE',
ADD COLUMN     "confidential_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "confidential_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE',
ADD COLUMN     "pmo_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "pmo_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE',
ADD COLUMN     "presales_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "presales_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE',
ADD COLUMN     "sales_main" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "sales_permission" "public"."PMOTabPermission" NOT NULL DEFAULT 'NONE';

-- CreateIndex
CREATE INDEX "pmo_collaborators_project_id_user_id_confidential_permissio_idx" ON "public"."pmo_collaborators"("project_id", "user_id", "confidential_permission", "confidential_main", "sales_permission", "sales_main", "presales_permission", "presales_main", "bidding_permission", "bidding_main", "pmo_permission", "pmo_main", "bizco_permission", "bizco_main");
