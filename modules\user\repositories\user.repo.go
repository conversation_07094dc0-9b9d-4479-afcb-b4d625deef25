package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/models/query"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var User = repository.Make[models.User]()

func UserOrderBy(pageOptions *core.PageOptions) repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("team_code ASC, display_name ASC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func UserWithTeam(code []string) repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		filteredCode := []string{}
		for _, c := range code {
			if c != "" {
				filteredCode = append(filteredCode, c)
			}
		}
		if len(filteredCode) == 0 {
			return
		}

		c.Where(query.User.TeamCode.In(filteredCode...))
	}
}

func UserWithTimeSheetDateRange(startDate *string, endDate *string) repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		if startDate != nil && endDate != nil {
			c.Joins("JOIN timesheets ON timesheets.user_id = users.id").Where("DATE(timesheets.date) BETWEEN ? AND ?", startDate, endDate).Distinct()
		} else if startDate != nil {
			c.Joins("JOIN timesheets ON timesheets.user_id = users.id").Where("DATE(timesheets.date) >= ?", startDate).Distinct()
		} else if endDate != nil {
			c.Joins("JOIN timesheets ON timesheets.user_id = users.id").Where("DATE(timesheets.date) <= ?", endDate).Distinct()
		}
	}
}

func UserWithSearch(q string) repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("email ILIKE ? OR full_name ILIKE ? OR display_name ILIKE ?", searchTerm, searchTerm, searchTerm)
	}
}

func UserWithActiveStatus(isActive *bool) repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		if isActive == nil {
			return
		}
		c.Where("is_active = ?", *isActive)
	}
}

func UserWithAllRelation() repository.Option[models.User] {
	return func(c repository.IRepository[models.User]) {
		c.Preload("Team").Preload("AccessLevel")
	}
}
