apiVersion: apps/v1
kind: Deployment
metadata:
  name: idindoc
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  selector:
    matchLabels:
      name: idindoc
  template:
    metadata:
      labels:
        name: idindoc
    spec:
      containers:
        - name: idindoc
          image: registry.finema.co/finema/idin-core:doc-latest
          imagePullPolicy: Always
          livenessProbe:
            tcpSocket:
              port: 5173
            initialDelaySeconds: 30
            timeoutSeconds: 1
            periodSeconds: 300
          readinessProbe:
            tcpSocket:
              port: 5173
            initialDelaySeconds: 30
            timeoutSeconds: 1
            periodSeconds: 30
            failureThreshold: 5
          ports:
            - name: idindoc
              containerPort: 5173
      imagePullSecrets:
        - name: gitlab-registry-secret
