package models

// PMOCompetitor represents project competitors
type PMOCompetitor struct {
	BaseModel
	ProjectID string `json:"project_id" gorm:"column:project_id;type:uuid"`
	Company   string `json:"company" gorm:"column:company"`
	Detail    string `json:"detail" gorm:"column:detail"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOCompetitor) TableName() string {
	return "pmo_competitors"
}
