package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMORemarkCreate struct {
	core.BaseValidator
	Tab<PERSON>ey *string `json:"tab_key"`
	Detail *string `json:"detail"`
}

func (r *PMORemarkCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>equired(r.<PERSON><PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.Detail, "detail"))

	r.Must(r.IsStrIn(r.<PERSON>b<PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.<PERSON>r()
}
