# Archiver (zip)

functionality for archiving files. It includes methods for downloading files from URLs and creating archives from byte data.

Base on [mholt/archiver: Easily create & extract archives, and compress & decompress files of various formats (github.com)](https://github.com/mholt/archiver)

## Types

### IArchiver

```go
type IArchiver interface {
	FromURLs(fileName string, urls []string, options *ArchiverOptions) ([]byte, IError)
	FromBytes(fileName string, body []ArchiveByteBody, options *ArchiverOptions) ([]byte, IError)
}
```

`IArchiver` is an interface that defines the archiver's methods.

### ArchiverOptions

```go
type ArchiverOptions struct {
}
```

`ArchiverOptions` represents the options for the archiver.

### ArchiveByteBody

```go
type ArchiveByteBody struct {
	File []byte
	Name string
}
```

`ArchiveByteBody` represents a byte body for archiving.

## Functions

### NewArchiver

```go
func NewArchiver(ctx IContext) IArchiver
```

`NewArchiver` creates a new archiver instance with the provided context.

## Methods

### FromURLs

```go
func (s archiver) FromURLs(fileName string, urls []string, options *ArchiverOptions) ([]byte, IError)
```

`FromURLs` downloads files from the provided URLs and creates a zip archive. It returns the archived data as a byte slice and an error.

### FromBytes

```go
func (s archiver) FromBytes(fileName string, body []ArchiveByteBody, options *ArchiverOptions) ([]byte, IError)
```

`FromBytes` creates a zip archive from the provided byte data. It returns the archived data as a byte slice and an error.

### createZipFile

```go
func (s archiver) createZipFile(fileName string, fromDir string) ([]byte, IError)
```

`createZipFile` creates a zip file from the specified directory. It returns the archived data as a byte slice and an error.

### downloadFile

```go
func (s archiver) downloadFile(url string, dest string) IError
```

`downloadFile` downloads a file from the specified URL and saves it to the destination path. It returns an error if the download fails.

## Error Codes

- `ARCHIVER_DOWNLOAD_ERROR`: Failed to download the file.
- `ARCHIVER_CREATE_FILE_ERROR`: Failed to create the file.
- `ARCHIVER_WRITE_FILE_ERROR`: Failed to write the downloaded data to a file.
- `ARCHIVER_ZIP_ERROR`: Failed to zip the file.
- `ARCHIVER_READ_FILE_ERROR`: Failed to read the file.

Please note that the code provided is a partial implementation, and the functionality of the `IContext` and other related types is not included in the provided code snippet.

## Example

```go
package main

import (
	"fmt"

  core "gitlab.finema.co/finema/idin-core"
)

func main() {
	// Create a new archiver
	archiver := core.NewArchiver(/* your context */)

	// Example 1: Download files from URLs and create a zip archive
	urls := []string{
		"https://example.com/file1.txt",
		"https://example.com/file2.txt",
		"https://example.com/file3.txt",
	}
	fileName := "archive"
	options := &core.ArchiverOptions{}
	archiveData, err := archiver.FromURLs(fileName, urls, options)
	if err != nil {
		fmt.Println("Failed to create zip archive:", err)
		return
	}
	fmt.Println("Zip archive created successfully.")

	// Example 2: Create a zip archive from byte data
	body := []core.ArchiveByteBody{
		{
			File: []byte("Content of file1"),
			Name: "file1.txt",
		},
		{
			File: []byte("Content of file2"),
			Name: "file2.txt",
		},
		{
			File: []byte("Content of file3"),
			Name: "file3.txt",
		},
	}
	fileName = "archive"
	archiveData, err = archiver.FromBytes(fileName, body, options)
	if err != nil {
		fmt.Println("Failed to create zip archive:", err)
		return
	}
	fmt.Println("Zip archive created successfully.")

	// Use the archiveData as needed
	fmt.Printf("Archive data: %v\n", archiveData)
}
```
