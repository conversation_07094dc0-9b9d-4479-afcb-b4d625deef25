// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newUser(db *gorm.DB, opts ...gen.DOOption) user {
	_user := user{}

	_user.userDo.UseDB(db, opts...)
	_user.userDo.UseModel(&models.User{})

	tableName := _user.userDo.TableName()
	_user.ALL = field.NewAsterisk(tableName)
	_user.ID = field.NewString(tableName, "id")
	_user.CreatedAt = field.NewTime(tableName, "created_at")
	_user.UpdatedAt = field.NewTime(tableName, "updated_at")
	_user.Email = field.NewString(tableName, "email")
	_user.FullName = field.NewString(tableName, "full_name")
	_user.DisplayName = field.NewString(tableName, "display_name")
	_user.Position = field.NewString(tableName, "position")
	_user.TeamCode = field.NewString(tableName, "team_code")
	_user.Company = field.NewString(tableName, "company")
	_user.AvatarURL = field.NewString(tableName, "avatar_url")
	_user.SlackID = field.NewString(tableName, "slack_id")
	_user.IsActive = field.NewBool(tableName, "is_active")
	_user.JoinedDate = field.NewTime(tableName, "joined_date")
	_user.AccessLevel = userHasOneAccessLevel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("AccessLevel", "models.UserAccessLevel"),
		User: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("AccessLevel.User", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("AccessLevel.User.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("AccessLevel.User.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("AccessLevel.User.AccessLevel", "models.UserAccessLevel"),
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("AccessLevel.User.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("AccessLevel.User.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("AccessLevel.User.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("AccessLevel.User.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("AccessLevel.User.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("AccessLevel.User.Checkins.User", "models.User"),
				},
			},
		},
	}

	_user.Timesheets = userHasManyTimesheets{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Timesheets", "models.Timesheet"),
	}

	_user.Checkins = userHasManyCheckins{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Checkins", "models.Checkin"),
	}

	_user.Team = userBelongsToTeam{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Team", "models.Team"),
	}

	_user.fillFieldMap()

	return _user
}

type user struct {
	userDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Email       field.String
	FullName    field.String
	DisplayName field.String
	Position    field.String
	TeamCode    field.String
	Company     field.String
	AvatarURL   field.String
	SlackID     field.String
	IsActive    field.Bool
	JoinedDate  field.Time
	AccessLevel userHasOneAccessLevel

	Timesheets userHasManyTimesheets

	Checkins userHasManyCheckins

	Team userBelongsToTeam

	fieldMap map[string]field.Expr
}

func (u user) Table(newTableName string) *user {
	u.userDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u user) As(alias string) *user {
	u.userDo.DO = *(u.userDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *user) updateTableName(table string) *user {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewString(table, "id")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.Email = field.NewString(table, "email")
	u.FullName = field.NewString(table, "full_name")
	u.DisplayName = field.NewString(table, "display_name")
	u.Position = field.NewString(table, "position")
	u.TeamCode = field.NewString(table, "team_code")
	u.Company = field.NewString(table, "company")
	u.AvatarURL = field.NewString(table, "avatar_url")
	u.SlackID = field.NewString(table, "slack_id")
	u.IsActive = field.NewBool(table, "is_active")
	u.JoinedDate = field.NewTime(table, "joined_date")

	u.fillFieldMap()

	return u
}

func (u *user) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *user) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 17)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["email"] = u.Email
	u.fieldMap["full_name"] = u.FullName
	u.fieldMap["display_name"] = u.DisplayName
	u.fieldMap["position"] = u.Position
	u.fieldMap["team_code"] = u.TeamCode
	u.fieldMap["company"] = u.Company
	u.fieldMap["avatar_url"] = u.AvatarURL
	u.fieldMap["slack_id"] = u.SlackID
	u.fieldMap["is_active"] = u.IsActive
	u.fieldMap["joined_date"] = u.JoinedDate

}

func (u user) clone(db *gorm.DB) user {
	u.userDo.ReplaceConnPool(db.Statement.ConnPool)
	u.AccessLevel.db = db.Session(&gorm.Session{Initialized: true})
	u.AccessLevel.db.Statement.ConnPool = db.Statement.ConnPool
	u.Timesheets.db = db.Session(&gorm.Session{Initialized: true})
	u.Timesheets.db.Statement.ConnPool = db.Statement.ConnPool
	u.Checkins.db = db.Session(&gorm.Session{Initialized: true})
	u.Checkins.db.Statement.ConnPool = db.Statement.ConnPool
	u.Team.db = db.Session(&gorm.Session{Initialized: true})
	u.Team.db.Statement.ConnPool = db.Statement.ConnPool
	return u
}

func (u user) replaceDB(db *gorm.DB) user {
	u.userDo.ReplaceDB(db)
	u.AccessLevel.db = db.Session(&gorm.Session{})
	u.Timesheets.db = db.Session(&gorm.Session{})
	u.Checkins.db = db.Session(&gorm.Session{})
	u.Team.db = db.Session(&gorm.Session{})
	return u
}

type userHasOneAccessLevel struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
}

func (a userHasOneAccessLevel) Where(conds ...field.Expr) *userHasOneAccessLevel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userHasOneAccessLevel) WithContext(ctx context.Context) *userHasOneAccessLevel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userHasOneAccessLevel) Session(session *gorm.Session) *userHasOneAccessLevel {
	a.db = a.db.Session(session)
	return &a
}

func (a userHasOneAccessLevel) Model(m *models.User) *userHasOneAccessLevelTx {
	return &userHasOneAccessLevelTx{a.db.Model(m).Association(a.Name())}
}

func (a userHasOneAccessLevel) Unscoped() *userHasOneAccessLevel {
	a.db = a.db.Unscoped()
	return &a
}

type userHasOneAccessLevelTx struct{ tx *gorm.Association }

func (a userHasOneAccessLevelTx) Find() (result *models.UserAccessLevel, err error) {
	return result, a.tx.Find(&result)
}

func (a userHasOneAccessLevelTx) Append(values ...*models.UserAccessLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userHasOneAccessLevelTx) Replace(values ...*models.UserAccessLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userHasOneAccessLevelTx) Delete(values ...*models.UserAccessLevel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userHasOneAccessLevelTx) Clear() error {
	return a.tx.Clear()
}

func (a userHasOneAccessLevelTx) Count() int64 {
	return a.tx.Count()
}

func (a userHasOneAccessLevelTx) Unscoped() *userHasOneAccessLevelTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userHasManyTimesheets struct {
	db *gorm.DB

	field.RelationField
}

func (a userHasManyTimesheets) Where(conds ...field.Expr) *userHasManyTimesheets {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userHasManyTimesheets) WithContext(ctx context.Context) *userHasManyTimesheets {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userHasManyTimesheets) Session(session *gorm.Session) *userHasManyTimesheets {
	a.db = a.db.Session(session)
	return &a
}

func (a userHasManyTimesheets) Model(m *models.User) *userHasManyTimesheetsTx {
	return &userHasManyTimesheetsTx{a.db.Model(m).Association(a.Name())}
}

func (a userHasManyTimesheets) Unscoped() *userHasManyTimesheets {
	a.db = a.db.Unscoped()
	return &a
}

type userHasManyTimesheetsTx struct{ tx *gorm.Association }

func (a userHasManyTimesheetsTx) Find() (result []*models.Timesheet, err error) {
	return result, a.tx.Find(&result)
}

func (a userHasManyTimesheetsTx) Append(values ...*models.Timesheet) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userHasManyTimesheetsTx) Replace(values ...*models.Timesheet) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userHasManyTimesheetsTx) Delete(values ...*models.Timesheet) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userHasManyTimesheetsTx) Clear() error {
	return a.tx.Clear()
}

func (a userHasManyTimesheetsTx) Count() int64 {
	return a.tx.Count()
}

func (a userHasManyTimesheetsTx) Unscoped() *userHasManyTimesheetsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userHasManyCheckins struct {
	db *gorm.DB

	field.RelationField
}

func (a userHasManyCheckins) Where(conds ...field.Expr) *userHasManyCheckins {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userHasManyCheckins) WithContext(ctx context.Context) *userHasManyCheckins {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userHasManyCheckins) Session(session *gorm.Session) *userHasManyCheckins {
	a.db = a.db.Session(session)
	return &a
}

func (a userHasManyCheckins) Model(m *models.User) *userHasManyCheckinsTx {
	return &userHasManyCheckinsTx{a.db.Model(m).Association(a.Name())}
}

func (a userHasManyCheckins) Unscoped() *userHasManyCheckins {
	a.db = a.db.Unscoped()
	return &a
}

type userHasManyCheckinsTx struct{ tx *gorm.Association }

func (a userHasManyCheckinsTx) Find() (result []*models.Checkin, err error) {
	return result, a.tx.Find(&result)
}

func (a userHasManyCheckinsTx) Append(values ...*models.Checkin) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userHasManyCheckinsTx) Replace(values ...*models.Checkin) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userHasManyCheckinsTx) Delete(values ...*models.Checkin) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userHasManyCheckinsTx) Clear() error {
	return a.tx.Clear()
}

func (a userHasManyCheckinsTx) Count() int64 {
	return a.tx.Count()
}

func (a userHasManyCheckinsTx) Unscoped() *userHasManyCheckinsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userBelongsToTeam struct {
	db *gorm.DB

	field.RelationField
}

func (a userBelongsToTeam) Where(conds ...field.Expr) *userBelongsToTeam {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a userBelongsToTeam) WithContext(ctx context.Context) *userBelongsToTeam {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a userBelongsToTeam) Session(session *gorm.Session) *userBelongsToTeam {
	a.db = a.db.Session(session)
	return &a
}

func (a userBelongsToTeam) Model(m *models.User) *userBelongsToTeamTx {
	return &userBelongsToTeamTx{a.db.Model(m).Association(a.Name())}
}

func (a userBelongsToTeam) Unscoped() *userBelongsToTeam {
	a.db = a.db.Unscoped()
	return &a
}

type userBelongsToTeamTx struct{ tx *gorm.Association }

func (a userBelongsToTeamTx) Find() (result *models.Team, err error) {
	return result, a.tx.Find(&result)
}

func (a userBelongsToTeamTx) Append(values ...*models.Team) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a userBelongsToTeamTx) Replace(values ...*models.Team) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a userBelongsToTeamTx) Delete(values ...*models.Team) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a userBelongsToTeamTx) Clear() error {
	return a.tx.Clear()
}

func (a userBelongsToTeamTx) Count() int64 {
	return a.tx.Count()
}

func (a userBelongsToTeamTx) Unscoped() *userBelongsToTeamTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type userDo struct{ gen.DO }

type IUserDo interface {
	gen.SubQuery
	Debug() IUserDo
	WithContext(ctx context.Context) IUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserDo
	WriteDB() IUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserDo
	Not(conds ...gen.Condition) IUserDo
	Or(conds ...gen.Condition) IUserDo
	Select(conds ...field.Expr) IUserDo
	Where(conds ...gen.Condition) IUserDo
	Order(conds ...field.Expr) IUserDo
	Distinct(cols ...field.Expr) IUserDo
	Omit(cols ...field.Expr) IUserDo
	Join(table schema.Tabler, on ...field.Expr) IUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserDo
	Group(cols ...field.Expr) IUserDo
	Having(conds ...gen.Condition) IUserDo
	Limit(limit int) IUserDo
	Offset(offset int) IUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserDo
	Unscoped() IUserDo
	Create(values ...*models.User) error
	CreateInBatches(values []*models.User, batchSize int) error
	Save(values ...*models.User) error
	First() (*models.User, error)
	Take() (*models.User, error)
	Last() (*models.User, error)
	Find() ([]*models.User, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.User, err error)
	FindInBatches(result *[]*models.User, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.User) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserDo
	Assign(attrs ...field.AssignExpr) IUserDo
	Joins(fields ...field.RelationField) IUserDo
	Preload(fields ...field.RelationField) IUserDo
	FirstOrInit() (*models.User, error)
	FirstOrCreate() (*models.User, error)
	FindByPage(offset int, limit int) (result []*models.User, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userDo) Debug() IUserDo {
	return u.withDO(u.DO.Debug())
}

func (u userDo) WithContext(ctx context.Context) IUserDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userDo) ReadDB() IUserDo {
	return u.Clauses(dbresolver.Read)
}

func (u userDo) WriteDB() IUserDo {
	return u.Clauses(dbresolver.Write)
}

func (u userDo) Session(config *gorm.Session) IUserDo {
	return u.withDO(u.DO.Session(config))
}

func (u userDo) Clauses(conds ...clause.Expression) IUserDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userDo) Returning(value interface{}, columns ...string) IUserDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userDo) Not(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userDo) Or(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userDo) Select(conds ...field.Expr) IUserDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userDo) Where(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userDo) Order(conds ...field.Expr) IUserDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userDo) Distinct(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userDo) Omit(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userDo) Join(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userDo) Group(cols ...field.Expr) IUserDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userDo) Having(conds ...gen.Condition) IUserDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userDo) Limit(limit int) IUserDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userDo) Offset(offset int) IUserDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userDo) Unscoped() IUserDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userDo) Create(values ...*models.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userDo) CreateInBatches(values []*models.User, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userDo) Save(values ...*models.User) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userDo) First() (*models.User, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.User), nil
	}
}

func (u userDo) Take() (*models.User, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.User), nil
	}
}

func (u userDo) Last() (*models.User, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.User), nil
	}
}

func (u userDo) Find() ([]*models.User, error) {
	result, err := u.DO.Find()
	return result.([]*models.User), err
}

func (u userDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.User, err error) {
	buf := make([]*models.User, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userDo) FindInBatches(result *[]*models.User, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userDo) Attrs(attrs ...field.AssignExpr) IUserDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userDo) Assign(attrs ...field.AssignExpr) IUserDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userDo) Joins(fields ...field.RelationField) IUserDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userDo) Preload(fields ...field.RelationField) IUserDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userDo) FirstOrInit() (*models.User, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.User), nil
	}
}

func (u userDo) FirstOrCreate() (*models.User, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.User), nil
	}
}

func (u userDo) FindByPage(offset int, limit int) (result []*models.User, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userDo) Delete(models ...*models.User) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userDo) withDO(do gen.Dao) *userDo {
	u.DO = *do.(*gen.DO)
	return u
}
