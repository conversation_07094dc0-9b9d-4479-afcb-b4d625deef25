# Validation

<a href="/godoc/pkg/gitlab.finema.co/finema/idin-core/index.html#BaseValidator" target="_blank">All
validator function</a>

Base on
1. [asaskevich/govalidator: [Go] Package of validators and sanitizers for strings, numerics, slices and structs (github.com)](https://github.com/asaskevich/govalidator)
2. [thedevsaddam/gojsonq: A simple Go package to Query over JSON/YAML/XML/CSV Data (github.com)](https://github.com/thedevsaddam/gojsonq)

Example
-------------
::: code-group

```go{9} [requests/user_create.request.go]
package requests

import (
  "gitlab.finema.co/finema/golang-template/models"
  core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
  core.BaseValidator 
  Email    *string `json:"email"`      // Email field for user creation
  FullName *string `json:"full_name"`  // Full name field for user creation
}


func (r *UserCreate) Valid(ctx core.IContext) core.IError {
  // Check if the email is valid format
  r.Must(r.IsEmail(r.Email, "email"))
  
  // Check if the email is not empty
  r.Must(r.IsStrRequired(r.Email, "email"))
  
  // Check if the email is unique in the database
  r.Must(r.IsStrUnique(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

  // Check if the full name is not empty
  r.Must(r.IsStrRequired(r.FullName, "full_name")) 

  return r.Error()
}

```

```go{15-21} [modules/user/user.controller.go]
package user

import (
	"gitlab.finema.co/finema/golang-template/requests"
	"gitlab.finema.co/finema/golang-template/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
	"net/http"
)

type UserController struct {
}

func (m UserController) Create(c core.IHTTPContext) error {
	// Create an instance of UserCreate struct to hold the request data
	input := &requests.UserCreate{}

	// Bind the request body to the input struct and validate the input
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	// Create an instance of UserService
	userSvc := services.NewUserService(c)

	// Create a payload to pass to the userSvc.Create method
	payload := &services.UserCreatePayload{}
	_ = utils.Copy(payload, input)

	// Call the userSvc.Create method to create a new user
	user, err := userSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	// Return the created user as JSON response
	return c.JSON(http.StatusCreated, user)
}

```
:::

## Example response (Return HTTP Status 400)

```json

{
    "code": "INVALID_PARAMS",
    "message": "Invalid parameters",
    "fields": {
        "password": {
            "code": "REQUIRED",
            "message": "The password field is required"
        },
        "username": {
            "code": "REQUIRED",
            "message": "The username field is required"
        }
    }
}
```

## All validators
--------------
Here's the list of functions organized by function category in a table format:

### String
--------------

| Function Name     | Function Name     | Function Name      |
|-------------------|-------------------|--------------------|
| IsStrIn           | IsStrMax          | IsStrMin           |
| IsStrRequired     | IsStrUnique       | IsStringContain    |
| IsStringEndWith   | IsStringLowercase | IsStringNotContain |
| IsStringNumber    | IsStringNumberMin | IsStringStartWith  |
| IsStringUppercase |

### Array

| Function Name  | Function Name  | Function Name |
|----------------|----------------|---------------|
| IsArrayBetween | IsArrayBetween | IsArrayMax    |
| IsArrayMax     | IsArrayMin     | IsArrayMin    |
| IsArraySize    | IsArraySize    |               |

### Number
--------------

| Function Name        | Function Name    | Function Name    |
|----------------------|------------------|------------------|
| IsFloatNumberBetween | IsFloatNumberMax | IsFloatNumberMin |
| IsNumberBetween      | IsNumberMax      | IsNumberMin      |

### Date and Time
--------------

| Function Name  | Function Name   | Function Name    |
|----------------|-----------------|------------------|
| IsDateTime     | IsDateTimeAfter | IsDateTimeBefore |
| IsTime         | IsTimeAfter     | IsTimeBefore     |
| IsTimeRequired |

### JSON
--------------

| Function Name          | Function Name             | Function Name         |
|------------------------|---------------------------|-----------------------|
| IsJSONArray            | IsJSONArrayMax            | IsJSONArrayMin        |
| IsJSONBoolPathRequired | IsJSONObject              | IsJSONObjectNotEmpty  |
| IsJSONObjectPath       | IsJSONPathRequireNotEmpty | IsJSONPathRequired    |
| IsJSONPathStrIn        | IsJSONRequired            | IsJSONStrPathRequired |
| LoopJSONArray          |                           |                       |

### Email
--------------

| Function Name |
|---------------|
| IsEmail       |

### Database
--------------

| Function Name              | Function Name         |
|----------------------------|-----------------------|
| IsExists                   | IsExistsWithCondition |
| IsMongoExistsWithCondition | IsMongoStrUnique      |
| IsMongoExistsWithCondition | IsStrUnique           |

### Base64

| Function Name | 
|---------------|
| IsBase64      | 

### Boolean
--------------

| Function Name  | Function Name |
|----------------|---------------|
| IsBoolRequired |               |

### Custom

| Function Name | Function Name |
|---------------|---------------|
| IsCustom      |               |

### IP Address
--------------

| Function Name |
|---------------|
| IsIP          |

### Required
--------------

| Function Name | Function Name   |
|---------------|-----------------|
| IsRequired    | IsRequiredArray |

### URL
--------------

| Function Name |
|---------------|
| IsURL         |
