package core

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/idin-core/utils"
	"net/http"
)

type MockMiddlewareWrapper func(model interface{}) interface{}
type MockMiddlewareManual func(c IHTTPContext) error
type MockMiddlewareOptions struct {
	Wrapper      MockMiddlewareWrapper
	Manual       MockMiddlewareManual
	IsPagination bool
	IsDisabled   bool
}

func MockMiddleware(model interface{}, options *MockMiddlewareOptions) func(next echo.HandlerFunc) echo.HandlerFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			cc := c.(*HTTPContext)

			if options != nil {
				if options.IsDisabled == true && !cc.ENV().IsMock() {
					return next(cc)
				}
				if options.Manual != nil {
					return options.Manual(cc)
				}
				err := Fake(model)
				if err != nil {
					return c.JSON(http.StatusInternalServerError, Map{
						"message": err.Error(),
					})
				}

				if options.IsPagination == true {
					pagination := NewPagination(model, &PageResponse{
						Total: cc.GetPageOptions().Limit * 5,
						Limit: cc.GetPageOptions().Limit,
						Count: cc.GetPageOptions().Limit,
						Page:  cc.GetPageOptions().Page,
						Q:     cc.GetPageOptions().Q,
					})

					items := make([]interface{}, 0)
					err = utils.MapToStruct(&pagination.Items, &items)
					if err != nil {
						return c.JSON(http.StatusOK, pagination)
					}

					if cc.GetPageOptions().Limit <= int64(len(items)) {
						pagination.Items = items[:cc.GetPageOptions().Limit]
					}

					return c.JSON(http.StatusOK, pagination)
				}

				if options.Wrapper != nil {
					return c.JSON(http.StatusOK, options.Wrapper(model))
				}
			}

			return c.JSON(http.StatusOK, model)
		}
	}
}
