// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOTemplateChecklistItem(db *gorm.DB, opts ...gen.DOOption) pMOTemplateChecklistItem {
	_pMOTemplateChecklistItem := pMOTemplateChecklistItem{}

	_pMOTemplateChecklistItem.pMOTemplateChecklistItemDo.UseDB(db, opts...)
	_pMOTemplateChecklistItem.pMOTemplateChecklistItemDo.UseModel(&models.PMOTemplateChecklistItem{})

	tableName := _pMOTemplateChecklistItem.pMOTemplateChecklistItemDo.TableName()
	_pMOTemplateChecklistItem.ALL = field.NewAsterisk(tableName)
	_pMOTemplateChecklistItem.ID = field.NewString(tableName, "id")
	_pMOTemplateChecklistItem.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOTemplateChecklistItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOTemplateChecklistItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOTemplateChecklistItem.TabKey = field.NewString(tableName, "tab_key")
	_pMOTemplateChecklistItem.Detail = field.NewString(tableName, "detail")
	_pMOTemplateChecklistItem.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOTemplateChecklistItem.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOTemplateChecklistItem.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOTemplateChecklistItem.CreatedBy = pMOTemplateChecklistItemBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("CreatedBy.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("CreatedBy.Checkins.User", "models.User"),
			},
		},
	}

	_pMOTemplateChecklistItem.UpdatedBy = pMOTemplateChecklistItemBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOTemplateChecklistItem.fillFieldMap()

	return _pMOTemplateChecklistItem
}

type pMOTemplateChecklistItem struct {
	pMOTemplateChecklistItemDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	TabKey      field.String
	Detail      field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	CreatedBy   pMOTemplateChecklistItemBelongsToCreatedBy

	UpdatedBy pMOTemplateChecklistItemBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOTemplateChecklistItem) Table(newTableName string) *pMOTemplateChecklistItem {
	p.pMOTemplateChecklistItemDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOTemplateChecklistItem) As(alias string) *pMOTemplateChecklistItem {
	p.pMOTemplateChecklistItemDo.DO = *(p.pMOTemplateChecklistItemDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOTemplateChecklistItem) updateTableName(table string) *pMOTemplateChecklistItem {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.TabKey = field.NewString(table, "tab_key")
	p.Detail = field.NewString(table, "detail")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOTemplateChecklistItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOTemplateChecklistItem) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 11)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["detail"] = p.Detail
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOTemplateChecklistItem) clone(db *gorm.DB) pMOTemplateChecklistItem {
	p.pMOTemplateChecklistItemDo.ReplaceConnPool(db.Statement.ConnPool)
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOTemplateChecklistItem) replaceDB(db *gorm.DB) pMOTemplateChecklistItem {
	p.pMOTemplateChecklistItemDo.ReplaceDB(db)
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOTemplateChecklistItemBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a pMOTemplateChecklistItemBelongsToCreatedBy) Where(conds ...field.Expr) *pMOTemplateChecklistItemBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOTemplateChecklistItemBelongsToCreatedBy) WithContext(ctx context.Context) *pMOTemplateChecklistItemBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOTemplateChecklistItemBelongsToCreatedBy) Session(session *gorm.Session) *pMOTemplateChecklistItemBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOTemplateChecklistItemBelongsToCreatedBy) Model(m *models.PMOTemplateChecklistItem) *pMOTemplateChecklistItemBelongsToCreatedByTx {
	return &pMOTemplateChecklistItemBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOTemplateChecklistItemBelongsToCreatedBy) Unscoped() *pMOTemplateChecklistItemBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOTemplateChecklistItemBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOTemplateChecklistItemBelongsToCreatedByTx) Unscoped() *pMOTemplateChecklistItemBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOTemplateChecklistItemBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOTemplateChecklistItemBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOTemplateChecklistItemBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOTemplateChecklistItemBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOTemplateChecklistItemBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOTemplateChecklistItemBelongsToUpdatedBy) Session(session *gorm.Session) *pMOTemplateChecklistItemBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOTemplateChecklistItemBelongsToUpdatedBy) Model(m *models.PMOTemplateChecklistItem) *pMOTemplateChecklistItemBelongsToUpdatedByTx {
	return &pMOTemplateChecklistItemBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOTemplateChecklistItemBelongsToUpdatedBy) Unscoped() *pMOTemplateChecklistItemBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOTemplateChecklistItemBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOTemplateChecklistItemBelongsToUpdatedByTx) Unscoped() *pMOTemplateChecklistItemBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOTemplateChecklistItemDo struct{ gen.DO }

type IPMOTemplateChecklistItemDo interface {
	gen.SubQuery
	Debug() IPMOTemplateChecklistItemDo
	WithContext(ctx context.Context) IPMOTemplateChecklistItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOTemplateChecklistItemDo
	WriteDB() IPMOTemplateChecklistItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOTemplateChecklistItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOTemplateChecklistItemDo
	Not(conds ...gen.Condition) IPMOTemplateChecklistItemDo
	Or(conds ...gen.Condition) IPMOTemplateChecklistItemDo
	Select(conds ...field.Expr) IPMOTemplateChecklistItemDo
	Where(conds ...gen.Condition) IPMOTemplateChecklistItemDo
	Order(conds ...field.Expr) IPMOTemplateChecklistItemDo
	Distinct(cols ...field.Expr) IPMOTemplateChecklistItemDo
	Omit(cols ...field.Expr) IPMOTemplateChecklistItemDo
	Join(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo
	Group(cols ...field.Expr) IPMOTemplateChecklistItemDo
	Having(conds ...gen.Condition) IPMOTemplateChecklistItemDo
	Limit(limit int) IPMOTemplateChecklistItemDo
	Offset(offset int) IPMOTemplateChecklistItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOTemplateChecklistItemDo
	Unscoped() IPMOTemplateChecklistItemDo
	Create(values ...*models.PMOTemplateChecklistItem) error
	CreateInBatches(values []*models.PMOTemplateChecklistItem, batchSize int) error
	Save(values ...*models.PMOTemplateChecklistItem) error
	First() (*models.PMOTemplateChecklistItem, error)
	Take() (*models.PMOTemplateChecklistItem, error)
	Last() (*models.PMOTemplateChecklistItem, error)
	Find() ([]*models.PMOTemplateChecklistItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOTemplateChecklistItem, err error)
	FindInBatches(result *[]*models.PMOTemplateChecklistItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOTemplateChecklistItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOTemplateChecklistItemDo
	Assign(attrs ...field.AssignExpr) IPMOTemplateChecklistItemDo
	Joins(fields ...field.RelationField) IPMOTemplateChecklistItemDo
	Preload(fields ...field.RelationField) IPMOTemplateChecklistItemDo
	FirstOrInit() (*models.PMOTemplateChecklistItem, error)
	FirstOrCreate() (*models.PMOTemplateChecklistItem, error)
	FindByPage(offset int, limit int) (result []*models.PMOTemplateChecklistItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOTemplateChecklistItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOTemplateChecklistItemDo) Debug() IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOTemplateChecklistItemDo) WithContext(ctx context.Context) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOTemplateChecklistItemDo) ReadDB() IPMOTemplateChecklistItemDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOTemplateChecklistItemDo) WriteDB() IPMOTemplateChecklistItemDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOTemplateChecklistItemDo) Session(config *gorm.Session) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOTemplateChecklistItemDo) Clauses(conds ...clause.Expression) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOTemplateChecklistItemDo) Returning(value interface{}, columns ...string) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOTemplateChecklistItemDo) Not(conds ...gen.Condition) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOTemplateChecklistItemDo) Or(conds ...gen.Condition) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOTemplateChecklistItemDo) Select(conds ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOTemplateChecklistItemDo) Where(conds ...gen.Condition) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOTemplateChecklistItemDo) Order(conds ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOTemplateChecklistItemDo) Distinct(cols ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOTemplateChecklistItemDo) Omit(cols ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOTemplateChecklistItemDo) Join(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOTemplateChecklistItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOTemplateChecklistItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOTemplateChecklistItemDo) Group(cols ...field.Expr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOTemplateChecklistItemDo) Having(conds ...gen.Condition) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOTemplateChecklistItemDo) Limit(limit int) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOTemplateChecklistItemDo) Offset(offset int) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOTemplateChecklistItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOTemplateChecklistItemDo) Unscoped() IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOTemplateChecklistItemDo) Create(values ...*models.PMOTemplateChecklistItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOTemplateChecklistItemDo) CreateInBatches(values []*models.PMOTemplateChecklistItem, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOTemplateChecklistItemDo) Save(values ...*models.PMOTemplateChecklistItem) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOTemplateChecklistItemDo) First() (*models.PMOTemplateChecklistItem, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateChecklistItem), nil
	}
}

func (p pMOTemplateChecklistItemDo) Take() (*models.PMOTemplateChecklistItem, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateChecklistItem), nil
	}
}

func (p pMOTemplateChecklistItemDo) Last() (*models.PMOTemplateChecklistItem, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateChecklistItem), nil
	}
}

func (p pMOTemplateChecklistItemDo) Find() ([]*models.PMOTemplateChecklistItem, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOTemplateChecklistItem), err
}

func (p pMOTemplateChecklistItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOTemplateChecklistItem, err error) {
	buf := make([]*models.PMOTemplateChecklistItem, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOTemplateChecklistItemDo) FindInBatches(result *[]*models.PMOTemplateChecklistItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOTemplateChecklistItemDo) Attrs(attrs ...field.AssignExpr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOTemplateChecklistItemDo) Assign(attrs ...field.AssignExpr) IPMOTemplateChecklistItemDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOTemplateChecklistItemDo) Joins(fields ...field.RelationField) IPMOTemplateChecklistItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOTemplateChecklistItemDo) Preload(fields ...field.RelationField) IPMOTemplateChecklistItemDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOTemplateChecklistItemDo) FirstOrInit() (*models.PMOTemplateChecklistItem, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateChecklistItem), nil
	}
}

func (p pMOTemplateChecklistItemDo) FirstOrCreate() (*models.PMOTemplateChecklistItem, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOTemplateChecklistItem), nil
	}
}

func (p pMOTemplateChecklistItemDo) FindByPage(offset int, limit int) (result []*models.PMOTemplateChecklistItem, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOTemplateChecklistItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOTemplateChecklistItemDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOTemplateChecklistItemDo) Delete(models ...*models.PMOTemplateChecklistItem) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOTemplateChecklistItemDo) withDO(do gen.Dao) *pMOTemplateChecklistItemDo {
	p.DO = *do.(*gen.DO)
	return p
}
