package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/models/query"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm/clause"
)

type IPMOProjectBidbondService interface {
	Create(input *dto.PMOBidbondCreatePayload) (*models.PMOBidbondInfo, core.IError)
	Find(projectID string) (*models.PMOBidbondInfo, core.IError)
	BidbondVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBidbondInfoVersion], core.IError)
}

type pmoProjectBidbondService struct {
	ctx core.IContext
}

// PMO Bidbond methods implementation
func (s pmoProjectBidbondService) Create(input *dto.PMOBidbondCreatePayload) (*models.PMOBidbondInfo, core.IError) {
	// Check if bidbond already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	bidbond := &models.PMOBidbondInfo{
		BaseModel:      models.NewBaseModel(),
		ProjectID:      input.ProjectID,
		GuaranteeAsset: input.GuaranteeAsset,
		BidbondPayer:   input.BidbondPayer,
		BidbondValue:   input.BidbondValue,
		StartDate:      input.StartDate,
		EndDate:        input.EndDate,
		DurationMonth:  input.DurationMonth,
		DurationYear:   input.DurationYear,
		Fee:            input.Fee,
		CreatedByID:    utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOBidbondInfo(s.ctx).Create(bidbond)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOBidbondInfoVersion{
		PMOBidbondInfo: *bidbond,
		OriginalID:     bidbond.ID,
	}

	ierr = repositories.PMOBidbondInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(bidbond.ProjectID)
}

func (s pmoProjectBidbondService) update(existing *models.PMOBidbondInfo, input *dto.PMOBidbondCreatePayload) (*models.PMOBidbondInfo, core.IError) {
	// Update the main record
	existing.GuaranteeAsset = input.GuaranteeAsset
	existing.BidbondPayer = input.BidbondPayer
	existing.BidbondValue = input.BidbondValue
	existing.StartDate = input.StartDate
	existing.EndDate = input.EndDate
	existing.DurationMonth = input.DurationMonth
	existing.DurationYear = input.DurationYear
	existing.Fee = input.Fee
	existing.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)
	existing.UpdatedAt = utils.GetCurrentDateTime()

	updateMap := map[string]interface{}{
		"guarantee_asset": input.GuaranteeAsset,
		"bidbond_payer":   input.BidbondPayer,
		"bidbond_value":   input.BidbondValue,
		"start_date":      input.StartDate,
		"end_date":        input.EndDate,
		"duration_month":  input.DurationMonth,
		"duration_year":   input.DurationYear,
		"fee":             input.Fee,
		"updated_by_id":   s.ctx.GetUser().ID,
		"updated_at":      utils.GetCurrentDateTime(),
	}

	ierr := repositories.PMOBidbondInfo(s.ctx).Where("id = ?", existing.ID).Updates(updateMap)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOBidbondInfoVersion{
		PMOBidbondInfo: *existing,
		OriginalID:     existing.ID,
	}

	ierr = repositories.PMOBidbondInfoVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectBidbondService) Find(projectID string) (*models.PMOBidbondInfo, core.IError) {
	return repositories.PMOBidbondInfo(s.ctx,
		repositories.PMOBidbondInfoWithRelations(),
	).FindOne(query.PMOBidbondInfo.ProjectID.Eq(projectID))
}

func (s pmoProjectBidbondService) BidbondVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBidbondInfoVersion], core.IError) {
	// First get the bidbond info to get the original ID
	bidbond, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOBidbondInfoVersion(s.ctx,
		repositories.PMOBidbondInfoVersionOrderBy(pageOptions),
		repositories.PMOBidbondInfoVersionByBidbondInfoID(bidbond.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectBidbondService(ctx core.IContext) IPMOProjectBidbondService {
	return &pmoProjectBidbondService{ctx: ctx}
}
