package core

import (
	"github.com/golang-jwt/jwt"
	"time"
)

type IJWTInjector interface {
	Valid(ctx IContext) IError
}

type JWTClaims[T IJWTInjector] struct {
	BaseValidator
	ExpiresAt *int64    `json:"exp,omitempty"`
	IssuedAt  *int64    `json:"iat,omitempty"`
	Issuer    *string   `json:"iss,omitempty"`
	ID        *string   `json:"jti,omitempty"`
	NotBefore *int64    `json:"nbf,omitempty"`
	Nonce     *string   `json:"nonce,omitempty"`
	Subject   *string   `json:"sub,omitempty"`
	Audience  *string   `json:"aud,omitempty"`
	VC        *JWTVC[T] `json:"vc,omitempty"`
	VP        *JWTVP    `json:"vp,omitempty"`
}

func (r JWTClaims[T]) Valid(ctx IContext) IError {
	r.Must(r.IsRequired(r.ExpiresAt, "claims.exp"))
	r.Must(r.IsRequired(r.IssuedAt, "claims.iat"))
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.Issuer, "claims.iss"))
	r.Must(r.IsStrRequired(r.ID, "claims.jti"))
	r.Must(r.IsRequired(r.NotBefore, "claims.nbf"))
	r.Must(r.IsStrRequired(r.Nonce, "claims.nonce"))
	r.Must(r.IsStrRequired(r.Subject, "claims.sub"))
	if r.VC != nil {
		return r.VC.Valid(ctx)
	}
	if r.VP != nil {
		r.Must(r.IsRequiredArray(r.VP.Context, "claims.vp.context"))
		r.Must(r.IsRequiredArray(r.VP.Type, "claims.vp.type"))
		r.Must(r.IsRequiredArray(r.VP.VerifiableCredential, "claims.vp.verifiableCredential"))
	}

	return r.Error()
}

type JWTVC[T IJWTInjector] struct {
	BaseValidator
	Context           []*string  `json:"@context"`
	ID                *string    `json:"id"`
	Type              []*string  `json:"type"`
	Issuer            *string    `json:"issuer"`
	IssuanceDate      *time.Time `json:"issuanceDate"`
	CredentialSubject *T         `json:"credentialSubject,omitempty"`
	DIDDocSubject     *T         `json:"didDocSubject,omitempty"`
}

func (r JWTVC[IJWTInjector]) Valid(ctx IContext) IError {
	r.Must(r.IsRequiredArray(r.Context, "claims.vc.context"))
	r.Must(r.IsStrRequired(r.ID, "claims.vc.id"))
	r.Must(r.IsRequiredArray(r.Type, "claims.vc.type"))
	r.Must(r.IsStrRequired(r.Issuer, "claims.vc.issuer"))
	r.Must(r.IsTimeRequired(r.IssuanceDate, "claims.vc.issuanceDate"))
	if r.CredentialSubject != nil {
		return (*r.CredentialSubject).Valid(ctx)
	}

	return r.Error()
}

type JWTVP struct {
	Context              []*string `json:"@context"`
	Type                 []*string `json:"type"`
	VerifiableCredential []*string `json:"verifiableCredential"`
}

type JWT[T IJWTInjector] struct {
	BaseValidator
	Header    *map[string]interface{} `json:"header"`
	Claims    *JWTClaims[T]           `json:"claims"`
	Signature *string                 `json:"signature"`
}

type JWTMap map[string]interface{}

func (r JWTMap) Valid(ctx IContext) IError {
	return nil
}

func (r JWT[IJWTInjector]) Valid(ctx IContext) IError {
	r.Must(r.IsRequired(r.Header, "header"))
	if r.Must(r.IsRequired(r.Claims, "claims")) {
		return (*r.Claims).Valid(ctx)
	}
	r.Must(r.IsStrRequired(r.Signature, "signature"))

	return r.Error()
}

func JWTDecode(tokenStr string) (*jwt.Token, error) {
	claims := jwt.MapClaims{}
	token, err := jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(""), nil
	})

	if err != nil {
		if token == nil {
			return nil, err
		}

		return token, nil
	}

	return token, nil
}
