# แนวคิดสำคัญของโครงสร้างโปรเจค

เอกสารนี้ให้ภาพรวมอย่างละเอียดเกี่ยวกับแนวคิดสำคัญของโครงสร้างพื้นฐานสำหรับโปรเจคที่พัฒนาด้วย Go โดยเพื่อแสดงตัวอย่างการจัดระเบียบและโครงสร้างของโปรเจค โค้ดสั้นๆ ในเอกสารนี้จะถูกแทรกเป็นตัวอย่าง

โครงสร้างของโปรเจคเป็นการอ้างอิงไปยังรหัสฐานข้อมูลที่มีอยู่ที่: https://gitlab.finema.co/finema/golang-template

เรามาตรวจสอบโครงสร้างไดเรกทอรีต่อไปนี้เป็นตัวอย่าง:

```
├── cmd
│   └── api.go                     # จุดเข้าหลักสำหรับแอปพลิเคชัน API
├── consts
│   └── env.const.go               # ค่าคงที่สำหรับตัวแปรสภาพแวดล้อม
├── emsgs
│   └── user.error.go              # ข้อความข้อผิดพลาดสำหรับโมดูลผู้ใช้
├── helpers
│   └── string.helper.go           # ฟังก์ชันช่วยสำหรับการจัดการสตริง
├── migrations
│   └── 20220912084753_users.ts    # ไฟล์การโยกย้ายฐานข้อมูลตารางผู้ใช้
├── models
│   ├── base.model.go              # โมเดลฐานสำหรับฟิลด์ที่ใช้ร่วมกันในโมเดลฐานข้อมูล
├── modules
│   └── user
│       ├── user.controller.go     # ควบคุมการดำเนินการของโมดูลผู้ใช้
│       └── user.http.go           # เส้นทางและตัวจัดการ HTTP สำหรับโมดูลผู้ใช้
├── repo
│   └── base.repo.go               # รีพอร์ตที่ใช้ร่วมกันสำหรับการดำเนินการฐานข้อมูลทั่วไป
├── requests
│   ├── user_create.request.go     # โครงสร้างคำขอสำหรับการสร้างผู้ใช้ใหม่
├── services
│   ├── user.dto.go                # อ็อบเจกต์การถ่ายโอนข้อมูล (DTO) สำหรับโมดูลผู้ใช้
│   └── user.service.go            # เซอร์วิสสำหรับตรรกะธุรกิจโมดูลผู้ใช้
├── views
│   └── user.view.go               # วิวสำหรับการแสดงข้อมูลที่เกี่ยวข้องกับผู้ใช้
├── .env                           # ไฟล์กำหนดค่าสำหรับตัวแปรสภาพแวดล้อม
├── main.go                        # ไฟล์แอปพลิเคชันหลัก
├── go.mod                         # ไฟล์โมดูล Go
├── go.sum                         # ไฟล์ตรวจสอบสำหรับโมดูล Go
```

## คำอธิบายแพ็คเกจ

### แพ็คเกจ `main`

แพ็คเกจ `main` รวมถึงฟังก์ชันหลักของแอปพลิเคชันและเป็นจุดเริ่มต้นของโปรแกรม

### แพ็คเกจ `cmd`

แพ็คเกจ `cmd` เก็บจุดเข้าของแอปพลิเคชันและเริ่มต้นเซิร์ฟเวอร์ API

#### ฟังก์ชัน `APIRun()`

ฟังก์ชันนี้รับผิดชอบในการเริ่มต้นคอมโพเนนต์ที่สำคัญรวมทั้งสภาพแวดล้อม เชื่อมต่อฐานข้อมูล และเซิร์ฟเวอร์ HTTP นอกจากนี้ยังสร้างเส้นทางสำหรับโมดูลต่างๆ และเปิดใช้งานเซิร์ฟเวอร์ HTTP

### แพ็คเกจ `modules/user`

แพ็คเกจนี้ถูกกำหนดให้ใช้งานฟังก์ชันที่เกี่ยวกับผ

ู้ใช้ซึ่งรวมถึงตัวจัดการ HTTP และการดำเนินการของเซอร์วิส

#### ฟังก์ชัน `NewUserHTTP(e *echo.Echo)`

ฟังก์ชันนี้ตั้งค่าเส้นทางที่เน้นผู้ใช้บนอินสแตนซ์ `Echo` ที่กำหนด (`e`) และจับคู่เส้นทางแต่ละเส้นทางกับฟังก์ชันตัวจัดการ HTTP ที่เรียกว่า `UserController`

#### โครงสร้าง `UserController`

โครงสร้างนี้ระบุวิธีการจัดการคำขอ HTTP ที่เกี่ยวข้องกับผู้ใช้:

- การแบ่งหน้า: รับข้อมูลผู้ใช้ที่แบ่งหน้า
- การค้นหา: ค้นหาผู้ใช้ตาม ID
- การสร้าง: สร้างผู้ใช้ใหม่
- การอัปเดต: แก้ไขผู้ใช้ที่มีอยู่
- การลบ: ลบผู้ใช้

### แพ็คเกจ `services`

แพ็คเกจนี้รวมถึงการสร้างเซอร์วิสสำหรับฟังก์ชันต่างๆ ที่เกี่ยวข้องกับผู้ใช้

#### อินเตอร์เฟซ `IUserService`

อินเตอร์เฟซ `IUserService` ระบุแบบแผนการใช้งานของเซอร์วิสผู้ใช้ซึ่งรวมถึงวิธีการสร้าง อัปเดต ค้นหา แบ่งหน้า และลบผู้ใช้

#### โครงสร้าง `userService`

โครงสร้าง `userService` ดำเนินการที่มีตัวอย่างสำหรับการดำเนินการเกี่ยวกับผู้ใช้ เช่น การสร้างผู้ใช้ การอัปเดตผู้ใช้ การค้นหาผู้ใช้ การแบ่งหน้าผู้ใช้ และการลบผู้ใช้

### แพ็คเกจ `repo`

แพ็คเกจ `repo` จัดการการบันทึก

และการเรียกคืนข้อมูลโดยใช้ไลบรารี gorm

## กระบวนการประมวลผลของโค้ด

1. ฟังก์ชัน `main` ในแพ็คเกจ `main` ทำหน้าที่เป็นจุดเริ่มต้นของแอปพลิเคชัน
2. ฟังก์ชันนี้เรียกใช้ฟังก์ชัน `APIRun` จากแพ็คเกจ `cmd`
3. ฟังก์ชัน `APIRun` ทำหน้าที่เริ่มต้นสภาพแวดล้อมแอปพลิเคชันและเชื่อมต่อฐานข้อมูล
4. ฟังก์ชันนี้สร้างเซิร์ฟเวอร์ HTTP โดยใช้ฟังก์ชัน `core.NewHTTPServer` โดยส่งค่าตัวเลือกของ context ที่จำเป็น เช่น ฐานข้อมูลและสภาพแวดล้อม
5. ฟังก์ชัน `APIRun` ลงทะเบียนเส้นทาง HTTP สำหรับการจัดการคำขอที่เกี่ยวข้องกับโมดูลหน้าแรกและผู้ใช้ โดยใช้ฟังก์ชัน `home.NewHomeHTTP` และ `user.NewUserHTTP` ตามลำดับ
6. ฟังก์ชัน `NewUserHTTP` ภายในแพ็คเกจ `user` ตั้งค่าเส้นทาง HTTP สำหรับการดำเนินการที่เกี่ยวข้องกับผู้ใช้และเชื่อมต่อกับตัวจัดการที่เกี่ยวข้องจาก `UserController`
7. เส้นทาง HTTP แต่ละเส้นทางในฟังก์ชัน `NewUserHTTP` ถูกเชื่อมโยงกับเมธอดตัวจัดการที่เฉพาะเจาะจงใน `UserController` (เช่น `Pagination`, `Find`, `Create`, `Update`, `Delete`)
8. เมื่อผู้ใช้ส่งคำขอ HTTP ไปยังหนึ่งในเส้นทางที่ลงทะเบียนแล้ว เมธอดตัวจัดการที่เฉพาะเจาะจง

ใน `UserController` จะถูกเรียกใช้
9. เมื่อเรียกใช้เมธอดตัวจัดการใน `UserController` ทุกเมธอดจะส่งคำขอไปยังเซอร์วิสผู้ใช้เพื่อดำเนินการเพิ่มเติม
10. เมธอดในเซอร์วิสผู้ใช้ (`Create`, `Update`, `Find`, `Pagination`, `Delete`) ร่วมมือกับรีพอร์ตที่จัดการการกระทำ CRUD กับข้อมูลผู้ใช้ในฐานข้อมูล
11. เมธอดในรีพอร์ตใช้การเชื่อมต่อฐานข้อมูลเพื่อดำเนินการคิวรี SQL ที่จำเป็นและส่งผลลัพธ์กลับไปยังเซอร์วิสผู้ใช้
12. เมธอดในเซอร์วิสผู้ใช้ประมวลผลข้อมูลที่ได้รับจากรีพอร์ตและส่งข้อมูลที่เกี่ยวข้องกลับไปยังเมธอดตัวจัดการใน `UserController`
13. เมธอดตัวจัดการใน `UserController` จัดรูปแบบข้อมูลที่ได้รับเป็น JSON และส่งกลับเป็นตอบสนอง HTTP ให้กับผู้ใช้

นี้เป็นภาพรวมระดับสูงของกระบวนการประมวลผลโค้ดภายในโครงสร้างของโค้ดที่ให้มา

## สรุป

โครงสร้างนี้เป็นการติดตามแนวทางเชิงชั้นโดยแยกความสำคัญออกเป็นแพ็คเกจที่แตกต่างกัน แพ็คเกจ `cmd` รับผิดชอบต่อจุดเริ่มต้นของแอปพลิเคชัน ในขณะที่แพ็คเกจ `user` ให้ความสำคัญกับฟังก์ชันที่เกี่ยวข้องกับผู้ใช้ แพ็คเกจ `services` ให้ตรรกะธุรกิ

จสำหรับการดำเนินงานเกี่ยวกับผู้ใช้ และแพ็คเกจ `repo` จัดการการบันทึกและการเรียกคืนข้อมูล

โดยปฏิบัติตามโครงสร้างนี้ โค้ดเบสจะเรียบร้อยและมีโมดูลที่สามารถขยายได้ง่ายในอนาคตและสะดวกในการบำรุงรักษา
