# Repository

This document provides an overview of a repository implementation for common database operations in Golang. The code you provided includes a repository package and a services package, which work together to perform CRUD (Create, Read, Update, Delete) operations on a user entity.

## Repository Package

### User Repository

The user repository provides methods for interacting with the user entity in the database.

#### User

The `User` variable is a function that creates a new instance of the user repository with optional customizations.

```go
var User = repository.Make[models.User]()
```

#### UserOrderBy

The `UserOrderBy` function is a user repository option that sets the order for retrieving user records.

```go
func UserOrderBy(pageOptions *core.PageOptions) repository.Option[models.User] {
    return func(c repository.IRepository[models.User]) {
        if len(pageOptions.OrderBy) == 0 {
            c.Order("created_at DESC")
        } else {
            c.Order(pageOptions.OrderBy)
        }
    }
}
```

## Services Package

### User Service Interface

The `IUserService` interface defines the contract for user-related operations.

```go
type IUserService interface {
    Create(input *UserCreatePayload) (*models.User, core.IError)
    Update(id string, input *UserUpdatePayload) (*models.User, core.IError)
    Find(id string) (*models.User, core.IError)
    Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError)
    Delete(id string) core.IError
}
```

### User Service Implementation

The `userService` struct implements the `IUserService` interface.

```go
type userService struct {
    ctx core.IContext
}
```

#### Create

The `Create` method creates a new user entity.

```go
func (s userService) Create(input *UserCreatePayload) (*models.User, core.IError) {
    // Implementation for creating a user

    // ...

    return s.Find(user.ID)
}
```

#### Update

The `Update` method updates an existing user entity.

```go
func (s userService) Update(id string, input *UserUpdatePayload) (*models.User, core.IError) {
    // Implementation for updating a user

    // ...

    return s.Find(user.ID)
}
```

#### Find

The `Find` method retrieves a user entity by ID.

```go
func (s userService) Find(id string) (*models.User, core.IError) {
    // Implementation for finding a user

    // ...

    return repo.User(s.ctx).FindOne("id = ?", id)
}
```

#### Pagination

The `Pagination` method performs pagination on user entities.

```go
func (s userService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.User], core.IError) {
    // Implementation for user pagination

    // ...

    return repo.User(s.ctx, repo.UserOrderBy(pageOptions)).Pagination(pageOptions)
}
```

#### Delete

The `Delete` method deletes a user entity by ID.

```go
func (s userService) Delete(id string) core.IError {
    // Implementation for deleting a user

    // ...

    return repo.User(s.ctx).Delete("id = ?", id)
}
```

This concludes the overview of the repository for common database operations in Golang. The provided code demonstrates the implementation of CRUD operations on a user entity using a repository pattern.
