import {defineConfig} from 'vitepress'


// https://vitepress.dev/reference/site-config
export default defineConfig({
  locales: {
    root: {
      label: 'English',
      lang: 'en'
    },
    th: {
      label: 'Thai',
      lang: 'th',
    }
  },
  title: "IDIN Core",
  description: "IDIN Core Golang Library",
  lastUpdated: true,
  ignoreDeadLinks: true,
  themeConfig: {
    outline: 'deep',
    // https://vitepress.dev/reference/default-theme-config
    editLink: {
      pattern: 'https://gitlab.finema.co/finema/idin-core/-/edit/master/docs/:path',
      text: 'Edit this page on Gitlab'
    },
    search: {
      provider: 'local'
    },
    nav: [
      {text: 'Home', link: '/'},
      {text: 'Guide', link: '/get-started'},
      {text: 'Starter template', link: 'https://gitlab.finema.co/finema/golang-template'},
      {
        text: process?.env?.IDIN_CORE_VERSION || 'v1.4.xx',
        items: [
          {
            text: `Changelog`,
            link: 'https://gitlab.finema.co/finema/idin-core/-/blob/master/CHANGELOG.md'
          },
          {
            text: `Contributing`,
            link: 'https://gitlab.finema.co/finema/idin-core/-/blob/master/contributing.md'
          },
          {
            text: `godoc`,
            link: '/godoc/pkg/gitlab.finema.co/finema/idin-core/index.html',
            target: '_blank'
          }
        ]
      },

    ],

    sidebar: [
      {text: 'Get Started', link: '/get-started'},
      {text: 'Architecture Concepts', link: '/architecture'},
      {text: 'Conventional Commits', link: '/commits'},
      {
        text: 'The Basics', items: [
          {text: 'Routing', link: '/basics/routing'},
          {text: 'Middleware', link: '/basics/middleware'},
          {text: 'Controller', link: '/basics/controller'},
          {text: 'Request', link: '/basics/request'},
          {text: 'Response', link: '/basics/response'},
          {text: 'Views', link: '/basics/views'},
          {text: 'Service', link: '/basics/service'},
          {text: 'Validation', link: '/basics/validation'},
          {text: 'Error Handling', link: '/basics/error-handling'},
          {text: 'Logging', link: '/basics/logging'},
          {text: 'Context', link: '/basics/context'},
          {text: 'Environment (.env)', link: '/basics/env'},
          {text: 'Makefile', link: '/basics/makefile'},
        ]
      },
      {
        text: 'Digging Deeper', items: [
          {text: 'HTTP Client', link: '/deeper/http-client'},
          {text: 'Email', link: '/deeper/email'},
          {text: 'CSV', link: '/deeper/csv'},
          {text: 'File Storage (s3)', link: '/deeper/s3'},
          {text: 'Archiver (zip)', link: '/deeper/archiver'},
          {text: 'Cache (redis)', link: '/deeper/redis'},
          {text: 'Queues (rabbitmq)', link: '/deeper/queues'},
          {text: 'Task Scheduling (cronjob)', link: '/deeper/cronjob'},
          {text: 'Helpers', link: '/deeper/helpers'},
          {text: 'Mocking API', link: '/deeper/mocking-api'},
          {text: 'WinRM', link: '/deeper/winrm'},
          {text: 'Faker', link: '/deeper/faker'},
          {text: 'Firebase Cloud Messaging', link: '/deeper/fcm'},
        ]
      },
      {
        text: 'Security', collapsed: true,
        items: [
          {text: 'Authentication', link: '/security/authentication'},
          {text: 'Authorization', link: '/security/authorization'},
          {text: 'Hashing', link: '/security/hashing'},
        ]
      },

      {
        text: 'Database (Relational)', collapsed: true, items: [
          {text: 'Get Started', link: '/database/get-started'},
          {text: 'Pagination', link: '/database/pagination'},
          {text: 'Repository', link: '/database/repository'},
          {text: 'Migrations', link: '/database/migrations'},
          {text: 'Seeding', link: '/database/seeding'},
        ]
      },
      {
        text: 'Database (MongoDB)', collapsed: true, items: [
          {text: 'Get Started', link: '/mongodb/get-started'},
          {text: 'Pagination', link: '/mongodb/pagination'},
          {text: 'Repository', link: '/mongodb/repository'},
          {text: 'Migrations', link: '/mongodb/migrations'},
          {text: 'Seeding', link: '/mongodb/seeding'},
        ]
      },
      {
        text: 'Testing', collapsed: true, items: [
          {text: 'Get Started', link: '/testing/get-started'},
          {text: 'Unit Testing', link: '/testing/unit-testing'},
          {text: 'Integration Testing', link: '/testing/integration-testing'},
          {text: 'End-to-End Testing', link: '/testing/end-to-end-testing'},
          {text: 'Mocking', link: '/testing/mocking'},
        ]
      },
      {
        text: 'Deployment', collapsed: true, items: [
          {text: 'Get Started', link: '/deploy/get-started'},
          {text: 'Docker', link: '/deploy/docker'},
          {text: 'Kubernetes', link: '/deploy/kubernetes'},
          {text: 'Gitlab CI/CD', link: '/deploy/ci-cd'},
        ]
      },
    ],

    socialLinks: [
      {icon: 'github', link: 'https://gitlab.finema.co/finema/idin-core'}
    ],
    footer: {
      message: 'Maintained by Passakon Puttasuwan & Dev Core Team.'
    }
  }
})
