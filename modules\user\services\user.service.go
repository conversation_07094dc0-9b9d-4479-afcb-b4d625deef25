package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models" // Importing the models package
	"gitlab.finema.co/finema/finework/finework-api/modules/user/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/repositories" // Importing the repo package
	core "gitlab.finema.co/finema/idin-core"                                  // Importing the core package
	"gitlab.finema.co/finema/idin-core/repository"                            // Importing the repository package
	"gitlab.finema.co/finema/idin-core/utils"
)

type IUserService interface { // Defining the IUserService interface
	Create(input *dto.UserCreatePayload) (*models.User, core.IError)                                                                 // Method declaration for creating a user
	Update(id string, input *dto.UserUpdatePayload) (*models.User, core.IError)                                                      // Method declaration for updating a user
	UpdateAccessLevel(id string, input *dto.UserAccessLevelUpdatePayload) (*models.User, core.IError)                                // Method declaration for updating user access level
	Find(id string) (*models.User, core.IError)                                                                                      // Method declaration for finding a user
	Pagination(pageOptions *core.PageOptions, options *dto.UserPaginationOptions) (*repository.Pagination[models.User], core.IError) // Method declaration for pagination of users
	Delete(id string) core.IError                                                                                                    // Method declaration for deleting a user
}

type userService struct {
	ctx core.IContext // Struct for userService with a context field
}

func (s userService) Create(input *dto.UserCreatePayload) (*models.User, core.IError) {
	// Implementation for creating a user
	user := &models.User{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Email:               input.Email,
		FullName:            input.FullName,
		DisplayName:         input.DisplayName,
		Position:            input.Position,
		TeamCode:            utils.ToPointer(input.TeamCode),
		AvatarURL:           input.AvatarURL,
	}

	ierr := repositories.User(s.ctx).Create(user) // Calling the Create method of the repositories.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Create method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the created user
}

func (s userService) Update(id string, input *dto.UserUpdatePayload) (*models.User, core.IError) {
	// Implementation for updating a user
	user, ierr := s.Find(id) // Calling the Find method to retrieve the user to update
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	// Update fields if provided
	if input.FullName != "" {
		user.FullName = input.FullName
	}
	if input.DisplayName != "" {
		user.DisplayName = input.DisplayName
	}
	if input.Position != "" {
		user.Position = input.Position
	}
	if input.TeamCode != "" {
		user.TeamCode = utils.ToPointer(input.TeamCode)
	}
	if input.AvatarURL != "" {
		user.AvatarURL = input.AvatarURL
	}

	if input.JoinedDate != nil {
		joinedDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.JoinedDate))
		if !joinedDate.IsZero() {
			user.JoinedDate = utils.ToPointer(joinedDate)
		}
	}

	if input.IsActive != nil {
		user.IsActive = utils.ToNonPointer(input.IsActive)
		ierr = repositories.User(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{"is_active": input.IsActive}) // Calling the Updates method of the repositories.User repository
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Updates method fails
		}
	}

	ierr = repositories.User(s.ctx).Where("id = ?", id).Updates(user) // Calling the Updates method of the repositories.User repository
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Updates method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the updated user
}

func (s userService) UpdateAccessLevel(id string, input *dto.UserAccessLevelUpdatePayload) (*models.User, core.IError) {
	// Implementation for updating user access level
	user, ierr := s.Find(id) // Calling the Find method to retrieve the user to update
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	// Initialize access level if it doesn't exist
	if user.AccessLevel == nil {
		ierr = repository.New[models.UserAccessLevel](s.ctx).Create(&models.UserAccessLevel{
			UserID:    user.ID,
			Clockin:   models.UserPermissionLevelUser,
			Timesheet: models.UserPermissionLevelUser,
			Pmo:       models.UserPermissionLevelNone,
			Setting:   models.UserPermissionLevelNone,
		}) // Creating the access level
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Create method fails
		}

		return s.Find(user.ID) // Calling the Find method to retrieve the updated user
	}

	// Update access level fields if provided
	if input.Clockin != "" {
		user.AccessLevel.Clockin = models.UserPermissionLevel(input.Clockin)
	}
	if input.Timesheet != "" {
		user.AccessLevel.Timesheet = models.UserPermissionLevel(input.Timesheet)
	}
	if input.Pmo != "" {
		user.AccessLevel.Pmo = models.UserPermissionLevel(input.Pmo)
	}
	if input.Setting != "" {
		user.AccessLevel.Setting = models.UserPermissionLevel(input.Setting)
	}

	ierr = repository.New[models.UserAccessLevel](s.ctx).Where("user_id = ?", id).Updates(user.AccessLevel) // Using Save to handle both user and access level updates
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr) // Returning an error if the Save method fails
	}

	return s.Find(user.ID) // Calling the Find method to retrieve the updated user
}

func (s userService) Find(id string) (*models.User, core.IError) {
	// Implementation for finding a user
	return repositories.User(s.ctx, repositories.UserWithAllRelation()).FindOne("id = ?", id) // Calling the FindOne method of the repositories.User repository with all relations
}

func (s userService) Pagination(pageOptions *core.PageOptions, options *dto.UserPaginationOptions) (*repository.Pagination[models.User], core.IError) {
	// Implementation for user pagination
	repoOptions := []repository.Option[models.User]{
		repositories.UserWithTeam([]string{utils.ToNonPointer(options.TeamCode)}),
		repositories.UserWithActiveStatus(options.IsActive),
		repositories.UserWithSearch(pageOptions.Q),
		repositories.UserWithAllRelation(),
		repositories.UserOrderBy(pageOptions),
	}

	return repositories.User(s.ctx, repoOptions...).Pagination(pageOptions) // Calling the Pagination method of the repositories.User repository
}

func (s userService) Delete(id string) core.IError {
	// Implementation for deleting a user
	_, ierr := s.Find(id) // Calling the Find method to check if the user exists
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr) // Returning an error if the Find method fails
	}

	return repositories.User(s.ctx).Delete("id = ?", id) // Calling the Delete method of the repositories.User repository
}

func NewUserService(ctx core.IContext) IUserService {
	return &userService{ctx: ctx} // Creating a new instance of userService with the provided context
}
