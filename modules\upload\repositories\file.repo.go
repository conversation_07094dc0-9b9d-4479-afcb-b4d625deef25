package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var File = repository.Make[models.File]()

func FileOrderBy(pageOptions *core.PageOptions) repository.Option[models.File] {
	return func(c repository.IRepository[models.File]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
