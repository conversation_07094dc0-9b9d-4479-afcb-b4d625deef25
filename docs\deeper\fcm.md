
# Firebase Cloud Messaging

Firebase Cloud Messaging (FCM) services. It allows for sending notifications to individual devices, multiple devices, or topics. The service handles connection initialization and error handling with FCM.


## Setup
To include instructions for setting the `FIREBASE_CREDENTIAL` in a `.env` file in your documentation, you can add a section under "Setup" or "Configuration." Here's how you might integrate this into your existing documentation:

### Environment Configuration

Before using the FCM service, set up the necessary environment variables. Add the following line to your `.env` file:

```plaintext
FIREBASE_CREDENTIAL=your_firebase_credential_json
```

Replace `your_firebase_credential_json` with the actual JSON credential data provided by Firebase. This environment variable is essential for authenticating with the Firebase services.

### `IFMC`

Interface for FCM service.

- **Methods:**
  - `SendSimpleMessage(tokens []string, payload *IFMCMessage) IError`
  - `SendSimpleMessages(payload []IFMCPayload) IError`
  - `SendTopic(topic string, payload map[string]string) IError`

## Functions

### `NewFMC(ctx IContext) IFMC`

Creates a new instance of FCM service.

- **Parameters:**
  - `ctx`: Context with environmental configuration.

## Usage Examples

### Sending a Simple Message

```go
fmcService := core.NewFMC(context)
message := core.IFMCMessage{Title: "Hello", Body: "World", ImageURL: "http://example.com/image.png"}
err := fmcService.SendSimpleMessage([]string{"device_token"}, &message)
if err != nil {
   // Handle error
}
```

### Sending to a Topic

```go
payload := map[string]string{"key": "value"}
err := fmcService.SendTopic("news_topic", payload)
if err != nil {
// Handle error
}
```

## Error Handling

The package defines custom errors `FCMServiceConnectError` and `FCMServiceSendError` for specific failure cases.
