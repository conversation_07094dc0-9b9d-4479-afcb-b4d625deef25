# Essential Concepts of Project Architecture

This document provides a comprehensive overview of the foundational architecture concepts for a project developed in Go. To illustrate the project's organization and structure, the code snippets in this document have been included as examples.

The project structure is a reference to the codebase available at: https://gitlab.finema.co/finema/golang-template

Let's examine the following directory structure as an instance:

```
├── cmd
│   └── api.go                     # Main entry point for the API application
├── consts
│   └── env.const.go               # Constants for environment variables
├── emsgs
│   └── user.error.go              # Error messages for the user module
├── helpers
│   └── string.helper.go           # Helper functions for string manipulation
├── migrations
│   └── 20220912084753_users.ts    # Users table database migration file
├── models
│   ├── base.model.go              # Base model for shared fields in database models
├── modules
│   └── user
│       ├── user.controller.go     # Controller for user module operations
│       └── user.http.go           # HTTP routes and handlers for user module
├── repo
│   └── base.repo.go               # Base repository for common database operations
├── requests
│   ├── user_create.request.go     # Request structure for creating a new user
├── services
│   ├── user.dto.go                # Data transfer objects (DTOs) for user module
│   └── user.service.go            # Service for user module business logic
├── views
│   └── user.view.go               # View for rendering user-related data
├── .env                           # Configuration file for environment variables
├── main.go                        # Main application file
├── go.mod                         # Go module file
├── go.sum                         # Checksum file for Go module
```

## Package Descriptions

### `main` Package

The `main` package includes the primary function of the application and serves as the initiation point for the program.

### cmd Package

The `cmd` package holds the entry point of the application and initiates the API server.

#### APIRun()

This function is responsible for initializing the essential components, including the environment, database connection, and HTTP server. Furthermore, it establishes routes for different modules and activates the HTTP server.

### modules/user Package

This package is designated for handling user-centric functionalities, which include HTTP handlers and service implementations.

#### NewUserHTTP(e *echo.Echo)

This function sets up the user-focused routes on the provided `Echo` instance (`e`) and maps each route with the respective HTTP handler function outlined in the `UserController`.

#### UserController struct

This struct specifies methods that manage different user-focused HTTP requests:

- Pagination: Obtains paginated user data.
- Find: Locates a user by ID.
- Create: Generates a new user.
- Update: Modifies an existing user.
- Delete: Removes a user.

### services Package

This package houses the service implementations for various functionalities, which involve user-centric operations.

#### IUserService Interface

The `IUserService` interface outlines the blueprint for user service implementations, which includes methods for creating, updating, locating, paginating, and removing users.

#### userService Struct

The `userService` struct implements the `IUserService` interface, offering the concrete logic for user-focused operations such as user creation, updates, location, pagination, and deletion.

### repo Package

The `repo` package manages data persistence and retrieval, leveraging the gorm library.

## Code Execution Flow



1. The `main` function in the `main` package acts as the application's entry point.
2. This function triggers the `APIRun` function from the `cmd` package.
3. The `APIRun` function initializes the application environment and forms a connection with the database.
4. It constructs an HTTP server using the `core.NewHTTPServer` function, passing the necessary context options, including the database and environment.
5. The `APIRun` function registers HTTP routes for handling requests related to the home and user modules using the `home.NewHomeHTTP` and `user.NewUserHTTP` functions, respectively.
6. The `NewUserHTTP` function within the `user` package establishes the HTTP routes for user-related operations and associates them with the respective handlers from the `UserController`.
7. Each HTTP route in the `NewUserHTTP` function is linked with a specific handler method in the `UserController` (i.e., `Pagination`, `Find`, `Create`, `Update`, `Delete`).
8. When a user submits an HTTP request to one of the registered routes, the respective handler method in the `UserController` is executed.
9. Every handler method in the `UserController` delegates the request to the user service for more processing.
10. The user service methods (`Create`, `Update`, `Find`, `Pagination`, `Delete`) collaborate with the repository to perform CRUD operations on the user data in the database.
11. The repository methods utilize the database connection to execute the required SQL queries and return the outcomes to the user service.
12. The user service methods process the data retrieved from the repository and return the relevant response to the handler methods in the `UserController`.
13. The handler methods in the `UserController` format the response into JSON and send it to the user as an HTTP response.

This represents a high-level overview of the code execution flow within the provided codebase.

## Conclusion

This architecture follows a layered approach, segregating concerns into separate packages. The `cmd` package takes charge of the application's entry point, whereas the `user` package focuses on user-related functions. The `services` package provides the business logic for user operations, and the `repo` package handles data persistence and retrieval.

By adhering to this architecture, the codebase remains organized and modular, simplifying future maintenance and extension of the application.
