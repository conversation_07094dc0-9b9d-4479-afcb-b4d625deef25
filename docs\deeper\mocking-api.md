# Mocking API

The Mocking API allows you to create mock responses for testing and development purposes. It uses the [go-faker/faker: Go (Golang) Fake Data Generator for Struct, previously https://github.com/bxcodec/faker](https://github.com/go-faker/faker) package to generate realistic and randomized data.

## MockMiddlewareOptions
```go
type MockMiddlewareOptions struct {
  Wrapper      MockMiddlewareWrapper
  Manual       MockMiddlewareManual
  IsPagination bool
  IsDisabled   bool
}

```

The `MockMiddlewareOptions` struct contains the following fields:

- `Wrapper`: A `MockMiddlewareWrapper` function type that represents the wrapper function to be used for modifying the model.

- `Manual`: A `MockMiddlewareManual` function type (not shown in the provided code) that represents the manual mocking function to be implemented.

- `IsPagination`: A boolean indicating whether pagination is enabled for the mocking API.

- `IsDisabled`: A boolean indicating whether the mocking middleware is disabled. Setting the `ENV` environment variable to `mock` will always return mock data.

## Setting Up the Basic Mocking API

### Step 1: Define the MockItem Struct

Define the `MockItem` struct with additional faker tags based on the examples provided in the [faker](https://github.com/go-faker/faker) repository:

```go
type MockItem struct {
	ID                 string  `json:"id" faker:"uuid_hyphenated"`
	Name               string  `json:"name" faker:"name"`
	Email              string  `json:"email" faker:"email"`
	Latitude           float32 `json:"latitude" faker:"lat"`
	Longitude          float32 `json:"longitude" faker:"long"`
	CreditCardNumber   string  `json:"credit_card_number" faker:"cc_number"`
	CreditCardType     string  `json:"credit_card_type" faker:"cc_type"`
	DomainName         string  `json:"domain_name" faker:"domain_name"`
	IPV4               string  `json:"ipv4" faker:"ipv4"`
	IPV6               string  `json:"ipv6" faker:"ipv6"`
	Password           string  `json:"password" faker:"password"`
	Jwt                string  `json:"jwt" faker:"jwt"`
	PhoneNumber        string  `json:"phone_number" faker:"phone_number"`
	MacAddress         string  `json:"mac_address" faker:"mac_address"`
}
```

### Step 2: Set Up the Mocking API Endpoint

Define the `NewHomeHTTP` function to set up the mocking API endpoint using Echo framework:

```go
func NewHomeHTTP(e *echo.Echo) {
	e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
		return c.JSON(http.StatusOK, core.Map{
			"status": "i'm ok",
		})
	}), core.MockMiddleware(&MockItem{}, &core.MockMiddlewareOptions{}))
}
```

### Example Response
```json
{
  id: "1709a272-ab86-425f-b3ab-6a5c258b0451",
  name: "Prof. Oral Rogahn",
  email: "<EMAIL>",
  latitude: 63.298218,
  longitude: -148.2835,
  credit_card_number: "38989048719320",
  credit_card_type: "Diners Club",
  domain_name: "tkbhPNt.com",
  ipv4: "***************",
  ipv6: "46d2:b413:3307:d7dd:6166:a4ce:a8e:e06f",
  password: "vMcZNdmHrujjblDIAWTtexgoQDoYSJULqCqJivuhWCcbFFbLCq",
  jwt: "PsfUcGrcAGvkkqaINXoXEZIBhgfnwKWLGVcyNHAk.PsfUcGrcAGvkkqaINXoXEZIBhgfnwKWLGVcyNHAk.PsfUcGrcAGvkkqaINXoXEZIBhgfnwKWLGVcyNHAk",
  phone_number: "************",
  mac_address: "58:55:29:ad:ef:62"
}
  ```

## Mocking Array API

### Set Up the Mocking API Endpoint

Modify the `NewHomeHTTP` function to set up the mocking array API endpoint using the Echo framework:

```go
func NewHomeHTTP(e *echo.Echo) {
	e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
		return c.JSON(http.StatusOK, core.Map{
			"status": "i'm ok",
		})
	}), core.MockMiddleware(&[]MockItem{}, &core.MockMiddlewareOptions{}))
}
```

### Example response
```json
[
  {
    id: "730fe83c-ba6f-42a5-b3a2-d1e19182399a",
    name: "King Brannon Turcotte",
    email: "<EMAIL>",
    latitude: 69.5202,
    longitude: 159.99734,
    credit_card_number: "38729394631322",
    credit_card_type: "Diners Club",
    domain_name: "Jhfirsj.ru",
    ipv4: "**************",
    ipv6: "7d17:dda8:9f13:8fc0:b30d:7e28:7d4d:c881",
    password: "fqKaChiyyyHmEZpdJwutqqQLxLRBRqUoPpcgmsdYYPFjfwylXx",
    jwt: "hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS.hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS.hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS",
    phone_number: "************",
    mac_address: "ca:61:f1:25:2e:18"
  },
  {
    id: "b69fb0ad-63ac-4945-bbf4-c02018859c48",
    name: "King Stone Koepp",
    email: "<EMAIL>",
    latitude: -23.81511,
    longitude: 10.673737,
    credit_card_number: "38492306824560",
    credit_card_type: "Diners Club",
    domain_name: "qCEmQph.net",
    ipv4: "**************",
    ipv6: "e413:190e:3fb9:c986:a6e5:4cb8:7822:c924",
    password: "cXfRmoeLWOMOCwSXfhFBXgCiGjQhnqPOZowmndKIouShpVIXkW",
    jwt: "RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY.RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY.RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY",
    phone_number: "************",
    mac_address: "67:26:5a:f7:79:66"
  }
]
```

## Mocking Paginated API

### Set Up the Mocking API Endpoint

Modify the `NewHomeHTTP` function to set up the mocking paginated API endpoint using the Echo framework:

```go
func NewHomeHTTP(e *echo.Echo) {
	e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
		return c.JSON(http.StatusOK, core.Map{
			"status": "i'm ok",
		})
	}), core.MockMiddleware(&[]MockItem{}, &core.MockMiddlewareOptions{IsPagination: true}))
}
```

### Example response
```json
{
  page: 1,
  total: 230,
  limit: 30,
  count: 0,
  items: [
    {
      id: "730fe83c-ba6f-42a5-b3a2-d1e19182399a",
      name: "King Brannon Turcotte",
      email: "<EMAIL>",
      latitude: 69.5202,
      longitude: 159.99734,
      credit_card_number: "38729394631322",
      credit_card_type: "Diners Club",
      domain_name: "Jhfirsj.ru",
      ipv4: "**************",
      ipv6: "7d17:dda8:9f13:8fc0:b30d:7e28:7d4d:c881",
      password: "fqKaChiyyyHmEZpdJwutqqQLxLRBRqUoPpcgmsdYYPFjfwylXx",
      jwt: "hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS.hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS.hBlklUXTgdvCaeGVdsAenNGARlmHuSkcRPHFmluS",
      phone_number: "************",
      mac_address: "ca:61:f1:25:2e:18"
    },
    {
      id: "b69fb0ad-63ac-4945-bbf4-c02018859c48",
      name: "King Stone Koepp",
      email: "<EMAIL>",
      latitude: -23.81511,
      longitude: 10.673737,
      credit_card_number: "38492306824560",
      credit_card_type: "Diners Club",
      domain_name: "qCEmQph.net",
      ipv4: "**************",
      ipv6: "e413:190e:3fb9:c986:a6e5:4cb8:7822:c924",
      password: "cXfRmoeLWOMOCwSXfhFBXgCiGjQhnqPOZowmndKIouShpVIXkW",
      jwt: "RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY.RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY.RlHFIFabdRiCMkrdUBYnmmfnUNvnMxQHKfgIVMVY",
      phone_number: "************",
      mac_address: "67:26:5a:f7:79:66"
    }
  ]
}
```

## Manual Mocking API

```go
e.GET("/manual", core.WithHTTPContext(func(c core.IHTTPContext) error {
	// Return a JSON response with a status message
	return c.JSON(http.StatusOK, core.Map{
		"status": "i'm ok",
	})
}), core.MockMiddleware(nil, &core.MockMiddlewareOptions{
	Manual: func(c core.IHTTPContext) error {
		// Implement your manual mocking logic here
		// You have access to the HTTP context (c) to handle the request and response

		// Example: Manually return a JSON response with a status message
		return c.JSON(http.StatusOK, core.Map{
			"status": "Manual mock response",
		})
	},
})) // Apply the mock middleware to the endpoint
```

## Conclusion

You have successfully set up a mocking API using the provided code. This API allows you to mock 
responses for different scenarios, such as basic responses, array responses, paginated responses, 
and manual responses. Feel free to customize and extend the functionality according to your requirements.
