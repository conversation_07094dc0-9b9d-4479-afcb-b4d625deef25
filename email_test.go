//go:build e2e
// +build e2e

package core

import (
	"gitlab.finema.co/finema/idin-core/utils"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type EmailTestSuite struct {
	suite.Suite
	ctx IContext
	s   IEmail
}

func TestEmailTestSuite(t *testing.T) {
	suite.Run(t, new(EmailTestSuite))
}

func (md *EmailTestSuite) BeforeTest(_, _ string) {
	env := NewENVPath(utils.RootDir())

	md.ctx = NewContext(&ContextOptions{
		ENV: env,
	})
	md.s = NewEmail(md.ctx)
}

func (md *EmailTestSuite) TestSendHTMLExpectSuccess() {
	body, err := md.s.ParseHTMLToString(utils.RootDir()+"/email-templates/change-password.html", map[string]string{
		"action_url": "https://google.com",
	})
	assert.NoError(md.T(), err)

	err = md.s.SendHTML(md.ctx.ENV().Config().EmailSender, []string{"<EMAIL>"}, "เปลี่ยนรหัสผ่านสำหรับบัญชี workD", body)
	assert.NoError(md.T(), err)
}

func (md *EmailTestSuite) TestSendHTMLExpectSuccess2() {
	testmap := []map[string]interface{}{}
	testmap = append(testmap, map[string]interface{}{
		"rowNo": 1, "errorMessage": "test", "payload": "testtt",
	})

	body, err := md.s.ParseHTMLToString(utils.RootDir()+"/email-templates/user-import.html", map[string]interface{}{
		"domainName":  "domainName",
		"totalRows":   10,
		"startTime":   "startTime",
		"endTime":     "endTime",
		"successRows": 2,
		"errorRows":   2,
		"err":         testmap,
	})
	assert.NoError(md.T(), err)

	err = md.s.SendHTML(md.ctx.ENV().Config().EmailSender, []string{"<EMAIL>"}, "เปลี่ยนรหัสผ่านสำหรับบัญชี workD", body)
	assert.NoError(md.T(), err)
}
