# IDIN-Core Contributing Guide

Hi! We're really excited that you are interested in contributing to IDIN-Core. Before submitting your contribution, please make sure to take a moment and read through the following guidelines:

- [Pull Request Guidelines](#pull-request-guidelines)

## Pull Request Guidelines

- Checkout a topic branch from the relevant branch, e.g. `main`, and merge back against that branch.

- If adding a new feature:

  - Provide a convincing reason to add this feature. Ideally, you should open a suggestion issue first and have it approved before working on it.

- If fixing bug:

  - Provide a detailed description of the bug in the PR. Live demo preferred.

- Commit messages must follow the https://www.conventionalcommits.org/.

## Development Setup

After cloning the repo, run:

```sh
# install the dependencies of the project
$ make install
```
### Run Tests

#### Unit tests
```sh
$ make test
```

#### Integration tests
```sh
$ make test-integration
```

#### E2E tests
```sh
$ make test-e2e
```





## Setup for Docs Dev Environment

```sh
# install the dependencies of the project
$ yarn install
```


The easiest way to start testing out IDIN-Core is to tweak the IDIN-Core docs. You may run `yarn docs:dev` to boot up VitePress documentation site locally, with live reloading of the source code.

```sh
$ yarn docs:dev
```

After executing the above command, visit http://localhost:5173 and try modifying the source code. You'll get live update.
