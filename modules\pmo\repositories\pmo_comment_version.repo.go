package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Comment Version Repository
var PMOCommentVersion = repository.Make[models.PMOCommentVersion]()

func PMOCommentVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentVersionByCommentID(commentID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if commentID != "" {
			c.Where("comment_id = ?", commentID)
		}
	}
}

func PMOCommentVersionByProjectID(projectID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCommentVersionWithRelations() repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		c.Preload("User")
	}
}
