package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOContractCreate struct {
	core.BaseValidator
	ContractNo           *string  `json:"contract_no"`
	Value                *float64 `json:"value"`
	SigningDate          *string  `json:"signing_date"`
	StartDate            *string  `json:"start_date"`
	EndDate              *string  `json:"end_date"`
	DurationDay          *int64   `json:"duration_day"`
	WarrantyDurationDay  *int64   `json:"warranty_duration_day"`
	WarrantyDurationYear *int64   `json:"warranty_duration_year"`
	Prime                *string  `json:"prime"`
	PenaltyFee           *float64 `json:"penalty_fee"`
	IsLegalizeStamp      *bool    `json:"is_legalize_stamp"`
}

func (r *PMOContractCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.ContractNo, "contract_no"))
	r.Must(r.<PERSON>equired(r.Value, "value"))
	r.Must(r.IsDate(r.SigningDate, "signing_date"))
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))
	r.Must(r.IsRequired(r.DurationDay, "duration_day"))
	r.Must(r.IsRequired(r.WarrantyDurationDay, "warranty_duration_day"))
	r.Must(r.IsRequired(r.WarrantyDurationYear, "warranty_duration_year"))
	r.Must(r.IsStrRequired(r.Prime, "prime"))
	r.Must(r.IsRequired(r.PenaltyFee, "penalty_fee"))
	r.Must(r.IsRequired(r.IsLegalizeStamp, "is_legalize_stamp"))

	return r.Error()
}
