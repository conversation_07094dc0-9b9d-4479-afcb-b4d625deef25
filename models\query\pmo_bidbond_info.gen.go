// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOBidbondInfo(db *gorm.DB, opts ...gen.DOOption) pMOBidbondInfo {
	_pMOBidbondInfo := pMOBidbondInfo{}

	_pMOBidbondInfo.pMOBidbondInfoDo.UseDB(db, opts...)
	_pMOBidbondInfo.pMOBidbondInfoDo.UseModel(&models.PMOBidbondInfo{})

	tableName := _pMOBidbondInfo.pMOBidbondInfoDo.TableName()
	_pMOBidbondInfo.ALL = field.NewAsterisk(tableName)
	_pMOBidbondInfo.ID = field.NewString(tableName, "id")
	_pMOBidbondInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOBidbondInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOBidbondInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOBidbondInfo.ProjectID = field.NewString(tableName, "project_id")
	_pMOBidbondInfo.GuaranteeAsset = field.NewString(tableName, "guarantee_asset")
	_pMOBidbondInfo.BidbondPayer = field.NewString(tableName, "bidbond_payer")
	_pMOBidbondInfo.BidbondValue = field.NewFloat64(tableName, "bidbond_value")
	_pMOBidbondInfo.StartDate = field.NewTime(tableName, "start_date")
	_pMOBidbondInfo.EndDate = field.NewTime(tableName, "end_date")
	_pMOBidbondInfo.DurationMonth = field.NewInt64(tableName, "duration_month")
	_pMOBidbondInfo.DurationYear = field.NewInt64(tableName, "duration_year")
	_pMOBidbondInfo.Fee = field.NewFloat64(tableName, "fee")
	_pMOBidbondInfo.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOBidbondInfo.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOBidbondInfo.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOBidbondInfo.Project = pMOBidbondInfoHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOBidbondInfo.CreatedBy = pMOBidbondInfoBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOBidbondInfo.UpdatedBy = pMOBidbondInfoBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOBidbondInfo.fillFieldMap()

	return _pMOBidbondInfo
}

type pMOBidbondInfo struct {
	pMOBidbondInfoDo

	ALL            field.Asterisk
	ID             field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	ProjectID      field.String
	GuaranteeAsset field.String
	BidbondPayer   field.String
	BidbondValue   field.Float64
	StartDate      field.Time
	EndDate        field.Time
	DurationMonth  field.Int64
	DurationYear   field.Int64
	Fee            field.Float64
	CreatedByID    field.String
	UpdatedByID    field.String
	DeletedByID    field.String
	Project        pMOBidbondInfoHasOneProject

	CreatedBy pMOBidbondInfoBelongsToCreatedBy

	UpdatedBy pMOBidbondInfoBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOBidbondInfo) Table(newTableName string) *pMOBidbondInfo {
	p.pMOBidbondInfoDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOBidbondInfo) As(alias string) *pMOBidbondInfo {
	p.pMOBidbondInfoDo.DO = *(p.pMOBidbondInfoDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOBidbondInfo) updateTableName(table string) *pMOBidbondInfo {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.GuaranteeAsset = field.NewString(table, "guarantee_asset")
	p.BidbondPayer = field.NewString(table, "bidbond_payer")
	p.BidbondValue = field.NewFloat64(table, "bidbond_value")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.DurationMonth = field.NewInt64(table, "duration_month")
	p.DurationYear = field.NewInt64(table, "duration_year")
	p.Fee = field.NewFloat64(table, "fee")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOBidbondInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOBidbondInfo) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 19)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["guarantee_asset"] = p.GuaranteeAsset
	p.fieldMap["bidbond_payer"] = p.BidbondPayer
	p.fieldMap["bidbond_value"] = p.BidbondValue
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["duration_month"] = p.DurationMonth
	p.fieldMap["duration_year"] = p.DurationYear
	p.fieldMap["fee"] = p.Fee
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOBidbondInfo) clone(db *gorm.DB) pMOBidbondInfo {
	p.pMOBidbondInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOBidbondInfo) replaceDB(db *gorm.DB) pMOBidbondInfo {
	p.pMOBidbondInfoDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOBidbondInfoHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOBidbondInfoHasOneProject) Where(conds ...field.Expr) *pMOBidbondInfoHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoHasOneProject) WithContext(ctx context.Context) *pMOBidbondInfoHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoHasOneProject) Session(session *gorm.Session) *pMOBidbondInfoHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoHasOneProject) Model(m *models.PMOBidbondInfo) *pMOBidbondInfoHasOneProjectTx {
	return &pMOBidbondInfoHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoHasOneProject) Unscoped() *pMOBidbondInfoHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoHasOneProjectTx) Unscoped() *pMOBidbondInfoHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBidbondInfoBelongsToCreatedBy) Where(conds ...field.Expr) *pMOBidbondInfoBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoBelongsToCreatedBy) WithContext(ctx context.Context) *pMOBidbondInfoBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoBelongsToCreatedBy) Session(session *gorm.Session) *pMOBidbondInfoBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoBelongsToCreatedBy) Model(m *models.PMOBidbondInfo) *pMOBidbondInfoBelongsToCreatedByTx {
	return &pMOBidbondInfoBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoBelongsToCreatedBy) Unscoped() *pMOBidbondInfoBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoBelongsToCreatedByTx) Unscoped() *pMOBidbondInfoBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOBidbondInfoBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOBidbondInfoBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOBidbondInfoBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOBidbondInfoBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOBidbondInfoBelongsToUpdatedBy) Session(session *gorm.Session) *pMOBidbondInfoBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOBidbondInfoBelongsToUpdatedBy) Model(m *models.PMOBidbondInfo) *pMOBidbondInfoBelongsToUpdatedByTx {
	return &pMOBidbondInfoBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOBidbondInfoBelongsToUpdatedBy) Unscoped() *pMOBidbondInfoBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOBidbondInfoBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOBidbondInfoBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOBidbondInfoBelongsToUpdatedByTx) Unscoped() *pMOBidbondInfoBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOBidbondInfoDo struct{ gen.DO }

type IPMOBidbondInfoDo interface {
	gen.SubQuery
	Debug() IPMOBidbondInfoDo
	WithContext(ctx context.Context) IPMOBidbondInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOBidbondInfoDo
	WriteDB() IPMOBidbondInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOBidbondInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOBidbondInfoDo
	Not(conds ...gen.Condition) IPMOBidbondInfoDo
	Or(conds ...gen.Condition) IPMOBidbondInfoDo
	Select(conds ...field.Expr) IPMOBidbondInfoDo
	Where(conds ...gen.Condition) IPMOBidbondInfoDo
	Order(conds ...field.Expr) IPMOBidbondInfoDo
	Distinct(cols ...field.Expr) IPMOBidbondInfoDo
	Omit(cols ...field.Expr) IPMOBidbondInfoDo
	Join(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo
	Group(cols ...field.Expr) IPMOBidbondInfoDo
	Having(conds ...gen.Condition) IPMOBidbondInfoDo
	Limit(limit int) IPMOBidbondInfoDo
	Offset(offset int) IPMOBidbondInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBidbondInfoDo
	Unscoped() IPMOBidbondInfoDo
	Create(values ...*models.PMOBidbondInfo) error
	CreateInBatches(values []*models.PMOBidbondInfo, batchSize int) error
	Save(values ...*models.PMOBidbondInfo) error
	First() (*models.PMOBidbondInfo, error)
	Take() (*models.PMOBidbondInfo, error)
	Last() (*models.PMOBidbondInfo, error)
	Find() ([]*models.PMOBidbondInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBidbondInfo, err error)
	FindInBatches(result *[]*models.PMOBidbondInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOBidbondInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOBidbondInfoDo
	Assign(attrs ...field.AssignExpr) IPMOBidbondInfoDo
	Joins(fields ...field.RelationField) IPMOBidbondInfoDo
	Preload(fields ...field.RelationField) IPMOBidbondInfoDo
	FirstOrInit() (*models.PMOBidbondInfo, error)
	FirstOrCreate() (*models.PMOBidbondInfo, error)
	FindByPage(offset int, limit int) (result []*models.PMOBidbondInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOBidbondInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOBidbondInfoDo) Debug() IPMOBidbondInfoDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOBidbondInfoDo) WithContext(ctx context.Context) IPMOBidbondInfoDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOBidbondInfoDo) ReadDB() IPMOBidbondInfoDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOBidbondInfoDo) WriteDB() IPMOBidbondInfoDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOBidbondInfoDo) Session(config *gorm.Session) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOBidbondInfoDo) Clauses(conds ...clause.Expression) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOBidbondInfoDo) Returning(value interface{}, columns ...string) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOBidbondInfoDo) Not(conds ...gen.Condition) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOBidbondInfoDo) Or(conds ...gen.Condition) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOBidbondInfoDo) Select(conds ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOBidbondInfoDo) Where(conds ...gen.Condition) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOBidbondInfoDo) Order(conds ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOBidbondInfoDo) Distinct(cols ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOBidbondInfoDo) Omit(cols ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOBidbondInfoDo) Join(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOBidbondInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOBidbondInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOBidbondInfoDo) Group(cols ...field.Expr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOBidbondInfoDo) Having(conds ...gen.Condition) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOBidbondInfoDo) Limit(limit int) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOBidbondInfoDo) Offset(offset int) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOBidbondInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOBidbondInfoDo) Unscoped() IPMOBidbondInfoDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOBidbondInfoDo) Create(values ...*models.PMOBidbondInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOBidbondInfoDo) CreateInBatches(values []*models.PMOBidbondInfo, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOBidbondInfoDo) Save(values ...*models.PMOBidbondInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOBidbondInfoDo) First() (*models.PMOBidbondInfo, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfo), nil
	}
}

func (p pMOBidbondInfoDo) Take() (*models.PMOBidbondInfo, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfo), nil
	}
}

func (p pMOBidbondInfoDo) Last() (*models.PMOBidbondInfo, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfo), nil
	}
}

func (p pMOBidbondInfoDo) Find() ([]*models.PMOBidbondInfo, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOBidbondInfo), err
}

func (p pMOBidbondInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOBidbondInfo, err error) {
	buf := make([]*models.PMOBidbondInfo, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOBidbondInfoDo) FindInBatches(result *[]*models.PMOBidbondInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOBidbondInfoDo) Attrs(attrs ...field.AssignExpr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOBidbondInfoDo) Assign(attrs ...field.AssignExpr) IPMOBidbondInfoDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOBidbondInfoDo) Joins(fields ...field.RelationField) IPMOBidbondInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOBidbondInfoDo) Preload(fields ...field.RelationField) IPMOBidbondInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOBidbondInfoDo) FirstOrInit() (*models.PMOBidbondInfo, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfo), nil
	}
}

func (p pMOBidbondInfoDo) FirstOrCreate() (*models.PMOBidbondInfo, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOBidbondInfo), nil
	}
}

func (p pMOBidbondInfoDo) FindByPage(offset int, limit int) (result []*models.PMOBidbondInfo, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOBidbondInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOBidbondInfoDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOBidbondInfoDo) Delete(models ...*models.PMOBidbondInfo) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOBidbondInfoDo) withDO(do gen.Dao) *pMOBidbondInfoDo {
	p.DO = *do.(*gen.DO)
	return p
}
