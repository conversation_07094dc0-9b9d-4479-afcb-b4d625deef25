package holiday

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewHolidayHTTP(e *echo.Echo) {
	holiday := &HolidayController{}
	e.GET("/holidays", core.WithHTTPContext(holiday.Pagination), middleware.AuthMiddleware())
	e.GET("/holidays/:id", core.WithHTTPContext(holiday.Find), middleware.AuthMiddleware())
	e.POST("/holidays", core.WithHTTPContext(holiday.Create), middleware.AuthMiddleware())
	e.PUT("/holidays/:id", core.WithHTTPContext(holiday.Update), middleware.AuthMiddleware())
	e.DELETE("/holidays/:id", core.WithHTTPContext(holiday.Delete), middleware.AuthMiddleware())
}
