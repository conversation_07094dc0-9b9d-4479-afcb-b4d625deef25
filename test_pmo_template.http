# PMO Template API Test File
# Use this file with REST Client extensions in VS Code or similar tools

### Variables
@baseUrl = http://localhost:8080
@authToken = your-jwt-token-here

### Create PMO Template Document
POST {{baseUrl}}/pmo-template/documents
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "tab_key": "INFO",
  "name": "Test Document",
  "sharepoint_url": "https://sharepoint.example.com/test-document"
}

### Get PMO Template Documents (Pagination)
GET {{baseUrl}}/pmo-template/documents?page=1&limit=10
Authorization: Bearer {{authToken}}

### Get PMO Template Documents with Tab Key Filter
GET {{baseUrl}}/pmo-template/documents?tab_key=INFO
Authorization: Bearer {{authToken}}

### Get PMO Template Documents with Search
GET {{baseUrl}}/pmo-template/documents?q=test
Authorization: Bearer {{authToken}}

### Get PMO Template Document by ID
GET {{baseUrl}}/pmo-template/documents/{{documentId}}
Authorization: Bearer {{authToken}}

### Update PMO Template Document
PUT {{baseUrl}}/pmo-template/documents/{{documentId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Updated Test Document",
  "sharepoint_url": "https://sharepoint.example.com/updated-test-document"
}

### Delete PMO Template Document
DELETE {{baseUrl}}/pmo-template/documents/{{documentId}}
Authorization: Bearer {{authToken}}

### Create PMO Template Checklist Item
POST {{baseUrl}}/pmo-template/checklist-items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "tab_key": "INFO",
  "detail": "Test checklist item detail"
}

### Get PMO Template Checklist Items (Pagination)
GET {{baseUrl}}/pmo-template/checklist-items?page=1&limit=10
Authorization: Bearer {{authToken}}

### Get PMO Template Checklist Items with Tab Key Filter
GET {{baseUrl}}/pmo-template/checklist-items?tab_key=INFO
Authorization: Bearer {{authToken}}

### Get PMO Template Checklist Items with Search
GET {{baseUrl}}/pmo-template/checklist-items?q=test
Authorization: Bearer {{authToken}}

### Get PMO Template Checklist Item by ID
GET {{baseUrl}}/pmo-template/checklist-items/{{checklistItemId}}
Authorization: Bearer {{authToken}}

### Update PMO Template Checklist Item
PUT {{baseUrl}}/pmo-template/checklist-items/{{checklistItemId}}
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "detail": "Updated test checklist item detail"
}

### Delete PMO Template Checklist Item
DELETE {{baseUrl}}/pmo-template/checklist-items/{{checklistItemId}}
Authorization: Bearer {{authToken}}
