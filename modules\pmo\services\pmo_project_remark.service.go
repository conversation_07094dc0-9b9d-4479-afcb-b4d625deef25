package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm/clause"
)

type IPMOProjectRemarkService interface {
	Create(input *dto.PMORemarkCreatePayload) (*models.PMORemark, core.IError)
	Update(projectID string, tabKey models.PMOTabKey, input *dto.PMORemarkUpdatePayload) (*models.PMORemark, core.IError)
	Find(projectID string, tabKey models.PMOTabKey) (*models.PMORemark, core.IError)

	// PMO Remark Version methods
	VersionsPagination(projectID string, tabKey models.PMOTabKey, pageOptions *core.PageOptions) (*repository.Pagination[models.PMORemarkVersion], core.IError)
}

type pmoProjectRemarkService struct {
	ctx core.IContext
}

// PMO Remark methods implementation
func (s pmoProjectRemarkService) Create(input *dto.PMORemarkCreatePayload) (*models.PMORemark, core.IError) {
	// Check if remark already exists for this project and tab key
	existing, ierr := s.Find(input.ProjectID, input.TabKey)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead
		return s.Update(input.ProjectID, input.TabKey, &dto.PMORemarkUpdatePayload{
			Detail: input.Detail,
		})
	}

	remark := &models.PMORemark{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		TabKey:      input.TabKey,
		Detail:      input.Detail,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMORemark(s.ctx).Create(remark)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	version := &models.PMORemarkVersion{
		PMORemark:  *remark,
		OriginalID: remark.ID,
	}

	ierr = repositories.PMORemarkVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(remark.ProjectID, remark.TabKey)
}

func (s pmoProjectRemarkService) Update(projectID string, tabKey models.PMOTabKey, input *dto.PMORemarkUpdatePayload) (*models.PMORemark, core.IError) {
	remark, ierr := s.Find(projectID, tabKey)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	updateMap := map[string]interface{}{
		"updated_by_id": s.ctx.GetUser().ID,
	}
	if input.Detail != "" {
		updateMap["detail"] = input.Detail
		remark.Detail = input.Detail
	}
	remark.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repositories.PMORemark(s.ctx).Where("project_id = ? AND tab_key = ?", projectID, tabKey).Updates(updateMap)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	version := &models.PMORemarkVersion{
		PMORemark:  *remark,
		OriginalID: remark.ID,
	}

	ierr = repositories.PMORemarkVersion(s.ctx).Omit(clause.Associations).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(remark.ProjectID, remark.TabKey)
}

func (s pmoProjectRemarkService) Find(projectID string, tabKey models.PMOTabKey) (*models.PMORemark, core.IError) {
	return repositories.PMORemark(s.ctx,
		repositories.PMORemarkWithRelations(),
	).FindOne("project_id = ? AND tab_key = ?", projectID, tabKey)
}

func (s pmoProjectRemarkService) VersionsPagination(projectID string, tabKey models.PMOTabKey, pageOptions *core.PageOptions) (*repository.Pagination[models.PMORemarkVersion], core.IError) {
	return repositories.PMORemarkVersion(s.ctx,
		repositories.PMORemarkVersionOrderBy(pageOptions),
		repositories.PMORemarkVersionByProjectIDAndTabKey(projectID, tabKey),
		repositories.PMORemarkVersionWithRelations(),
	).Pagination(pageOptions)
}

func NewPMOProjectRemarkService(ctx core.IContext) IPMOProjectRemarkService {
	return &pmoProjectRemarkService{ctx: ctx}
}
