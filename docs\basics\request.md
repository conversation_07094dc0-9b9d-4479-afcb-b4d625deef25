# Request

the configuration of routes for user-related operations using the Echo
web framework. The router is responsible for mapping incoming HTTP requests to their corresponding
handler functions, allowing the application to process and respond to different types of requests.

Form Data[](https://echo.labstack.com/guide/request/#form-data)
---------

Form data can be retrieved by name using`Context#FormValue(name string)`.

```go
// controller
func (m UserController) Create(c core.IHTTPContext) error {
  name := c.FormValue("name")
  return c.String(http.StatusOK, name)
}

```

```bash
curl -X POST http://localhost:3000 -d 'name=Joe'

```

Query Parameters[](https://echo.labstack.com/guide/request/#query-parameters)
---------

Query parameters can be retrieved by name using`Context#QueryParam(name string)`.

```go
// controller
func (m UserController) Create(c core.IHTTPContext) error {
  name := c.QueryParam("name")
  return c.String(http.StatusOK, name)
})

```

```bash
curl\
  -X GET\
  http://localhost:3000\?name\=Joe

```

Similar to form data, custom data type can be bind using`Context#QueryParam(name string)`.

Path Parameters[](https://echo.labstack.com/guide/request/#path-parameters)
---------

Registered path parameters can be retrieved by name using`Context#Param(name string) string`.

```go
e.GET("/users/:name", core.WithHTTPContext(func(c core.IHTTPContext) error {
  name := c.Param("name")
  return c.String(http.StatusOK, name)
}))

```

```bash
curl http://localhost:3000/users/Joe

```

### Binding Data[](https://echo.labstack.com/guide/request/#binding-data)

Also binding of request data to native Go structs and variables is supported.
See [Binding Data](https://echo.labstack.com/guide/binding/)
