package handlers

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewPMOTemplateHTTP(e *echo.Echo) {
	pmoTemplate := &PMOTemplateController{}
	
	// PMO Template Documents routes
	e.GET("/pmo/template/documents", core.WithHTTPContext(pmoTemplate.DocumentsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/template/documents/:id", core.WithHTTPContext(pmoTemplate.DocumentsFind), middleware.AuthMiddleware())
	e.POST("/pmo/template/documents", core.WithHTTPContext(pmoTemplate.DocumentsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/template/documents/:id", core.WithHTTPContext(pmoTemplate.DocumentsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/template/documents/:id", core.WithHTTPContext(pmoTemplate.DocumentsDelete), middleware.AuthMiddleware())
	
	// PMO Template Checklist Items routes
	e.GET("/pmo/template/checklist-items", core.WithHTTPContext(pmoTemplate.ChecklistItemsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/template/checklist-items/:id", core.WithHTTPContext(pmoTemplate.ChecklistItemsFind), middleware.AuthMiddleware())
	e.POST("/pmo/template/checklist-items", core.WithHTTPContext(pmoTemplate.ChecklistItemsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/template/checklist-items/:id", core.WithHTTPContext(pmoTemplate.ChecklistItemsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/template/checklist-items/:id", core.WithHTTPContext(pmoTemplate.ChecklistItemsDelete), middleware.AuthMiddleware())
}
