// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newTimesheet(db *gorm.DB, opts ...gen.DOOption) timesheet {
	_timesheet := timesheet{}

	_timesheet.timesheetDo.UseDB(db, opts...)
	_timesheet.timesheetDo.UseModel(&models.Timesheet{})

	tableName := _timesheet.timesheetDo.TableName()
	_timesheet.ALL = field.NewAsterisk(tableName)
	_timesheet.ID = field.NewString(tableName, "id")
	_timesheet.CreatedAt = field.NewTime(tableName, "created_at")
	_timesheet.UpdatedAt = field.NewTime(tableName, "updated_at")
	_timesheet.ProjectID = field.NewString(tableName, "project_id")
	_timesheet.ProjectName = field.NewString(tableName, "project_name")
	_timesheet.ProjectCode = field.NewString(tableName, "project_code")
	_timesheet.SgaID = field.NewString(tableName, "sga_id")
	_timesheet.SgaName = field.NewString(tableName, "sga_name")
	_timesheet.UserID = field.NewString(tableName, "user_id")
	_timesheet.Timing = field.NewFloat64(tableName, "timing")
	_timesheet.Type = field.NewString(tableName, "type")
	_timesheet.LeaveType = field.NewString(tableName, "leave_type")
	_timesheet.Description = field.NewString(tableName, "description")
	_timesheet.Date = field.NewString(tableName, "date")
	_timesheet.User = timesheetBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "models.User"),
		Team: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Team", "models.Team"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Team.Users", "models.User"),
			},
		},
		AccessLevel: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.AccessLevel", "models.UserAccessLevel"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.AccessLevel.User", "models.User"),
			},
		},
		Timesheets: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Timesheets", "models.Timesheet"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.User", "models.User"),
			},
			Sga: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Sga", "models.Sga"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Timesheets.Project", "models.Project"),
			},
		},
		Checkins: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Checkins", "models.Checkin"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Checkins.User", "models.User"),
			},
		},
	}

	_timesheet.Sga = timesheetBelongsToSga{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Sga", "models.Sga"),
	}

	_timesheet.Project = timesheetBelongsToProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.Project"),
	}

	_timesheet.fillFieldMap()

	return _timesheet
}

type timesheet struct {
	timesheetDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	ProjectID   field.String
	ProjectName field.String
	ProjectCode field.String
	SgaID       field.String
	SgaName     field.String
	UserID      field.String
	Timing      field.Float64
	Type        field.String
	LeaveType   field.String
	Description field.String
	Date        field.String
	User        timesheetBelongsToUser

	Sga timesheetBelongsToSga

	Project timesheetBelongsToProject

	fieldMap map[string]field.Expr
}

func (t timesheet) Table(newTableName string) *timesheet {
	t.timesheetDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t timesheet) As(alias string) *timesheet {
	t.timesheetDo.DO = *(t.timesheetDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *timesheet) updateTableName(table string) *timesheet {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.ProjectID = field.NewString(table, "project_id")
	t.ProjectName = field.NewString(table, "project_name")
	t.ProjectCode = field.NewString(table, "project_code")
	t.SgaID = field.NewString(table, "sga_id")
	t.SgaName = field.NewString(table, "sga_name")
	t.UserID = field.NewString(table, "user_id")
	t.Timing = field.NewFloat64(table, "timing")
	t.Type = field.NewString(table, "type")
	t.LeaveType = field.NewString(table, "leave_type")
	t.Description = field.NewString(table, "description")
	t.Date = field.NewString(table, "date")

	t.fillFieldMap()

	return t
}

func (t *timesheet) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *timesheet) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 17)
	t.fieldMap["id"] = t.ID
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["project_id"] = t.ProjectID
	t.fieldMap["project_name"] = t.ProjectName
	t.fieldMap["project_code"] = t.ProjectCode
	t.fieldMap["sga_id"] = t.SgaID
	t.fieldMap["sga_name"] = t.SgaName
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["timing"] = t.Timing
	t.fieldMap["type"] = t.Type
	t.fieldMap["leave_type"] = t.LeaveType
	t.fieldMap["description"] = t.Description
	t.fieldMap["date"] = t.Date

}

func (t timesheet) clone(db *gorm.DB) timesheet {
	t.timesheetDo.ReplaceConnPool(db.Statement.ConnPool)
	t.User.db = db.Session(&gorm.Session{Initialized: true})
	t.User.db.Statement.ConnPool = db.Statement.ConnPool
	t.Sga.db = db.Session(&gorm.Session{Initialized: true})
	t.Sga.db.Statement.ConnPool = db.Statement.ConnPool
	t.Project.db = db.Session(&gorm.Session{Initialized: true})
	t.Project.db.Statement.ConnPool = db.Statement.ConnPool
	return t
}

func (t timesheet) replaceDB(db *gorm.DB) timesheet {
	t.timesheetDo.ReplaceDB(db)
	t.User.db = db.Session(&gorm.Session{})
	t.Sga.db = db.Session(&gorm.Session{})
	t.Project.db = db.Session(&gorm.Session{})
	return t
}

type timesheetBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Team struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
	AccessLevel struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	Timesheets struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Sga struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Checkins struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a timesheetBelongsToUser) Where(conds ...field.Expr) *timesheetBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a timesheetBelongsToUser) WithContext(ctx context.Context) *timesheetBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a timesheetBelongsToUser) Session(session *gorm.Session) *timesheetBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a timesheetBelongsToUser) Model(m *models.Timesheet) *timesheetBelongsToUserTx {
	return &timesheetBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a timesheetBelongsToUser) Unscoped() *timesheetBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type timesheetBelongsToUserTx struct{ tx *gorm.Association }

func (a timesheetBelongsToUserTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a timesheetBelongsToUserTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a timesheetBelongsToUserTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a timesheetBelongsToUserTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a timesheetBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a timesheetBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a timesheetBelongsToUserTx) Unscoped() *timesheetBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type timesheetBelongsToSga struct {
	db *gorm.DB

	field.RelationField
}

func (a timesheetBelongsToSga) Where(conds ...field.Expr) *timesheetBelongsToSga {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a timesheetBelongsToSga) WithContext(ctx context.Context) *timesheetBelongsToSga {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a timesheetBelongsToSga) Session(session *gorm.Session) *timesheetBelongsToSga {
	a.db = a.db.Session(session)
	return &a
}

func (a timesheetBelongsToSga) Model(m *models.Timesheet) *timesheetBelongsToSgaTx {
	return &timesheetBelongsToSgaTx{a.db.Model(m).Association(a.Name())}
}

func (a timesheetBelongsToSga) Unscoped() *timesheetBelongsToSga {
	a.db = a.db.Unscoped()
	return &a
}

type timesheetBelongsToSgaTx struct{ tx *gorm.Association }

func (a timesheetBelongsToSgaTx) Find() (result *models.Sga, err error) {
	return result, a.tx.Find(&result)
}

func (a timesheetBelongsToSgaTx) Append(values ...*models.Sga) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a timesheetBelongsToSgaTx) Replace(values ...*models.Sga) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a timesheetBelongsToSgaTx) Delete(values ...*models.Sga) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a timesheetBelongsToSgaTx) Clear() error {
	return a.tx.Clear()
}

func (a timesheetBelongsToSgaTx) Count() int64 {
	return a.tx.Count()
}

func (a timesheetBelongsToSgaTx) Unscoped() *timesheetBelongsToSgaTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type timesheetBelongsToProject struct {
	db *gorm.DB

	field.RelationField
}

func (a timesheetBelongsToProject) Where(conds ...field.Expr) *timesheetBelongsToProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a timesheetBelongsToProject) WithContext(ctx context.Context) *timesheetBelongsToProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a timesheetBelongsToProject) Session(session *gorm.Session) *timesheetBelongsToProject {
	a.db = a.db.Session(session)
	return &a
}

func (a timesheetBelongsToProject) Model(m *models.Timesheet) *timesheetBelongsToProjectTx {
	return &timesheetBelongsToProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a timesheetBelongsToProject) Unscoped() *timesheetBelongsToProject {
	a.db = a.db.Unscoped()
	return &a
}

type timesheetBelongsToProjectTx struct{ tx *gorm.Association }

func (a timesheetBelongsToProjectTx) Find() (result *models.Project, err error) {
	return result, a.tx.Find(&result)
}

func (a timesheetBelongsToProjectTx) Append(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a timesheetBelongsToProjectTx) Replace(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a timesheetBelongsToProjectTx) Delete(values ...*models.Project) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a timesheetBelongsToProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a timesheetBelongsToProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a timesheetBelongsToProjectTx) Unscoped() *timesheetBelongsToProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type timesheetDo struct{ gen.DO }

type ITimesheetDo interface {
	gen.SubQuery
	Debug() ITimesheetDo
	WithContext(ctx context.Context) ITimesheetDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITimesheetDo
	WriteDB() ITimesheetDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITimesheetDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITimesheetDo
	Not(conds ...gen.Condition) ITimesheetDo
	Or(conds ...gen.Condition) ITimesheetDo
	Select(conds ...field.Expr) ITimesheetDo
	Where(conds ...gen.Condition) ITimesheetDo
	Order(conds ...field.Expr) ITimesheetDo
	Distinct(cols ...field.Expr) ITimesheetDo
	Omit(cols ...field.Expr) ITimesheetDo
	Join(table schema.Tabler, on ...field.Expr) ITimesheetDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITimesheetDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITimesheetDo
	Group(cols ...field.Expr) ITimesheetDo
	Having(conds ...gen.Condition) ITimesheetDo
	Limit(limit int) ITimesheetDo
	Offset(offset int) ITimesheetDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITimesheetDo
	Unscoped() ITimesheetDo
	Create(values ...*models.Timesheet) error
	CreateInBatches(values []*models.Timesheet, batchSize int) error
	Save(values ...*models.Timesheet) error
	First() (*models.Timesheet, error)
	Take() (*models.Timesheet, error)
	Last() (*models.Timesheet, error)
	Find() ([]*models.Timesheet, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Timesheet, err error)
	FindInBatches(result *[]*models.Timesheet, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Timesheet) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITimesheetDo
	Assign(attrs ...field.AssignExpr) ITimesheetDo
	Joins(fields ...field.RelationField) ITimesheetDo
	Preload(fields ...field.RelationField) ITimesheetDo
	FirstOrInit() (*models.Timesheet, error)
	FirstOrCreate() (*models.Timesheet, error)
	FindByPage(offset int, limit int) (result []*models.Timesheet, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITimesheetDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t timesheetDo) Debug() ITimesheetDo {
	return t.withDO(t.DO.Debug())
}

func (t timesheetDo) WithContext(ctx context.Context) ITimesheetDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t timesheetDo) ReadDB() ITimesheetDo {
	return t.Clauses(dbresolver.Read)
}

func (t timesheetDo) WriteDB() ITimesheetDo {
	return t.Clauses(dbresolver.Write)
}

func (t timesheetDo) Session(config *gorm.Session) ITimesheetDo {
	return t.withDO(t.DO.Session(config))
}

func (t timesheetDo) Clauses(conds ...clause.Expression) ITimesheetDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t timesheetDo) Returning(value interface{}, columns ...string) ITimesheetDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t timesheetDo) Not(conds ...gen.Condition) ITimesheetDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t timesheetDo) Or(conds ...gen.Condition) ITimesheetDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t timesheetDo) Select(conds ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t timesheetDo) Where(conds ...gen.Condition) ITimesheetDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t timesheetDo) Order(conds ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t timesheetDo) Distinct(cols ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t timesheetDo) Omit(cols ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t timesheetDo) Join(table schema.Tabler, on ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t timesheetDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t timesheetDo) RightJoin(table schema.Tabler, on ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t timesheetDo) Group(cols ...field.Expr) ITimesheetDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t timesheetDo) Having(conds ...gen.Condition) ITimesheetDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t timesheetDo) Limit(limit int) ITimesheetDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t timesheetDo) Offset(offset int) ITimesheetDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t timesheetDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITimesheetDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t timesheetDo) Unscoped() ITimesheetDo {
	return t.withDO(t.DO.Unscoped())
}

func (t timesheetDo) Create(values ...*models.Timesheet) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t timesheetDo) CreateInBatches(values []*models.Timesheet, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t timesheetDo) Save(values ...*models.Timesheet) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t timesheetDo) First() (*models.Timesheet, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Timesheet), nil
	}
}

func (t timesheetDo) Take() (*models.Timesheet, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Timesheet), nil
	}
}

func (t timesheetDo) Last() (*models.Timesheet, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Timesheet), nil
	}
}

func (t timesheetDo) Find() ([]*models.Timesheet, error) {
	result, err := t.DO.Find()
	return result.([]*models.Timesheet), err
}

func (t timesheetDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Timesheet, err error) {
	buf := make([]*models.Timesheet, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t timesheetDo) FindInBatches(result *[]*models.Timesheet, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t timesheetDo) Attrs(attrs ...field.AssignExpr) ITimesheetDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t timesheetDo) Assign(attrs ...field.AssignExpr) ITimesheetDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t timesheetDo) Joins(fields ...field.RelationField) ITimesheetDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t timesheetDo) Preload(fields ...field.RelationField) ITimesheetDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t timesheetDo) FirstOrInit() (*models.Timesheet, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Timesheet), nil
	}
}

func (t timesheetDo) FirstOrCreate() (*models.Timesheet, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Timesheet), nil
	}
}

func (t timesheetDo) FindByPage(offset int, limit int) (result []*models.Timesheet, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t timesheetDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t timesheetDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t timesheetDo) Delete(models ...*models.Timesheet) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *timesheetDo) withDO(do gen.Dao) *timesheetDo {
	t.DO = *do.(*gen.DO)
	return t
}
