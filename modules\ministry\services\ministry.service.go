package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/ministry/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/ministry/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IMinistryService interface {
	Create(input *dto.MinistryCreatePayload) (*models.Ministry, core.IError)
	Update(id string, input *dto.MinistryUpdatePayload) (*models.Ministry, core.IError)
	Find(id string) (*models.Ministry, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Ministry], core.IError)
	Delete(id string) core.IError
}

type ministryService struct {
	ctx core.IContext
}

func (s ministryService) Create(input *dto.MinistryCreatePayload) (*models.Ministry, core.IError) {
	ministry := &models.Ministry{
		BaseModel:   models.NewBaseModel(),
		NameTh:      input.NameTh,
		NameEn:      utils.ToPointer(input.NameEn),
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.Ministry(s.ctx).Create(ministry)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(ministry.ID)
}

func (s ministryService) Update(id string, input *dto.MinistryUpdatePayload) (*models.Ministry, core.IError) {
	ministry, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.NameTh != "" {
		ministry.NameTh = input.NameTh
	}

	if input.NameEn != "" {
		ministry.NameEn = utils.ToPointer(input.NameEn)
	}

	ministry.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repositories.Ministry(s.ctx).Where("id = ?", id).Updates(ministry)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(ministry.ID)
}

func (s ministryService) Find(id string) (*models.Ministry, core.IError) {
	return repositories.Ministry(s.ctx, repositories.MinistryWithDepartments()).FindOne("id = ?", id)
}

func (s ministryService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Ministry], core.IError) {
	return repositories.Ministry(s.ctx, repositories.MinistryOrderBy(pageOptions), repositories.MinistryWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s ministryService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.Ministry(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewMinistryService(ctx core.IContext) IMinistryService {
	return &ministryService{ctx: ctx}
}
