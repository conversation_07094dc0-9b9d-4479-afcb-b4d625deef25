# Docker

To create documentation for the Docker page, you can follow the template below and provide detailed
instructions on how to deploy the GoLang application using Docker.

## Docker Deployment Guide for GoLang Application

This guide provides step-by-step instructions on how to deploy a GoLang application using Docker.

### Prerequisites

Before you begin, make sure you have the following prerequisites installed on your system:

- Docker: [Install Docker](https://docs.docker.com/get-docker/)

Sure! Here's a step-by-step guide on getting started with deploying a GoLang application in Docker based on the provided code:

### Step 1: Set Up Environment Variables
Create a file named `.env` and add the following content:

```
HOST=0.0.0.0:3000
ENV=dev
LOG_LEVEL=debug

SERVICE=API

DB_DRIVER=mysql
DB_USER=my_user
DB_PASSWORD=my_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=finesign
```

Adjust the values of the environment variables as per your requirements.

### Step 2: Dockerfile for GoLang Application
Create a file named `Dockerfile` and add the following content:

```Dockerfile
FROM registry.finema.co/finema/idin-core:1.4.2

# Copy Go module files to the build environment
ADD go.mod go.sum /app/

# Download Go module dependencies
RUN go mod download

# Add the entire local directory to the build environment
ADD . /app/

# Build the Go application inside the build environment
RUN go build -o main

# Create the production image
FROM alpine:3.16

# Copy the executable from the build environment to the production image
COPY --from=0 /app/main /main

# Set the default command to run the executable
CMD ./main

```

This Dockerfile sets up the GoLang environment, copies the necessary files, builds the GoLang application, and runs it.

### Step 3: Dockerfile for Database Migrations
Create a file named `Dockerfile.migrate` and add the following content:

```Dockerfile
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy the package.json file to the working directory
COPY ./package.json /app

# Copy the yarn.lock file to the working directory
COPY ./yarn.lock /app

# Install dependencies using yarn
RUN yarn

# Copy the knexfile.ts file to the working directory
COPY ./knexfile.ts /app

# Copy the migrations directory to the app directory inside the container
COPY ./migrations /app/migrations

# Set the default command to run the migration script
CMD yarn migrate

```

This Dockerfile sets up the Node.js environment, installs the necessary dependencies, and runs the database migrations using Knex.js.

### Step 4: Dockerfile for Database Seeds
Create a file named `Dockerfile.seed` and add the following content:

```Dockerfile
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy the package.json file to the working directory
COPY ./package.json /app

# Copy the yarn.lock file to the working directory
COPY ./yarn.lock /app

# Install dependencies using yarn
RUN yarn

# Copy the knexfile.ts file to the working directory
COPY ./knexfile.ts /app

# Copy the seeds directory to the app directory inside the container
COPY ./seeds /app/seeds

# Set the default command to run the seed script
CMD yarn seed

```

This Dockerfile sets up the Node.js environment, installs the necessary dependencies, and runs the database seeds using Knex.js.

### Step 5: Build and Run the Docker Containers
1. Open a terminal and navigate to the root directory of your project.
2. Build the Docker image for the GoLang application by running the following command:

   ```shell
   docker build -t my-golang-app .
   ```

   This will build the Docker image using the `Dockerfile` in the current directory.

3. Run the Docker container for the GoLang application by executing the following command:

   ```shell
   docker run -p 3000:3000 --env-file .env my-golang-app
   ```

   This will start the container and map port 3000 of the container to port 3000 of your local machine.

4. Open another terminal and navigate to the root directory of your project.
5. Build the Docker image for the database migrations by running the following command:

   ```shell
   docker build -t my-migrate-image -f Dockerfile.migrate .
   ```

   This will build the Docker image for the database migrations using the `Dockerfile.migrate` file.

6. Run the Docker container for the database migrations by executing the following command:

   ```shell
   docker run --env-file .env my-migrate-image
   ```

   This will execute the database migrations inside the container.

7. Build the Docker image for the database seeds by running the


### Accessing the Application

Your GoLang application is now deployed and accessible at `http://localhost:3000`.

### Additional Notes

- If your Go application listens on a different port, make sure to update the port mapping (`-p`) in
  the `docker run` command accordingly.
- Adjust the environment variables in the `.env` file based on your application's requirements.
- For more advanced Docker usage and customization, refer to
  the [Docker documentation](https://docs.docker.com/).

That's it! You have successfully deployed your GoLang application using Docker. Feel free to
customize the documentation to include any specific details or additional instructions relevant to
your application.
