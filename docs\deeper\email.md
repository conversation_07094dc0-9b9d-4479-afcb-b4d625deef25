# Email
functionality for sending and parsing emails using the [go-gomail/gomail at v2 (github.com)](https://github.com/go-gomail/gomail/tree/v2) package.

Interface
-------------

```go
type IEmail interface {
	SendHTML(from string, to []string, subject string, body string) IError
	SendHTMLWithAttach(from string, to []string, subject string, body string, file []byte, fileName string) IError
	SendText(from string, to []string, subject string, body string) IError
	ParseHTMLToString(path string, data interface{}) (string, IError)
}

```

Functions
-------------

#### NewEmail

`func NewEmail(ctx IContext) IEmail`

-   Description: Creates a new instance of the email service.
-   Parameters:
  -   `ctx`: An object implementing the `IContext` interface.
-   Returns: An object that implements the `IEmail` interface.

### Structs

#### email

-   `ctx`: An object implementing the `IContext` interface.

#### Methods

##### ParseHTMLToString

`func (s email) ParseHTMLToString(path string, data interface{}) (string, IError)`

-   Description: Parses an HTML template file and returns the string representation.
-   Parameters:
  -   `path`: The path to the HTML template file.
  -   `data`: The data used for template rendering.
-   Returns:
  -   The parsed HTML string.
  -   An error of type `IError` if parsing fails.

##### SendHTML

`func (s email) SendHTML(from string, to []string, subject string, body string) IError`

-   Description: Sends an HTML email.
-   Parameters:
  -   `from`: The email sender.
  -   `to`: An array of email recipients.
  -   `subject`: The email subject.
  -   `body`: The HTML body of the email.
-   Returns: An error of type `IError` if sending the email fails.

##### SendHTMLWithAttach

`func (s email) SendHTMLWithAttach(from string, to []string, subject string, body string, fileByte []byte, fileName string) IError`

-   Description: Sends an HTML email with attachments.
-   Parameters:
  -   `from`: The email sender.
  -   `to`: An array of email recipients.
  -   `subject`: The email subject.
  -   `body`: The HTML body of the email.
  -   `fileByte`: The byte slice representing the attachment file.
  -   `fileName`: The name of the attachment file.
-   Returns: An error of type `IError` if sending the email fails.

##### SendText

`func (s email) SendText(from string, to []string, subject string, body string) IError`

-   Description: Sends a plain text email.
-   Parameters:
  -   `from`: The email sender.
  -   `to`: An array of email recipients.
  -   `subject`: The email subject.
  -   `body`: The plain text body of the email.
-   Returns: An error of type `IError` if sending the email fails.

##### send

`func (s email) send(msg *gomail.Message) IError`

-   Description: Sends the email using the `gomail` package.
-   Parameters:
  -   `msg`: The `gomail.Message` object representing the email.
-   Returns: An error of

Example
-------------

Here's an example code snippet that demonstrates the usage of the `core` package for sending an HTML email:

```go
package main

import (
  "fmt"
  core "gitlab.finema.co/finema/idin-core"
)

func SendEmail(ctx core.IContext) {
  // Create an instance of the email service
  emailService := core.NewEmail(ctx)
  
  // Define the email details
  from := "<EMAIL>"
  to := []string{"<EMAIL>", "<EMAIL>"}
  subject := "Hello, World!"
  body := "<h1>This is an HTML email</h1><p>Sample email content.</p>"
  
  // Send the HTML email
  ierr := emailService.SendHTML(from, to, subject, body)
  if ierr != nil {
    fmt.Printf("Failed to send email: %v\n", ierr)
    return
  }
  
  fmt.Println("Email sent successfully!")
}
```

In this example, the `core` package is imported, and an instance of the email service is created using `core.NewEmail(ctx)`. The email details, such as the sender, recipients, subject, and HTML body, are defined. Then, the `SendHTML` method of the email service is called with the email details to send the HTML email.

If the email sending operation encounters an error, it is captured in the `err` variable. Otherwise, a success message is printed.

You can adapt this example code to suit your needs by customizing the email details and incorporating it into your application to send HTML emails using the `core` package.
