package handlers

import (
	"net/http"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectContractController struct {
}

// PMO Project Contract methods
func (m PMOProjectContractController) ContractFind(c core.IHTTPContext) error {
	input := &requests.PMOContractFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoContractSvc := services.NewPMOProjectContractService(c)
	contract, err := pmoContractSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, contract)
}

func (m PMOProjectContractController) ContractCreate(c core.IHTTPContext) error {
	input := &requests.PMOContractCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoContractSvc := services.NewPMOProjectContractService(c)
	signingDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.SigningDate))
	startDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.StartDate))
	endDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.EndDate))
	payload := &dto.PMOContractCreatePayload{
		ProjectID:            c.Param("id"),
		ContractNo:           utils.ToNonPointer(input.ContractNo),
		Value:                utils.ToNonPointer(input.Value),
		SigningDate:          &signingDate,
		StartDate:            &startDate,
		EndDate:              &endDate,
		DurationDay:          utils.ToNonPointer(input.DurationDay),
		WarrantyDurationDay:  utils.ToNonPointer(input.WarrantyDurationDay),
		WarrantyDurationYear: utils.ToNonPointer(input.WarrantyDurationYear),
		Prime:                utils.ToNonPointer(input.Prime),
		PenaltyFee:           utils.ToNonPointer(input.PenaltyFee),
		IsLegalizeStamp:      utils.ToNonPointer(input.IsLegalizeStamp),
	}

	contract, err := pmoContractSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, contract)
}

func (m PMOProjectContractController) ContractVersionsFind(c core.IHTTPContext) error {
	pmoContractSvc := services.NewPMOProjectContractService(c)
	res, err := pmoContractSvc.ContractVersionsFind(c.Param("id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
