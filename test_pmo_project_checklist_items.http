### PMO Project Checklist Items API Tests

@baseUrl = http://localhost:8080
@projectId = your-project-id-here
@token = your-auth-token-here

### 1. Get Project Checklist Items (Pagination)
GET {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items
Authorization: Bearer {{token}}
Content-Type: application/json

### 2. Get Project Checklist Items with filters
GET {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items?tab_key=INFO&is_checked=false&page=1&limit=10
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. Create Project Checklist Item
POST {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "tab_key": "INFO",
  "detail": "Test checklist item detail",
  "is_checked": false
}

### 4. Get Project Checklist Item by ID
@checklistItemId = your-checklist-item-id-here
GET {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items/{{checklistItemId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### 5. Update Project Checklist Item
PUT {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items/{{checklistItemId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "tab_key": "SALES",
  "detail": "Updated checklist item detail",
  "is_checked": true
}

### 6. Delete Project Checklist Item
DELETE {{baseUrl}}/pmo/projects/{{projectId}}/checklist-items/{{checklistItemId}}
Authorization: Bearer {{token}}
Content-Type: application/json
