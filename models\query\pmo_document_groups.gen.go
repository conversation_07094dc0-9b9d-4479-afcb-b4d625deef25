// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMODocumentGroup(db *gorm.DB, opts ...gen.DOOption) pMODocumentGroup {
	_pMODocumentGroup := pMODocumentGroup{}

	_pMODocumentGroup.pMODocumentGroupDo.UseDB(db, opts...)
	_pMODocumentGroup.pMODocumentGroupDo.UseModel(&models.PMODocumentGroup{})

	tableName := _pMODocumentGroup.pMODocumentGroupDo.TableName()
	_pMODocumentGroup.ALL = field.NewAsterisk(tableName)
	_pMODocumentGroup.ID = field.NewString(tableName, "id")
	_pMODocumentGroup.CreatedAt = field.NewTime(tableName, "created_at")
	_pMODocumentGroup.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMODocumentGroup.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMODocumentGroup.ProjectID = field.NewString(tableName, "project_id")
	_pMODocumentGroup.TabKey = field.NewString(tableName, "tab_key")
	_pMODocumentGroup.GroupName = field.NewString(tableName, "group_name")
	_pMODocumentGroup.SharepointURL = field.NewString(tableName, "sharepoint_url")
	_pMODocumentGroup.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMODocumentGroup.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMODocumentGroup.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMODocumentGroup.Project = pMODocumentGroupHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMODocumentGroup.DocumentItems = pMODocumentGroupHasManyDocumentItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DocumentItems", "models.PMODocumentItem"),
	}

	_pMODocumentGroup.fillFieldMap()

	return _pMODocumentGroup
}

type pMODocumentGroup struct {
	pMODocumentGroupDo

	ALL           field.Asterisk
	ID            field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	ProjectID     field.String
	TabKey        field.String
	GroupName     field.String
	SharepointURL field.String
	CreatedByID   field.String
	UpdatedByID   field.String
	DeletedByID   field.String
	Project       pMODocumentGroupHasOneProject

	DocumentItems pMODocumentGroupHasManyDocumentItems

	fieldMap map[string]field.Expr
}

func (p pMODocumentGroup) Table(newTableName string) *pMODocumentGroup {
	p.pMODocumentGroupDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMODocumentGroup) As(alias string) *pMODocumentGroup {
	p.pMODocumentGroupDo.DO = *(p.pMODocumentGroupDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMODocumentGroup) updateTableName(table string) *pMODocumentGroup {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.TabKey = field.NewString(table, "tab_key")
	p.GroupName = field.NewString(table, "group_name")
	p.SharepointURL = field.NewString(table, "sharepoint_url")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMODocumentGroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMODocumentGroup) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["group_name"] = p.GroupName
	p.fieldMap["sharepoint_url"] = p.SharepointURL
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMODocumentGroup) clone(db *gorm.DB) pMODocumentGroup {
	p.pMODocumentGroupDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.DocumentItems.db = db.Session(&gorm.Session{Initialized: true})
	p.DocumentItems.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMODocumentGroup) replaceDB(db *gorm.DB) pMODocumentGroup {
	p.pMODocumentGroupDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.DocumentItems.db = db.Session(&gorm.Session{})
	return p
}

type pMODocumentGroupHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMODocumentGroupHasOneProject) Where(conds ...field.Expr) *pMODocumentGroupHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentGroupHasOneProject) WithContext(ctx context.Context) *pMODocumentGroupHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentGroupHasOneProject) Session(session *gorm.Session) *pMODocumentGroupHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentGroupHasOneProject) Model(m *models.PMODocumentGroup) *pMODocumentGroupHasOneProjectTx {
	return &pMODocumentGroupHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentGroupHasOneProject) Unscoped() *pMODocumentGroupHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentGroupHasOneProjectTx struct{ tx *gorm.Association }

func (a pMODocumentGroupHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentGroupHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentGroupHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentGroupHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentGroupHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentGroupHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentGroupHasOneProjectTx) Unscoped() *pMODocumentGroupHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentGroupHasManyDocumentItems struct {
	db *gorm.DB

	field.RelationField
}

func (a pMODocumentGroupHasManyDocumentItems) Where(conds ...field.Expr) *pMODocumentGroupHasManyDocumentItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMODocumentGroupHasManyDocumentItems) WithContext(ctx context.Context) *pMODocumentGroupHasManyDocumentItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMODocumentGroupHasManyDocumentItems) Session(session *gorm.Session) *pMODocumentGroupHasManyDocumentItems {
	a.db = a.db.Session(session)
	return &a
}

func (a pMODocumentGroupHasManyDocumentItems) Model(m *models.PMODocumentGroup) *pMODocumentGroupHasManyDocumentItemsTx {
	return &pMODocumentGroupHasManyDocumentItemsTx{a.db.Model(m).Association(a.Name())}
}

func (a pMODocumentGroupHasManyDocumentItems) Unscoped() *pMODocumentGroupHasManyDocumentItems {
	a.db = a.db.Unscoped()
	return &a
}

type pMODocumentGroupHasManyDocumentItemsTx struct{ tx *gorm.Association }

func (a pMODocumentGroupHasManyDocumentItemsTx) Find() (result []*models.PMODocumentItem, err error) {
	return result, a.tx.Find(&result)
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Append(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Replace(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Delete(values ...*models.PMODocumentItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Count() int64 {
	return a.tx.Count()
}

func (a pMODocumentGroupHasManyDocumentItemsTx) Unscoped() *pMODocumentGroupHasManyDocumentItemsTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMODocumentGroupDo struct{ gen.DO }

type IPMODocumentGroupDo interface {
	gen.SubQuery
	Debug() IPMODocumentGroupDo
	WithContext(ctx context.Context) IPMODocumentGroupDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMODocumentGroupDo
	WriteDB() IPMODocumentGroupDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMODocumentGroupDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMODocumentGroupDo
	Not(conds ...gen.Condition) IPMODocumentGroupDo
	Or(conds ...gen.Condition) IPMODocumentGroupDo
	Select(conds ...field.Expr) IPMODocumentGroupDo
	Where(conds ...gen.Condition) IPMODocumentGroupDo
	Order(conds ...field.Expr) IPMODocumentGroupDo
	Distinct(cols ...field.Expr) IPMODocumentGroupDo
	Omit(cols ...field.Expr) IPMODocumentGroupDo
	Join(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo
	Group(cols ...field.Expr) IPMODocumentGroupDo
	Having(conds ...gen.Condition) IPMODocumentGroupDo
	Limit(limit int) IPMODocumentGroupDo
	Offset(offset int) IPMODocumentGroupDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentGroupDo
	Unscoped() IPMODocumentGroupDo
	Create(values ...*models.PMODocumentGroup) error
	CreateInBatches(values []*models.PMODocumentGroup, batchSize int) error
	Save(values ...*models.PMODocumentGroup) error
	First() (*models.PMODocumentGroup, error)
	Take() (*models.PMODocumentGroup, error)
	Last() (*models.PMODocumentGroup, error)
	Find() ([]*models.PMODocumentGroup, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentGroup, err error)
	FindInBatches(result *[]*models.PMODocumentGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMODocumentGroup) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMODocumentGroupDo
	Assign(attrs ...field.AssignExpr) IPMODocumentGroupDo
	Joins(fields ...field.RelationField) IPMODocumentGroupDo
	Preload(fields ...field.RelationField) IPMODocumentGroupDo
	FirstOrInit() (*models.PMODocumentGroup, error)
	FirstOrCreate() (*models.PMODocumentGroup, error)
	FindByPage(offset int, limit int) (result []*models.PMODocumentGroup, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMODocumentGroupDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMODocumentGroupDo) Debug() IPMODocumentGroupDo {
	return p.withDO(p.DO.Debug())
}

func (p pMODocumentGroupDo) WithContext(ctx context.Context) IPMODocumentGroupDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMODocumentGroupDo) ReadDB() IPMODocumentGroupDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMODocumentGroupDo) WriteDB() IPMODocumentGroupDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMODocumentGroupDo) Session(config *gorm.Session) IPMODocumentGroupDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMODocumentGroupDo) Clauses(conds ...clause.Expression) IPMODocumentGroupDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMODocumentGroupDo) Returning(value interface{}, columns ...string) IPMODocumentGroupDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMODocumentGroupDo) Not(conds ...gen.Condition) IPMODocumentGroupDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMODocumentGroupDo) Or(conds ...gen.Condition) IPMODocumentGroupDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMODocumentGroupDo) Select(conds ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMODocumentGroupDo) Where(conds ...gen.Condition) IPMODocumentGroupDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMODocumentGroupDo) Order(conds ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMODocumentGroupDo) Distinct(cols ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMODocumentGroupDo) Omit(cols ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMODocumentGroupDo) Join(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMODocumentGroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMODocumentGroupDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMODocumentGroupDo) Group(cols ...field.Expr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMODocumentGroupDo) Having(conds ...gen.Condition) IPMODocumentGroupDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMODocumentGroupDo) Limit(limit int) IPMODocumentGroupDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMODocumentGroupDo) Offset(offset int) IPMODocumentGroupDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMODocumentGroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMODocumentGroupDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMODocumentGroupDo) Unscoped() IPMODocumentGroupDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMODocumentGroupDo) Create(values ...*models.PMODocumentGroup) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMODocumentGroupDo) CreateInBatches(values []*models.PMODocumentGroup, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMODocumentGroupDo) Save(values ...*models.PMODocumentGroup) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMODocumentGroupDo) First() (*models.PMODocumentGroup, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentGroup), nil
	}
}

func (p pMODocumentGroupDo) Take() (*models.PMODocumentGroup, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentGroup), nil
	}
}

func (p pMODocumentGroupDo) Last() (*models.PMODocumentGroup, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentGroup), nil
	}
}

func (p pMODocumentGroupDo) Find() ([]*models.PMODocumentGroup, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMODocumentGroup), err
}

func (p pMODocumentGroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMODocumentGroup, err error) {
	buf := make([]*models.PMODocumentGroup, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMODocumentGroupDo) FindInBatches(result *[]*models.PMODocumentGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMODocumentGroupDo) Attrs(attrs ...field.AssignExpr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMODocumentGroupDo) Assign(attrs ...field.AssignExpr) IPMODocumentGroupDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMODocumentGroupDo) Joins(fields ...field.RelationField) IPMODocumentGroupDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMODocumentGroupDo) Preload(fields ...field.RelationField) IPMODocumentGroupDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMODocumentGroupDo) FirstOrInit() (*models.PMODocumentGroup, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentGroup), nil
	}
}

func (p pMODocumentGroupDo) FirstOrCreate() (*models.PMODocumentGroup, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMODocumentGroup), nil
	}
}

func (p pMODocumentGroupDo) FindByPage(offset int, limit int) (result []*models.PMODocumentGroup, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMODocumentGroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMODocumentGroupDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMODocumentGroupDo) Delete(models ...*models.PMODocumentGroup) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMODocumentGroupDo) withDO(do gen.Dao) *pMODocumentGroupDo {
	p.DO = *do.(*gen.DO)
	return p
}
