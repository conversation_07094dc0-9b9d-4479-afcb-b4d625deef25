package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOBiddingInfo represents project bidding information
type PMOBiddingInfo struct {
	BaseModel
	ProjectID    string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	BiddingType  string     `json:"bidding_type" gorm:"column:bidding_type"`
	BiddingValue float64    `json:"bidding_value" gorm:"column:bidding_value"`
	TenderDate   *time.Time `json:"tender_date" gorm:"column:tender_date;type:date"`
	TenderEntity string     `json:"tender_entity" gorm:"column:tender_entity"`
	AnnounceDate *time.Time `json:"announce_date" gorm:"column:announce_date;type:date"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID;references:ID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID;references:ID"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBiddingInfo) TableName() string {
	return "pmo_bidding_info"
}

// PMOBiddingInfoVersion represents versioned bidding information
type PMOBiddingInfoVersion struct {
	PMOBiddingInfo
	OriginalID string `json:"bidding_info_id" gorm:"column:bidding_info_id;type:uuid;index"`
}

func (PMOBiddingInfoVersion) TableName() string {
	return "pmo_bidding_info_versions"
}

func (u *PMOBiddingInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
