// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMOContractInfo(db *gorm.DB, opts ...gen.DOOption) pMOContractInfo {
	_pMOContractInfo := pMOContractInfo{}

	_pMOContractInfo.pMOContractInfoDo.UseDB(db, opts...)
	_pMOContractInfo.pMOContractInfoDo.UseModel(&models.PMOContractInfo{})

	tableName := _pMOContractInfo.pMOContractInfoDo.TableName()
	_pMOContractInfo.ALL = field.NewAsterisk(tableName)
	_pMOContractInfo.ID = field.NewString(tableName, "id")
	_pMOContractInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_pMOContractInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMOContractInfo.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMOContractInfo.ProjectID = field.NewString(tableName, "project_id")
	_pMOContractInfo.ContractNo = field.NewString(tableName, "contract_no")
	_pMOContractInfo.Value = field.NewFloat64(tableName, "value")
	_pMOContractInfo.SigningDate = field.NewTime(tableName, "signing_date")
	_pMOContractInfo.StartDate = field.NewTime(tableName, "start_date")
	_pMOContractInfo.EndDate = field.NewTime(tableName, "end_date")
	_pMOContractInfo.DurationDay = field.NewInt64(tableName, "duration_day")
	_pMOContractInfo.WarrantyDurationDay = field.NewInt64(tableName, "warranty_duration_day")
	_pMOContractInfo.WarrantyDurationYear = field.NewInt64(tableName, "warranty_duration_year")
	_pMOContractInfo.Prime = field.NewString(tableName, "prime")
	_pMOContractInfo.PenaltyFee = field.NewFloat64(tableName, "penalty_fee")
	_pMOContractInfo.IsLegalizeStamp = field.NewBool(tableName, "is_legalize_stamp")
	_pMOContractInfo.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMOContractInfo.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMOContractInfo.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMOContractInfo.Project = pMOContractInfoHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMOContractInfo.CreatedBy = pMOContractInfoBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMOContractInfo.UpdatedBy = pMOContractInfoBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMOContractInfo.fillFieldMap()

	return _pMOContractInfo
}

type pMOContractInfo struct {
	pMOContractInfoDo

	ALL                  field.Asterisk
	ID                   field.String
	CreatedAt            field.Time
	UpdatedAt            field.Time
	DeletedAt            field.Field
	ProjectID            field.String
	ContractNo           field.String
	Value                field.Float64
	SigningDate          field.Time
	StartDate            field.Time
	EndDate              field.Time
	DurationDay          field.Int64
	WarrantyDurationDay  field.Int64
	WarrantyDurationYear field.Int64
	Prime                field.String
	PenaltyFee           field.Float64
	IsLegalizeStamp      field.Bool
	CreatedByID          field.String
	UpdatedByID          field.String
	DeletedByID          field.String
	Project              pMOContractInfoHasOneProject

	CreatedBy pMOContractInfoBelongsToCreatedBy

	UpdatedBy pMOContractInfoBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMOContractInfo) Table(newTableName string) *pMOContractInfo {
	p.pMOContractInfoDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMOContractInfo) As(alias string) *pMOContractInfo {
	p.pMOContractInfoDo.DO = *(p.pMOContractInfoDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMOContractInfo) updateTableName(table string) *pMOContractInfo {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.ContractNo = field.NewString(table, "contract_no")
	p.Value = field.NewFloat64(table, "value")
	p.SigningDate = field.NewTime(table, "signing_date")
	p.StartDate = field.NewTime(table, "start_date")
	p.EndDate = field.NewTime(table, "end_date")
	p.DurationDay = field.NewInt64(table, "duration_day")
	p.WarrantyDurationDay = field.NewInt64(table, "warranty_duration_day")
	p.WarrantyDurationYear = field.NewInt64(table, "warranty_duration_year")
	p.Prime = field.NewString(table, "prime")
	p.PenaltyFee = field.NewFloat64(table, "penalty_fee")
	p.IsLegalizeStamp = field.NewBool(table, "is_legalize_stamp")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")

	p.fillFieldMap()

	return p
}

func (p *pMOContractInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMOContractInfo) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 22)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["contract_no"] = p.ContractNo
	p.fieldMap["value"] = p.Value
	p.fieldMap["signing_date"] = p.SigningDate
	p.fieldMap["start_date"] = p.StartDate
	p.fieldMap["end_date"] = p.EndDate
	p.fieldMap["duration_day"] = p.DurationDay
	p.fieldMap["warranty_duration_day"] = p.WarrantyDurationDay
	p.fieldMap["warranty_duration_year"] = p.WarrantyDurationYear
	p.fieldMap["prime"] = p.Prime
	p.fieldMap["penalty_fee"] = p.PenaltyFee
	p.fieldMap["is_legalize_stamp"] = p.IsLegalizeStamp
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID

}

func (p pMOContractInfo) clone(db *gorm.DB) pMOContractInfo {
	p.pMOContractInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMOContractInfo) replaceDB(db *gorm.DB) pMOContractInfo {
	p.pMOContractInfoDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMOContractInfoHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMOContractInfoHasOneProject) Where(conds ...field.Expr) *pMOContractInfoHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoHasOneProject) WithContext(ctx context.Context) *pMOContractInfoHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoHasOneProject) Session(session *gorm.Session) *pMOContractInfoHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoHasOneProject) Model(m *models.PMOContractInfo) *pMOContractInfoHasOneProjectTx {
	return &pMOContractInfoHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoHasOneProject) Unscoped() *pMOContractInfoHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoHasOneProjectTx struct{ tx *gorm.Association }

func (a pMOContractInfoHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoHasOneProjectTx) Unscoped() *pMOContractInfoHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOContractInfoBelongsToCreatedBy) Where(conds ...field.Expr) *pMOContractInfoBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoBelongsToCreatedBy) WithContext(ctx context.Context) *pMOContractInfoBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoBelongsToCreatedBy) Session(session *gorm.Session) *pMOContractInfoBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoBelongsToCreatedBy) Model(m *models.PMOContractInfo) *pMOContractInfoBelongsToCreatedByTx {
	return &pMOContractInfoBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoBelongsToCreatedBy) Unscoped() *pMOContractInfoBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMOContractInfoBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoBelongsToCreatedByTx) Unscoped() *pMOContractInfoBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMOContractInfoBelongsToUpdatedBy) Where(conds ...field.Expr) *pMOContractInfoBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMOContractInfoBelongsToUpdatedBy) WithContext(ctx context.Context) *pMOContractInfoBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMOContractInfoBelongsToUpdatedBy) Session(session *gorm.Session) *pMOContractInfoBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMOContractInfoBelongsToUpdatedBy) Model(m *models.PMOContractInfo) *pMOContractInfoBelongsToUpdatedByTx {
	return &pMOContractInfoBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMOContractInfoBelongsToUpdatedBy) Unscoped() *pMOContractInfoBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMOContractInfoBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMOContractInfoBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMOContractInfoBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMOContractInfoBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMOContractInfoBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMOContractInfoBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMOContractInfoBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMOContractInfoBelongsToUpdatedByTx) Unscoped() *pMOContractInfoBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMOContractInfoDo struct{ gen.DO }

type IPMOContractInfoDo interface {
	gen.SubQuery
	Debug() IPMOContractInfoDo
	WithContext(ctx context.Context) IPMOContractInfoDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMOContractInfoDo
	WriteDB() IPMOContractInfoDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMOContractInfoDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMOContractInfoDo
	Not(conds ...gen.Condition) IPMOContractInfoDo
	Or(conds ...gen.Condition) IPMOContractInfoDo
	Select(conds ...field.Expr) IPMOContractInfoDo
	Where(conds ...gen.Condition) IPMOContractInfoDo
	Order(conds ...field.Expr) IPMOContractInfoDo
	Distinct(cols ...field.Expr) IPMOContractInfoDo
	Omit(cols ...field.Expr) IPMOContractInfoDo
	Join(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo
	Group(cols ...field.Expr) IPMOContractInfoDo
	Having(conds ...gen.Condition) IPMOContractInfoDo
	Limit(limit int) IPMOContractInfoDo
	Offset(offset int) IPMOContractInfoDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOContractInfoDo
	Unscoped() IPMOContractInfoDo
	Create(values ...*models.PMOContractInfo) error
	CreateInBatches(values []*models.PMOContractInfo, batchSize int) error
	Save(values ...*models.PMOContractInfo) error
	First() (*models.PMOContractInfo, error)
	Take() (*models.PMOContractInfo, error)
	Last() (*models.PMOContractInfo, error)
	Find() ([]*models.PMOContractInfo, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOContractInfo, err error)
	FindInBatches(result *[]*models.PMOContractInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMOContractInfo) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMOContractInfoDo
	Assign(attrs ...field.AssignExpr) IPMOContractInfoDo
	Joins(fields ...field.RelationField) IPMOContractInfoDo
	Preload(fields ...field.RelationField) IPMOContractInfoDo
	FirstOrInit() (*models.PMOContractInfo, error)
	FirstOrCreate() (*models.PMOContractInfo, error)
	FindByPage(offset int, limit int) (result []*models.PMOContractInfo, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMOContractInfoDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMOContractInfoDo) Debug() IPMOContractInfoDo {
	return p.withDO(p.DO.Debug())
}

func (p pMOContractInfoDo) WithContext(ctx context.Context) IPMOContractInfoDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMOContractInfoDo) ReadDB() IPMOContractInfoDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMOContractInfoDo) WriteDB() IPMOContractInfoDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMOContractInfoDo) Session(config *gorm.Session) IPMOContractInfoDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMOContractInfoDo) Clauses(conds ...clause.Expression) IPMOContractInfoDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMOContractInfoDo) Returning(value interface{}, columns ...string) IPMOContractInfoDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMOContractInfoDo) Not(conds ...gen.Condition) IPMOContractInfoDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMOContractInfoDo) Or(conds ...gen.Condition) IPMOContractInfoDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMOContractInfoDo) Select(conds ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMOContractInfoDo) Where(conds ...gen.Condition) IPMOContractInfoDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMOContractInfoDo) Order(conds ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMOContractInfoDo) Distinct(cols ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMOContractInfoDo) Omit(cols ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMOContractInfoDo) Join(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMOContractInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMOContractInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMOContractInfoDo) Group(cols ...field.Expr) IPMOContractInfoDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMOContractInfoDo) Having(conds ...gen.Condition) IPMOContractInfoDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMOContractInfoDo) Limit(limit int) IPMOContractInfoDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMOContractInfoDo) Offset(offset int) IPMOContractInfoDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMOContractInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMOContractInfoDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMOContractInfoDo) Unscoped() IPMOContractInfoDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMOContractInfoDo) Create(values ...*models.PMOContractInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMOContractInfoDo) CreateInBatches(values []*models.PMOContractInfo, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMOContractInfoDo) Save(values ...*models.PMOContractInfo) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMOContractInfoDo) First() (*models.PMOContractInfo, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfo), nil
	}
}

func (p pMOContractInfoDo) Take() (*models.PMOContractInfo, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfo), nil
	}
}

func (p pMOContractInfoDo) Last() (*models.PMOContractInfo, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfo), nil
	}
}

func (p pMOContractInfoDo) Find() ([]*models.PMOContractInfo, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMOContractInfo), err
}

func (p pMOContractInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMOContractInfo, err error) {
	buf := make([]*models.PMOContractInfo, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMOContractInfoDo) FindInBatches(result *[]*models.PMOContractInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMOContractInfoDo) Attrs(attrs ...field.AssignExpr) IPMOContractInfoDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMOContractInfoDo) Assign(attrs ...field.AssignExpr) IPMOContractInfoDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMOContractInfoDo) Joins(fields ...field.RelationField) IPMOContractInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMOContractInfoDo) Preload(fields ...field.RelationField) IPMOContractInfoDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMOContractInfoDo) FirstOrInit() (*models.PMOContractInfo, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfo), nil
	}
}

func (p pMOContractInfoDo) FirstOrCreate() (*models.PMOContractInfo, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMOContractInfo), nil
	}
}

func (p pMOContractInfoDo) FindByPage(offset int, limit int) (result []*models.PMOContractInfo, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMOContractInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMOContractInfoDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMOContractInfoDo) Delete(models ...*models.PMOContractInfo) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMOContractInfoDo) withDO(do gen.Dao) *pMOContractInfoDo {
	p.DO = *do.(*gen.DO)
	return p
}
