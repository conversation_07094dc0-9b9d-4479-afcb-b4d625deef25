package home

import (
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	core "gitlab.finema.co/finema/idin-core"
)

func NewHomeHTTP(e *echo.Echo) {
	e.GET("", core.WithHTTPContext(func(c core.IHTTPContext) error {
		return c.JSON(http.StatusOK, core.Map{
			"status":              "i'm ok",
			"release_version":     c.ENV().String("RELEASE_VERSION"),
			"release_environment": c.ENV().String("RELEASE_ENVIRONMENT"),
		})
	}))
	e.GET("/error", core.WithHTTPContext(func(c core.IHTTPContext) error {
		msg := fmt.Errorf("test error, VERSION %v, ENV %v",
			c.ENV().String("RELEASE_VERSION"),
			c.ENV().String("RELEASE_ENVIRONMENT"))
		c.<PERSON>rror(msg, core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "TEST_ERROR",
			Message: msg.Error(),
		})
		return c.JSON(http.StatusInternalServerError, core.Map{
			"status":              "i'm not ok",
			"release_version":     c.ENV().String("RELEASE_VERSION"),
			"release_environment": c.ENV().String("RELEASE_ENVIRONMENT"),
		})
	}))
}
