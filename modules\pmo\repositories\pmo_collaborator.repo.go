package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Collaborator Repository
var PMOCollaborator = repository.Make[models.PMOCollaborator]()

func PMOCollaboratorOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCollaboratorWithUser() repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		c.Preload("User.Team")
		c.Preload("User.AccessLevel")
		c.Preload("UpdatedBy")
		c.Preload("CreatedBy")
	}
}

func PMOCollaboratorByProjectID(projectID string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCollaboratorByTabKey(tabKey string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if tabKey != "" {
			switch tabKey {
			case "CONFIDENTIAL":
				c.Where("confidential_permission != 'NONE'")
			case "SALES":
				c.Where("sales_permission != 'NONE'")
			case "PRESALES":
				c.Where("presales_permission != 'NONE'")
			case "BIDDING":
				c.Where("bidding_permission != 'NONE'")
			case "PMO":
				c.Where("pmo_permission != 'NONE'")
			}
		}
	}
}
