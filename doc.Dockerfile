# Build stage
FROM node:18.14-alpine3.16 AS builder

# Set the working directory
WORKDIR /app

ARG IDIN_CORE_VERSION

ENV IDIN_CORE_VERSION=${IDIN_CORE_VERSION}

# Install necessary tools
RUN apk add --no-cache git

# Cache npm modules
COPY package*.json yarn.lock ./
RUN yarn --frozen-lockfile
# Copy the source code and build the project
COPY docs docs

RUN IDIN_CORE_VERSION=${IDIN_CORE_VERSION} yarn docs:build

CMD IDIN_CORE_VERSION=${IDIN_CORE_VERSION} yarn docs:preview --port 5173
