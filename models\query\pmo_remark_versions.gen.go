// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.finema.co/finema/finework/finework-api/models"
)

func newPMORemarkVersion(db *gorm.DB, opts ...gen.DOOption) pMORemarkVersion {
	_pMORemarkVersion := pMORemarkVersion{}

	_pMORemarkVersion.pMORemarkVersionDo.UseDB(db, opts...)
	_pMORemarkVersion.pMORemarkVersionDo.UseModel(&models.PMORemarkVersion{})

	tableName := _pMORemarkVersion.pMORemarkVersionDo.TableName()
	_pMORemarkVersion.ALL = field.NewAsterisk(tableName)
	_pMORemarkVersion.ID = field.NewString(tableName, "id")
	_pMORemarkVersion.CreatedAt = field.NewTime(tableName, "created_at")
	_pMORemarkVersion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pMORemarkVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_pMORemarkVersion.ProjectID = field.NewString(tableName, "project_id")
	_pMORemarkVersion.TabKey = field.NewString(tableName, "tab_key")
	_pMORemarkVersion.Detail = field.NewString(tableName, "detail")
	_pMORemarkVersion.CreatedByID = field.NewString(tableName, "created_by_id")
	_pMORemarkVersion.UpdatedByID = field.NewString(tableName, "updated_by_id")
	_pMORemarkVersion.DeletedByID = field.NewString(tableName, "deleted_by_id")
	_pMORemarkVersion.OriginalID = field.NewString(tableName, "remark_id")
	_pMORemarkVersion.Project = pMORemarkVersionHasOneProject{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Project", "models.PMOProject"),
		CreatedBy: struct {
			field.RelationField
			Team struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
			AccessLevel struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			Timesheets struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
			Checkins struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.CreatedBy", "models.User"),
			Team: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Team", "models.Team"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Team.Users", "models.User"),
				},
			},
			AccessLevel: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.AccessLevel", "models.UserAccessLevel"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.AccessLevel.User", "models.User"),
				},
			},
			Timesheets: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Sga struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Timesheets", "models.Timesheet"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.User", "models.User"),
				},
				Sga: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Sga", "models.Sga"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Timesheets.Project", "models.Project"),
				},
			},
			Checkins: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.CreatedBy.Checkins", "models.Checkin"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.CreatedBy.Checkins.User", "models.User"),
				},
			},
		},
		UpdatedBy: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.UpdatedBy", "models.User"),
		},
		Project: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Project", "models.Project"),
		},
		Permission: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Permission", "models.PMOCollaborator"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.UpdatedBy", "models.User"),
			},
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Permission.Project", "models.PMOProject"),
			},
		},
		Collaborators: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.Collaborators", "models.PMOCollaborator"),
		},
		Comments: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Comments", "models.PMOComment"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Comments.Replies", "models.PMOComment"),
			},
		},
		CommentVersions: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
			Replies struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.CommentVersions", "models.PMOCommentVersion"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.User", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Project", "models.PMOProject"),
			},
			Replies: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.CommentVersions.Replies", "models.PMOComment"),
			},
		},
		Remarks: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Remarks", "models.PMORemark"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Remarks.Project", "models.PMOProject"),
			},
		},
		RemarkVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.RemarkVersions", "models.PMORemarkVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.RemarkVersions.Project", "models.PMOProject"),
			},
		},
		DocumentGroups: struct {
			field.RelationField
			Project struct {
				field.RelationField
			}
			DocumentItems struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Project.DocumentGroups", "models.PMODocumentGroup"),
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.Project", "models.PMOProject"),
			},
			DocumentItems: struct {
				field.RelationField
				CreatedBy struct {
					field.RelationField
				}
				UpdatedBy struct {
					field.RelationField
				}
				Group struct {
					field.RelationField
				}
				File struct {
					field.RelationField
				}
				Project struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems", "models.PMODocumentItem"),
				CreatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.CreatedBy", "models.User"),
				},
				UpdatedBy: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.UpdatedBy", "models.User"),
				},
				Group: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Group", "models.PMODocumentGroup"),
				},
				File: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.File", "models.File"),
				},
				Project: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Project.DocumentGroups.DocumentItems.Project", "models.PMOProject"),
				},
			},
		},
		DocumentItems: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Project.DocumentItems", "models.PMODocumentItem"),
		},
		DocumentItemVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.DocumentItemVersions", "models.PMODocumentItemVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.UpdatedBy", "models.User"),
			},
			Group: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Group", "models.PMODocumentGroup"),
			},
			File: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.File", "models.File"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.DocumentItemVersions.Project", "models.PMOProject"),
			},
		},
		Contacts: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Contacts", "models.PMOContact"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Contacts.Project", "models.PMOProject"),
			},
		},
		Competitors: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Competitors", "models.PMOCompetitor"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Competitors.Project", "models.PMOProject"),
			},
		},
		Partners: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.Partners", "models.PMOPartner"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.Partners.Project", "models.PMOProject"),
			},
		},
		BudgetInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfo", "models.PMOBudgetInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfo.Project", "models.PMOProject"),
			},
		},
		BudgetInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BudgetInfoVersions", "models.PMOBudgetInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BudgetInfoVersions.Project", "models.PMOProject"),
			},
		},
		BiddingInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfo", "models.PMOBiddingInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfo.Project", "models.PMOProject"),
			},
		},
		BiddingInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BiddingInfoVersions", "models.PMOBiddingInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BiddingInfoVersions.Project", "models.PMOProject"),
			},
		},
		ContractInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfo", "models.PMOContractInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfo.Project", "models.PMOProject"),
			},
		},
		ContractInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.ContractInfoVersions", "models.PMOContractInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.ContractInfoVersions.Project", "models.PMOProject"),
			},
		},
		BidbondInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfo", "models.PMOBidbondInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfo.Project", "models.PMOProject"),
			},
		},
		BidbondInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.BidbondInfoVersions", "models.PMOBidbondInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.BidbondInfoVersions.Project", "models.PMOProject"),
			},
		},
		LGInfo: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfo", "models.PMOLGInfo"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfo.Project", "models.PMOProject"),
			},
		},
		LGInfoVersions: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.LGInfoVersions", "models.PMOLGInfoVersion"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.LGInfoVersions.Project", "models.PMOProject"),
			},
		},
		VendorItems: struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Project.VendorItems", "models.PMOVendorItem"),
			CreatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.CreatedBy", "models.User"),
			},
			UpdatedBy: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.UpdatedBy", "models.User"),
			},
			Project: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Project.VendorItems.Project", "models.PMOProject"),
			},
		},
	}

	_pMORemarkVersion.CreatedBy = pMORemarkVersionBelongsToCreatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CreatedBy", "models.User"),
	}

	_pMORemarkVersion.UpdatedBy = pMORemarkVersionBelongsToUpdatedBy{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("UpdatedBy", "models.User"),
	}

	_pMORemarkVersion.fillFieldMap()

	return _pMORemarkVersion
}

type pMORemarkVersion struct {
	pMORemarkVersionDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	ProjectID   field.String
	TabKey      field.String
	Detail      field.String
	CreatedByID field.String
	UpdatedByID field.String
	DeletedByID field.String
	OriginalID  field.String
	Project     pMORemarkVersionHasOneProject

	CreatedBy pMORemarkVersionBelongsToCreatedBy

	UpdatedBy pMORemarkVersionBelongsToUpdatedBy

	fieldMap map[string]field.Expr
}

func (p pMORemarkVersion) Table(newTableName string) *pMORemarkVersion {
	p.pMORemarkVersionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pMORemarkVersion) As(alias string) *pMORemarkVersion {
	p.pMORemarkVersionDo.DO = *(p.pMORemarkVersionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pMORemarkVersion) updateTableName(table string) *pMORemarkVersion {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.ProjectID = field.NewString(table, "project_id")
	p.TabKey = field.NewString(table, "tab_key")
	p.Detail = field.NewString(table, "detail")
	p.CreatedByID = field.NewString(table, "created_by_id")
	p.UpdatedByID = field.NewString(table, "updated_by_id")
	p.DeletedByID = field.NewString(table, "deleted_by_id")
	p.OriginalID = field.NewString(table, "remark_id")

	p.fillFieldMap()

	return p
}

func (p *pMORemarkVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pMORemarkVersion) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 14)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["project_id"] = p.ProjectID
	p.fieldMap["tab_key"] = p.TabKey
	p.fieldMap["detail"] = p.Detail
	p.fieldMap["created_by_id"] = p.CreatedByID
	p.fieldMap["updated_by_id"] = p.UpdatedByID
	p.fieldMap["deleted_by_id"] = p.DeletedByID
	p.fieldMap["remark_id"] = p.OriginalID

}

func (p pMORemarkVersion) clone(db *gorm.DB) pMORemarkVersion {
	p.pMORemarkVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Project.db = db.Session(&gorm.Session{Initialized: true})
	p.Project.db.Statement.ConnPool = db.Statement.ConnPool
	p.CreatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.CreatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	p.UpdatedBy.db = db.Session(&gorm.Session{Initialized: true})
	p.UpdatedBy.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p pMORemarkVersion) replaceDB(db *gorm.DB) pMORemarkVersion {
	p.pMORemarkVersionDo.ReplaceDB(db)
	p.Project.db = db.Session(&gorm.Session{})
	p.CreatedBy.db = db.Session(&gorm.Session{})
	p.UpdatedBy.db = db.Session(&gorm.Session{})
	return p
}

type pMORemarkVersionHasOneProject struct {
	db *gorm.DB

	field.RelationField

	CreatedBy struct {
		field.RelationField
		Team struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
		AccessLevel struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		Timesheets struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Sga struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
		Checkins struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
	}
	UpdatedBy struct {
		field.RelationField
	}
	Project struct {
		field.RelationField
	}
	Permission struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Collaborators struct {
		field.RelationField
	}
	Comments struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	CommentVersions struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
		Replies struct {
			field.RelationField
		}
	}
	Remarks struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	RemarkVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	DocumentGroups struct {
		field.RelationField
		Project struct {
			field.RelationField
		}
		DocumentItems struct {
			field.RelationField
			CreatedBy struct {
				field.RelationField
			}
			UpdatedBy struct {
				field.RelationField
			}
			Group struct {
				field.RelationField
			}
			File struct {
				field.RelationField
			}
			Project struct {
				field.RelationField
			}
		}
	}
	DocumentItems struct {
		field.RelationField
	}
	DocumentItemVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Group struct {
			field.RelationField
		}
		File struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Contacts struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Competitors struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	Partners struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BudgetInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BiddingInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	ContractInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	BidbondInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfo struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	LGInfoVersions struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
	VendorItems struct {
		field.RelationField
		CreatedBy struct {
			field.RelationField
		}
		UpdatedBy struct {
			field.RelationField
		}
		Project struct {
			field.RelationField
		}
	}
}

func (a pMORemarkVersionHasOneProject) Where(conds ...field.Expr) *pMORemarkVersionHasOneProject {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMORemarkVersionHasOneProject) WithContext(ctx context.Context) *pMORemarkVersionHasOneProject {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMORemarkVersionHasOneProject) Session(session *gorm.Session) *pMORemarkVersionHasOneProject {
	a.db = a.db.Session(session)
	return &a
}

func (a pMORemarkVersionHasOneProject) Model(m *models.PMORemarkVersion) *pMORemarkVersionHasOneProjectTx {
	return &pMORemarkVersionHasOneProjectTx{a.db.Model(m).Association(a.Name())}
}

func (a pMORemarkVersionHasOneProject) Unscoped() *pMORemarkVersionHasOneProject {
	a.db = a.db.Unscoped()
	return &a
}

type pMORemarkVersionHasOneProjectTx struct{ tx *gorm.Association }

func (a pMORemarkVersionHasOneProjectTx) Find() (result *models.PMOProject, err error) {
	return result, a.tx.Find(&result)
}

func (a pMORemarkVersionHasOneProjectTx) Append(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMORemarkVersionHasOneProjectTx) Replace(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMORemarkVersionHasOneProjectTx) Delete(values ...*models.PMOProject) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMORemarkVersionHasOneProjectTx) Clear() error {
	return a.tx.Clear()
}

func (a pMORemarkVersionHasOneProjectTx) Count() int64 {
	return a.tx.Count()
}

func (a pMORemarkVersionHasOneProjectTx) Unscoped() *pMORemarkVersionHasOneProjectTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMORemarkVersionBelongsToCreatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMORemarkVersionBelongsToCreatedBy) Where(conds ...field.Expr) *pMORemarkVersionBelongsToCreatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMORemarkVersionBelongsToCreatedBy) WithContext(ctx context.Context) *pMORemarkVersionBelongsToCreatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMORemarkVersionBelongsToCreatedBy) Session(session *gorm.Session) *pMORemarkVersionBelongsToCreatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMORemarkVersionBelongsToCreatedBy) Model(m *models.PMORemarkVersion) *pMORemarkVersionBelongsToCreatedByTx {
	return &pMORemarkVersionBelongsToCreatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMORemarkVersionBelongsToCreatedBy) Unscoped() *pMORemarkVersionBelongsToCreatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMORemarkVersionBelongsToCreatedByTx struct{ tx *gorm.Association }

func (a pMORemarkVersionBelongsToCreatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMORemarkVersionBelongsToCreatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMORemarkVersionBelongsToCreatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMORemarkVersionBelongsToCreatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMORemarkVersionBelongsToCreatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMORemarkVersionBelongsToCreatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMORemarkVersionBelongsToCreatedByTx) Unscoped() *pMORemarkVersionBelongsToCreatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMORemarkVersionBelongsToUpdatedBy struct {
	db *gorm.DB

	field.RelationField
}

func (a pMORemarkVersionBelongsToUpdatedBy) Where(conds ...field.Expr) *pMORemarkVersionBelongsToUpdatedBy {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a pMORemarkVersionBelongsToUpdatedBy) WithContext(ctx context.Context) *pMORemarkVersionBelongsToUpdatedBy {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a pMORemarkVersionBelongsToUpdatedBy) Session(session *gorm.Session) *pMORemarkVersionBelongsToUpdatedBy {
	a.db = a.db.Session(session)
	return &a
}

func (a pMORemarkVersionBelongsToUpdatedBy) Model(m *models.PMORemarkVersion) *pMORemarkVersionBelongsToUpdatedByTx {
	return &pMORemarkVersionBelongsToUpdatedByTx{a.db.Model(m).Association(a.Name())}
}

func (a pMORemarkVersionBelongsToUpdatedBy) Unscoped() *pMORemarkVersionBelongsToUpdatedBy {
	a.db = a.db.Unscoped()
	return &a
}

type pMORemarkVersionBelongsToUpdatedByTx struct{ tx *gorm.Association }

func (a pMORemarkVersionBelongsToUpdatedByTx) Find() (result *models.User, err error) {
	return result, a.tx.Find(&result)
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Append(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Replace(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Delete(values ...*models.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Clear() error {
	return a.tx.Clear()
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Count() int64 {
	return a.tx.Count()
}

func (a pMORemarkVersionBelongsToUpdatedByTx) Unscoped() *pMORemarkVersionBelongsToUpdatedByTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type pMORemarkVersionDo struct{ gen.DO }

type IPMORemarkVersionDo interface {
	gen.SubQuery
	Debug() IPMORemarkVersionDo
	WithContext(ctx context.Context) IPMORemarkVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPMORemarkVersionDo
	WriteDB() IPMORemarkVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPMORemarkVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPMORemarkVersionDo
	Not(conds ...gen.Condition) IPMORemarkVersionDo
	Or(conds ...gen.Condition) IPMORemarkVersionDo
	Select(conds ...field.Expr) IPMORemarkVersionDo
	Where(conds ...gen.Condition) IPMORemarkVersionDo
	Order(conds ...field.Expr) IPMORemarkVersionDo
	Distinct(cols ...field.Expr) IPMORemarkVersionDo
	Omit(cols ...field.Expr) IPMORemarkVersionDo
	Join(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo
	Group(cols ...field.Expr) IPMORemarkVersionDo
	Having(conds ...gen.Condition) IPMORemarkVersionDo
	Limit(limit int) IPMORemarkVersionDo
	Offset(offset int) IPMORemarkVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPMORemarkVersionDo
	Unscoped() IPMORemarkVersionDo
	Create(values ...*models.PMORemarkVersion) error
	CreateInBatches(values []*models.PMORemarkVersion, batchSize int) error
	Save(values ...*models.PMORemarkVersion) error
	First() (*models.PMORemarkVersion, error)
	Take() (*models.PMORemarkVersion, error)
	Last() (*models.PMORemarkVersion, error)
	Find() ([]*models.PMORemarkVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMORemarkVersion, err error)
	FindInBatches(result *[]*models.PMORemarkVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PMORemarkVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPMORemarkVersionDo
	Assign(attrs ...field.AssignExpr) IPMORemarkVersionDo
	Joins(fields ...field.RelationField) IPMORemarkVersionDo
	Preload(fields ...field.RelationField) IPMORemarkVersionDo
	FirstOrInit() (*models.PMORemarkVersion, error)
	FirstOrCreate() (*models.PMORemarkVersion, error)
	FindByPage(offset int, limit int) (result []*models.PMORemarkVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPMORemarkVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pMORemarkVersionDo) Debug() IPMORemarkVersionDo {
	return p.withDO(p.DO.Debug())
}

func (p pMORemarkVersionDo) WithContext(ctx context.Context) IPMORemarkVersionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pMORemarkVersionDo) ReadDB() IPMORemarkVersionDo {
	return p.Clauses(dbresolver.Read)
}

func (p pMORemarkVersionDo) WriteDB() IPMORemarkVersionDo {
	return p.Clauses(dbresolver.Write)
}

func (p pMORemarkVersionDo) Session(config *gorm.Session) IPMORemarkVersionDo {
	return p.withDO(p.DO.Session(config))
}

func (p pMORemarkVersionDo) Clauses(conds ...clause.Expression) IPMORemarkVersionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pMORemarkVersionDo) Returning(value interface{}, columns ...string) IPMORemarkVersionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pMORemarkVersionDo) Not(conds ...gen.Condition) IPMORemarkVersionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pMORemarkVersionDo) Or(conds ...gen.Condition) IPMORemarkVersionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pMORemarkVersionDo) Select(conds ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pMORemarkVersionDo) Where(conds ...gen.Condition) IPMORemarkVersionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pMORemarkVersionDo) Order(conds ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pMORemarkVersionDo) Distinct(cols ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pMORemarkVersionDo) Omit(cols ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pMORemarkVersionDo) Join(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pMORemarkVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pMORemarkVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pMORemarkVersionDo) Group(cols ...field.Expr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pMORemarkVersionDo) Having(conds ...gen.Condition) IPMORemarkVersionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pMORemarkVersionDo) Limit(limit int) IPMORemarkVersionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pMORemarkVersionDo) Offset(offset int) IPMORemarkVersionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pMORemarkVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPMORemarkVersionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pMORemarkVersionDo) Unscoped() IPMORemarkVersionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pMORemarkVersionDo) Create(values ...*models.PMORemarkVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pMORemarkVersionDo) CreateInBatches(values []*models.PMORemarkVersion, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pMORemarkVersionDo) Save(values ...*models.PMORemarkVersion) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pMORemarkVersionDo) First() (*models.PMORemarkVersion, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMORemarkVersion), nil
	}
}

func (p pMORemarkVersionDo) Take() (*models.PMORemarkVersion, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMORemarkVersion), nil
	}
}

func (p pMORemarkVersionDo) Last() (*models.PMORemarkVersion, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMORemarkVersion), nil
	}
}

func (p pMORemarkVersionDo) Find() ([]*models.PMORemarkVersion, error) {
	result, err := p.DO.Find()
	return result.([]*models.PMORemarkVersion), err
}

func (p pMORemarkVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PMORemarkVersion, err error) {
	buf := make([]*models.PMORemarkVersion, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pMORemarkVersionDo) FindInBatches(result *[]*models.PMORemarkVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pMORemarkVersionDo) Attrs(attrs ...field.AssignExpr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pMORemarkVersionDo) Assign(attrs ...field.AssignExpr) IPMORemarkVersionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pMORemarkVersionDo) Joins(fields ...field.RelationField) IPMORemarkVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pMORemarkVersionDo) Preload(fields ...field.RelationField) IPMORemarkVersionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pMORemarkVersionDo) FirstOrInit() (*models.PMORemarkVersion, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMORemarkVersion), nil
	}
}

func (p pMORemarkVersionDo) FirstOrCreate() (*models.PMORemarkVersion, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PMORemarkVersion), nil
	}
}

func (p pMORemarkVersionDo) FindByPage(offset int, limit int) (result []*models.PMORemarkVersion, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pMORemarkVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pMORemarkVersionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pMORemarkVersionDo) Delete(models ...*models.PMORemarkVersion) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pMORemarkVersionDo) withDO(do gen.Dao) *pMORemarkVersionDo {
	p.DO = *do.(*gen.DO)
	return p
}
