# Conventional Commits

Conventional Commits is a lightweight convention for commit messages. It provides a consistent and readable format for version control history. This document outlines the Conventional Commits 1.0.0 specification.

Based on https://www.conventionalcommits.org/en/v1.0.0/

## Format

The Conventional Commits format consists of three parts: the type, the optional scope, and the message. Each part is separated by a colon and a space (`: `).

```markdown
<type>(<scope>): <message>
```

Here's an explanation of each part:

- **Type**: Describes the purpose of the commit. It can be one of the following:

  - `feat`: A new feature or enhancement.
  - `fix`: A bug fix.
  - `docs`: Documentation changes.
  - `style`: Code style changes (e.g., formatting).
  - `refactor`: Code changes that don't add features or fix bugs.
  - `test`: Adding or modifying tests.
  - `chore`: Maintenance tasks, build changes, or other non-user-facing changes.

- **Scope** (optional): Represents the component or module being affected by the commit.

- **Message**: A concise and descriptive summary of the change.

## Examples

Here are some examples of conventional commit messages:

- `feat(auth): Add login functionality`
- `fix(api): Handle edge case in search endpoint`
- `docs(readme): Update installation instructions`

## Commit Message Convention

Conventional Commits also introduces a commit message convention to automate versioning and generate changelogs. By following this convention, tools can analyze commit history and determine the appropriate semantic version bump.

The commit message convention is based on prefixes added to the type:

- `BREAKING CHANGE`: Used for commits that introduce backward-incompatible changes. It should be placed at the beginning of the commit message followed by a space and a description.

Here's an example of a commit message with a breaking change:

```
feat(api): Add new API endpoint

BREAKING CHANGE: The response format has changed, and clients need to update their code accordingly.
```

## Why Use Conventional Commits
- Automatically generating CHANGELOGs.
- Automatically determining a semantic version bump (based on the types of commits landed).
- Communicating the nature of changes to teammates, the public, and other stakeholders.
- Triggering build and publish processes.
- Making it easier for people to contribute to your projects, by allowing them to explore a more structured commit history.

## Conclusion

Adopting Conventional Commits provides a structured and consistent approach to writing commit messages. It simplifies the process of generating changelogs and automating versioning. By following the Conventional Commits 1.0.0 specification, teams can improve collaboration and maintain a clean version control history.
