package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMORemarkFind struct {
	core.BaseValidator
	Tab<PERSON><PERSON> *string `json:"tab_key" query:"tab_key"`
}

func (r *PMORemarkFind) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>tr<PERSON>equired(r.Tab<PERSON><PERSON>, "tab_key"))

	r.Must(r.IsStrIn(r.Tab<PERSON><PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.<PERSON>rror()
}
