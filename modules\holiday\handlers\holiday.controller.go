package holiday

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/modules/holiday/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/holiday/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/holiday/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type HolidayController struct {
}

func (m HolidayController) Pagination(c core.IHTTPContext) error {
	holidaySvc := services.NewHolidayService(c)
	res, ierr := holidaySvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m HolidayController) Find(c core.IHTTPContext) error {
	holidaySvc := services.NewHolidayService(c)
	holiday, err := holidaySvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.<PERSON><PERSON><PERSON>())
	}

	return c.JSON(http.StatusOK, holiday)
}

func (m HolidayController) Create(c core.IHTTPContext) error {
	input := &requests.HolidayCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	holidaySvc := services.NewHolidayService(c)
	payload := &dto.HolidayCreatePayload{}
	_ = utils.Copy(payload, input)
	holiday, err := holidaySvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, holiday)
}

func (m HolidayController) Update(c core.IHTTPContext) error {
	input := &requests.HolidayUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	holidaySvc := services.NewHolidayService(c)
	payload := &dto.HolidayUpdatePayload{}
	_ = utils.Copy(payload, input)
	holiday, err := holidaySvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, holiday)
}

func (m HolidayController) Delete(c core.IHTTPContext) error {
	holidaySvc := services.NewHolidayService(c)
	err := holidaySvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
