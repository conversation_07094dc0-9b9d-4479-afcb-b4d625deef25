package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/user/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IMeService interface {
	GetProfile(userID string) (*models.User, core.IError)
	UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError)
	GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError)
	DeleteDevice(userID, tokenID string) core.IError
}

type meService struct {
	ctx core.IContext
}

type MeUpdatePayload struct {
	FullName    string
	DisplayName string
	Position    string
	TeamCode    string
	AvatarURL   string
	JoinedDate  *string
}

func (s meService) GetProfile(userID string) (*models.User, core.IError) {
	user, ierr := repositories.User(s.ctx, repositories.UserWithAllRelation()).FindOne("id = ?", userID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return user, nil
}

func (s meService) UpdateProfile(userID string, input *MeUpdatePayload) (*models.User, core.IError) {
	// Get user directly from repo for updating
	user, ierr := repositories.User(s.ctx, repositories.UserWithAllRelation()).FindOne("id = ?", userID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.FullName != "" {
		user.FullName = input.FullName
	}
	if input.DisplayName != "" {
		user.DisplayName = input.DisplayName
	}
	if input.Position != "" {
		user.Position = input.Position
	}
	if input.TeamCode != "" {
		user.TeamCode = utils.ToPointer(input.TeamCode)
	}
	if input.AvatarURL != "" {
		user.AvatarURL = input.AvatarURL
	}
	if input.JoinedDate != nil {
		joinedDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.JoinedDate))
		if !joinedDate.IsZero() {
			user.JoinedDate = utils.ToPointer(joinedDate)
		}
	}

	ierr = repositories.User(s.ctx).Where("id = ?", userID).Updates(user)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Return updated user from repo
	return repositories.User(s.ctx, repositories.UserWithAllRelation()).FindOne("id = ?", userID)
}

func (s meService) GetDevices(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.UserToken], core.IError) {
	return repositories.UserToken(s.ctx, repositories.UserTokenOrderBy(pageOptions)).Where("user_id = ?", userID).Pagination(pageOptions)
}

func (s meService) DeleteDevice(userID, tokenID string) core.IError {
	// Verify the token belongs to the user
	_, ierr := repositories.UserToken(s.ctx).FindOne("id = ? AND user_id = ?", tokenID, userID)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.UserToken(s.ctx).Delete("id = ? AND user_id = ?", tokenID, userID)
}

func NewMeService(ctx core.IContext) IMeService {
	return &meService{ctx: ctx}
}
