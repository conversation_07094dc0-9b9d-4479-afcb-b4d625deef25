package services

import (
	"gitlab.finema.co/finema/finework/finework-api/modules/sga/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/sga/repositories"
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ISgaService interface {
	Create(input *dto.SgaCreatePayload) (*models.Sga, core.IError)
	Update(id string, input *dto.SgaUpdatePayload) (*models.Sga, core.IError)
	Find(id string) (*models.Sga, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Sga], core.IError)
	Delete(id string) core.IError
}

type SgaService struct {
	ctx core.IContext
}

func (s SgaService) Create(input *dto.SgaCreatePayload) (*models.Sga, core.IError) {
	sga := &models.Sga{
		BaseModel:   models.NewBaseModel(),
		Name:        input.Name,
		Description: utils.ToPointer(input.Description),
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.Sga(s.ctx).Create(sga)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(sga.ID)
}

func (s SgaService) Update(id string, input *dto.SgaUpdatePayload) (*models.Sga, core.IError) {
	sga, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		sga.Name = input.Name
	}

	if input.Description != "" {
		sga.Description = utils.ToPointer(input.Description)
	}

	sga.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repositories.Sga(s.ctx).Where("id = ?", id).Updates(sga)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(sga.ID)
}

func (s SgaService) Find(id string) (*models.Sga, core.IError) {
	return repositories.Sga(s.ctx).FindOne("id = ?", id)
}

func (s SgaService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Sga], core.IError) {
	return repositories.Sga(s.ctx, repositories.SgaOrderBy(pageOptions), repositories.SgaWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s SgaService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.Sga(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewSgaService(ctx core.IContext) ISgaService {
	return &SgaService{ctx: ctx}
}

