package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectPartnerController struct {
}

// PMO Project Partners methods
func (m PMOProjectPartnerController) PartnersPagination(c core.IHTTPContext) error {
	input := &requests.PMOPartnerPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoPartnerSvc := services.NewPMOProjectPartnerService(c)
	res, ierr := pmoPartnerSvc.Pagination(c.Param("id"), c.GetPageOptions(), &dto.PMOPartnerPaginationOptions{})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectPartnerController) PartnersFind(c core.IHTTPContext) error {
	pmoPartnerSvc := services.NewPMOProjectPartnerService(c)
	partner, err := pmoPartnerSvc.Find(c.Param("partner_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, partner)
}

func (m PMOProjectPartnerController) PartnersCreate(c core.IHTTPContext) error {
	input := &requests.PMOPartnerCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoPartnerSvc := services.NewPMOProjectPartnerService(c)
	payload := &dto.PMOPartnerCreatePayload{
		ProjectID: c.Param("id"),
		Company:   utils.ToNonPointer(input.Company),
		Detail:    utils.ToNonPointer(input.Detail),
	}

	partner, err := pmoPartnerSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, partner)
}

func (m PMOProjectPartnerController) PartnersUpdate(c core.IHTTPContext) error {
	input := &requests.PMOPartnerUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoPartnerSvc := services.NewPMOProjectPartnerService(c)
	payload := &dto.PMOPartnerUpdatePayload{
		Company: utils.ToNonPointer(input.Company),
		Detail:  utils.ToNonPointer(input.Detail),
	}

	partner, err := pmoPartnerSvc.Update(c.Param("partner_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, partner)
}

func (m PMOProjectPartnerController) PartnersDelete(c core.IHTTPContext) error {
	pmoPartnerSvc := services.NewPMOProjectPartnerService(c)
	err := pmoPartnerSvc.Delete(c.Param("partner_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
