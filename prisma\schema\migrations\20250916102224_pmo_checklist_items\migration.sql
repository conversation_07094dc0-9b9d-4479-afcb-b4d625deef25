/*
  Warnings:

  - Added the required column `project_id` to the `pmo_checklist_items` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."pmo_checklist_items" ADD COLUMN     "project_id" UUID NOT NULL;

-- CreateTable
CREATE TABLE "public"."pmo_project_checklist_items" (
    "id" UUID NOT NULL,
    "project_id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "detail" TEXT NOT NULL,
    "is_checked" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_project_checklist_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pmo_project_checklist_items_project_id_tab_key_idx" ON "public"."pmo_project_checklist_items"("project_id", "tab_key");

-- AddForeignKey
ALTER TABLE "public"."pmo_checklist_items" ADD CONSTRAINT "pmo_checklist_items_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pmo_project_checklist_items" ADD CONSTRAINT "pmo_project_checklist_items_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."pmo_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
