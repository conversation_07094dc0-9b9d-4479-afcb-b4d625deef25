package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type PMOContactUpdate struct {
	core.BaseValidator
	Fullname *string `json:"fullname"`
	Phone    *string `json:"phone"`
	Email    *string `json:"email"`
	Detail   *string `json:"detail"`
	Company  *string `json:"company"`
	Position *string `json:"position"`
}

func (r *PMOContactUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>tr<PERSON>equired(r.Fullname, "fullname"))
	r.Must(r.<PERSON>(r.Email, "email"))

	return r.Error()
}
