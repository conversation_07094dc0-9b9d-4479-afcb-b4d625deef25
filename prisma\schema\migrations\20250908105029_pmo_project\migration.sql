/*
  Warnings:

  - The `app` column on the `files` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- <PERSON>reateEnum
CREATE TYPE "public"."FileAppKey" AS ENUM ('PMO', 'CLOCKIN', 'TIMESHEET', 'COMMON');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."PMOProjectStatus" AS ENUM ('DRAFT', 'TOR', 'BIDDING', 'PMO', 'WARRANTY', 'CLOSED', 'CANCEL');

-- AlterTable
ALTER TABLE "public"."files" DROP COLUMN "app",
ADD COLUMN     "app" "public"."FileAppKey" NOT NULL DEFAULT 'COMMON';

-- CreateTable
CREATE TABLE "public"."pmo_projects" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "status" "public"."PMOProjectStatus" NOT NULL DEFAULT 'DRAFT',
    "project_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_projects_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "pmo_projects_name_key" ON "public"."pmo_projects"("name");

-- CreateIndex
CREATE UNIQUE INDEX "pmo_projects_slug_key" ON "public"."pmo_projects"("slug");

-- CreateIndex
CREATE INDEX "pmo_projects_name_slug_idx" ON "public"."pmo_projects"("name", "slug");

-- AddForeignKey
ALTER TABLE "public"."pmo_projects" ADD CONSTRAINT "pmo_projects_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
