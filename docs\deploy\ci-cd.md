# Gitlab CI/CD

documentation guide for setting up a CI/CD pipeline for a GoLang application using GitLab CI/CD

## GitLab CI/CD Configuration Guide for GoLang Application

This guide provides step-by-step instructions on how to configure GitLab CI/CD for a GoLang application using GitLab's CI/CD capabilities.

### Prerequisites
Before you begin, make sure you have the following prerequisites:
- A GoLang application repository: Set up a repository for your GoLang application on GitLab.
- Docker and Kubernetes: Have Docker and Kubernetes set up and configured.
- AWS Account: Have an AWS account set up with the necessary permissions to use AWS services.

### Step 1: Create the `.gitlab-ci.yml` File
1. Create a file named `.gitlab-ci.yml` in the root of your GoLang application repository.
2. Copy the following content into the `.gitlab-ci.yml` file:

```yaml
# Define the base Docker image for the CI/CD pipeline
image: docker:stable

# Define the Docker service to run Docker commands within the pipeline
services:
  - docker:18.09.7-dind

# Define variables used throughout the pipeline
variables:
  DOCKER_HOST: tcp://localhost:2375
  TAG_LATEST: $AWS_ECR_REGISTRY/$PROJECT_NAME:latest-$APP_NAME
  TAG_COMMIT: $AWS_ECR_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHORT_SHA-$APP_NAME
  TAG_MIGRATE_LATEST: $AWS_ECR_REGISTRY/$PROJECT_NAME:latest-$APP_NAME-migrate
  TAG_MIGRATE_COMMIT: $AWS_ECR_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHORT_SHA-$APP_NAME-migrate
  TAG_SEED_LATEST: $AWS_ECR_REGISTRY/$PROJECT_NAME:latest-$APP_NAME-seed
  TAG_SEED_COMMIT: $AWS_ECR_REGISTRY/$PROJECT_NAME:$CI_COMMIT_SHORT_SHA-$APP_NAME-seed

# Define the stages in the pipeline
stages:
  - build
  - deploy

# Job: Build Develop
# This job builds the Docker image for the development environment
Build Develop:
  stage: build
  only:
    - develop
  image:
    name: amazon/aws-cli:latest
    entrypoint: [ "" ]
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
  script:
    # Log in to the Docker registry
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Build the Docker image with the specified tags
    - docker build -f Dockerfile -t $TAG_COMMIT -t $TAG_LATEST .
    # Log in to the AWS ECR registry
    - aws ecr get-login-password | docker login --username AWS --password-stdin $AWS_ECR_REGISTRY
    # Push the Docker image to the registry
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    # Remove the locally built Docker image
    - docker rmi $TAG_COMMIT $TAG_LATEST
  environment:
    name: develop

# Job: Deploy Develop
# This job deploys the application to the development environment
Deploy Develop:
  stage: deploy
  only:
    - develop
  image: jshimko/kube-tools-aws:latest
  script:
    # Update the Kubernetes configuration for the specified cluster
    - aws eks update-kubeconfig --region ap-southeast-1 --name $K8S_CLUSTER_NAME
    # Update the image of the specified deployment in the specified namespace
    - kubectl set image deployment/$APP_NAME $APP_NAME=$TAG_COMMIT -n $K8S_NAMESPACE
  environment:
    name: develop

# Job: Build Production
# This job builds the Docker image for the production environment
Build Production:
  stage: build
  only:
    - main
  image:
    name: amazon/aws-cli:latest
    entrypoint: [ "" ]
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
  script:
    # Log in to the Docker registry
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Build the Docker image with the specified tags
    - docker build -f Dockerfile -t $TAG_COMMIT -t $TAG_LATEST .
    # Log in to the AWS ECR registry
    - aws ecr get-login-password | docker login --username AWS --password-stdin $AWS_ECR_REGISTRY
    # Push the Docker image to the registry
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    # Remove the locally built Docker image
    - docker rmi $TAG_COMMIT $TAG_LATEST
  environment:
    name: production

# Job: Build Migrate Production
# This job builds the Docker image for database migrations in the production environment
Build Migrate Production:
  stage: build
  only:
    - main
  image:
    name: amazon/aws-cli:latest
    entrypoint: [ "" ]
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
  script:
    # Log in to the Docker registry
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Build the Docker image for migrations with the specified tags
    - docker build -f migrate.Dockerfile -t $TAG_MIGRATE_COMMIT -t $TAG_MIGRATE_LATEST .
    # Log in to the AWS ECR registry
    - aws ecr get-login-password | docker login --username AWS --password-stdin $AWS_ECR_REGISTRY
    # Push the Docker image for migrations to the registry
    - docker push $TAG_MIGRATE_COMMIT
    - docker push $TAG_MIGRATE_LATEST
    # Remove the locally built Docker image
    - docker rmi $TAG_MIGRATE_COMMIT $TAG_MIGRATE_LATEST
  environment:
    name: production

```

### Step 2: Configure GitLab CI/CD Variables
To configure the required environment variables for your GitLab CI/CD pipeline, follow these steps:
1. Go to your GitLab repository.
2. Navigate to **Settings > CI/CD > Variables**.
3. Add the following environment variables and their respective values:
  - `AWS_ECR_REGISTRY`: The URL of your AWS ECR registry (e.g., `123456789012.dkr.ecr.us-east-1.amazonaws.com`).
  - `PROJECT_NAME`: The name of your project.
  - `APP_NAME`: The name of your GoLang application.
  - `K8S_CLUSTER_NAME`: The name of your Kubernetes cluster.
  - `K8S_NAMESPACE`: The namespace in which your application will be deployed.
  - `CI_REGISTRY_USER`: Your Docker registry username.
  - `CI_REGISTRY_PASSWORD`: Your Docker registry password.

### Step 3: Commit and Push Changes
1. Commit the `.gitlab-ci.yml` file to your GitLab repository.
2. Push the changes to trigger the CI/CD pipeline.

### Step 4: Verify the CI/CD Pipeline


GitLab CI/CD will automatically detect the `.gitlab-ci.yml` file and start running the pipeline. You can monitor the progress and results of the pipeline by navigating to **CI/CD > Pipelines** in your GitLab repository.

### Step 5: Access the Deployed Application
After the deployment stage is completed, your GoLang application will be deployed to the specified environment (develop or production). Access your application using the appropriate URL or IP address associated with your Kubernetes cluster.

### Additional Notes
- Review the `.gitlab-ci.yml` file and customize it based on your application's requirements.
- Ensure that you have set up the necessary AWS credentials and access for pushing Docker images to your AWS ECR repository.
- Adjust the environment variables and image names in the `.gitlab-ci.yml` file based on your project and application names.
- Customize the documentation to include any specific details or additional instructions relevant to your application and CI/CD requirements.

That's it! You have successfully configured GitLab CI/CD for your GoLang application using the provided code. The pipeline will build, test, and deploy your application automatically with each push to the repository.
