# PMO Project Collaborator API Tests
# Make sure to replace the project_id and user_id with actual values from your database

### Get all collaborators for a project
GET http://localhost:8080/pmo/projects/{{project_id}}/collaborators
Authorization: Bearer {{token}}

### Get collaborators filtered by tab key
GET http://localhost:8080/pmo/projects/{{project_id}}/collaborators?tab_key=CONFIDENTIAL
Authorization: Bearer {{token}}

### Get specific collaborator
GET http://localhost:8080/pmo/projects/{{project_id}}/collaborators/{{collaborator_id}}
Authorization: Bearer {{token}}

### Create new collaborator
POST http://localhost:8080/pmo/projects/{{project_id}}/collaborators
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "user_id": "{{user_id}}",
  "confidential_permission": "READONLY",
  "confidential_main": false,
  "sales_permission": "MODIFY",
  "sales_main": true,
  "presales_permission": "NONE",
  "presales_main": false,
  "bidding_permission": "READONLY",
  "bidding_main": false,
  "pmo_permission": "MODIFY",
  "pmo_main": false
}

### Update collaborator
PUT http://localhost:8080/pmo/projects/{{project_id}}/collaborators/{{collaborator_id}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "confidential_permission": "MODIFY",
  "confidential_main": true,
  "sales_permission": "READONLY",
  "sales_main": false,
  "presales_permission": "MODIFY",
  "presales_main": true,
  "bidding_permission": "NONE",
  "bidding_main": false,
  "pmo_permission": "READONLY",
  "pmo_main": false
}

### Delete collaborator
DELETE http://localhost:8080/pmo/projects/{{project_id}}/collaborators/{{collaborator_id}}
Authorization: Bearer {{token}}
