package core

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"gitlab.finema.co/finema/idin-core/utils"
	"testing"
)

func TestJWTDecode(t *testing.T) {
	token, err := JWTDecode("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MDM3NjQ4MDEsImlhdCI6MTYwMjc2NDgwMSwiaXNzIjoiZGlkOmV4YW1wbGU6ZWJmZWIxZjcxMmViYzZmMWMyNzZlMTJlYzIxIiwianRpIjoiZWM3NDE1NTYtM2Y2ZS00ODkxLWJlNTQtNzRjMjNmZDkzNjA1IiwibmJmIjoxNjAyNzY0ODAxLCJub25jZSI6bnVsbCwic3ViIjpudWxsLCJhdWQiOiJkaWQ6aWRpbjo4ZmNjNzhmZDhhMDQwMTUyZmJkMTI4ZjM1ZjZkMDYzYjI2MDFkMzAzYTZjZDQ1YjAyNDU5Y2VjZmY3NGIzNzFjIiwidmMiOm51bGwsInZwIjp7IkBjb250ZXh0IjpbImh0dHBzOi8vd3d3LnczLm9yZy8yMDE4L2NyZWRlbnRpYWxzL3YxIl0sInR5cGUiOlsiVmVyaWZpYWJsZVByZXNlbnRhdGlvbiIsIkxvZ2luQ3JlZGVudGlhbCJdLCJ2ZXJpZmlhYmxlQ3JlZGVudGlhbCI6WyJleUpoYkdjaU9pSkZVekkxTmlJc0ltdHBaQ0k2SWpJNVpXUm1Nekl4TnpjMk5EY3paakprTldJd1ptSTFOamxsWVRnMU9XUTVOMkZtWmpjeE0yVmhPR1UwTmpnME16ZGhZbVEyTnpKbFlXUTBaRFUzTXpRaUxDSjBlWEFpT2lKS1YxUWlmUS5leUpwYzNNaU9pSmthV1E2YVdScGJqbzRNREUzTVdRME1UUTRNVE0zT1RObE1tWmxPREV6WXpCaU5ETmhaVFZoTlRNM1lXTTBNMkUzTnpBNU5HWTJNRGN3TVdRNE1EbGtZamd3TkdNNU5HUTJJaXdpYW5ScElqb2lOVFEwTlRWaU5qQTNNVGRtTWpSa01qRTNOMkZpTkRNd04yWmtORE5pWm1JM1lUVXhaREpsTkRrelkyVTNZemhpTW1ReVptUXdNV1k0TkRjM1pqQXlPQ0lzSW01aVppSTZNVFl6TURrNE1UZzRPU3dpYm05dVkyVWlPaUkxT0dJMk5HVXpPREJtWTJSaFpXUTNPRGczWW1FM1pUUm1ObUZrWXpVMVlTSXNJbk4xWWlJNkltUnBaRHBwWkdsdU9qZ3haRE5rWVRabFpHUXlNRFkwT0RCbU5qazNPVGRsTnpjMU9EYzBPRFl5TXpGaVlUYzRNalE1WmpCbFpUTmpZbVl4TUdFeU16UTNOVFJpTWpFME5HRWlMQ0oyWXlJNmV5SkFZMjl1ZEdWNGRDSTZXeUpvZEhSd2N6b3ZMM2QzZHk1M015NXZjbWN2TWpBeE9DOWpjbVZrWlc1MGFXRnNjeTkyTVNKZExDSjBlWEJsSWpwYklsWmxjbWxtYVdGaWJHVkRjbVZrWlc1MGFXRnNJaXdpVTNSeWFXNW5YM1I1Y0dVaVhTd2lZM0psWkdWdWRHbGhiRk4xWW1wbFkzUWlPbnNpVTNSeWFXNW5Jam9pTVRNME5UWWlmU3dpWTNKbFpHVnVkR2xoYkZOamFHVnRZU0k2ZXlKcFpDSTZJbWgwZEhCek9pOHZjM05wTFhSbGMzUXVkR1ZrWVM1MGFDOWhjR2t2YzJOb1pXMWhjeTh6WVRFM05UZGhOUzAwT0RFd0xUUTFaVFF0WWpKallpMDJNbUpqT1RNMk9EQTVNR012TVM0d0xqQXZVM1J5YVc1bmRHVnpkQzVxYzI5dUlpd2lkSGx3WlNJNklsTjBjbWx1WjE5MGVYQmxJbjE5ZlEuTUVVQ0lRQ09EL3M0SE9RKzVQMXEzOUI4c1haQVZ3Y1JDSDRFM0kzS0k3QXVETFVDS3dJZ1U0dkVUYVZ6YVpCK1R2OEtqTjR1MG9HdHJnNVFLdjIrM2NVcmdEcEswZ1U9Il19fQ.dnLS6JIp5gvbbPAW_2O1OAXHCIqjbprbHUgy9W0CkAE")
	assert.NoError(t, err)
	assert.NotNil(t, token)

	jwt := JWT[JWTMap]{}
	err = json.Unmarshal(utils.StringToBytes(utils.JSONToString(token)), &jwt)
	assert.NoError(t, err)
	assert.NotNil(t, jwt.Header)
	assert.NotNil(t, jwt.Claims)
	assert.NotNil(t, jwt.Signature)
}
