//go:build e2e

package core

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
	"os"
	"testing"
	"time"
)

type user struct {
	ID        string          `json:"id" gorm:"column:ID;primary_key"`
	Email     string          `json:"email" gorm:"column:EMAIL"`
	FullName  string          `json:"full_name" gorm:"column:FULL_NAME"`
	Articles  []article       `json:"articles" gorm:"foreignKey:UserID"`
	CreatedAt *time.Time      `json:"created_at" gorm:"column:CREATED_AT"`
	UpdatedAt *time.Time      `json:"updated_at" gorm:"column:UPDATED_AT"`
	DeletedAt *gorm.DeletedAt `json:"-" gorm:"column:DELETED_AT"`
}

func (user) TableName() string {
	return "USERS"
}

type article struct {
	ID     string `json:"id" gorm:"column:ID;primary_key"`
	Name   string `json:"name" gorm:"column:NAME"`
	UserID string `json:"user_id" gorm:"column:USER_ID"`

	CreatedAt *time.Time      `json:"created_at" gorm:"column:CREATED_AT"`
	UpdatedAt *time.Time      `json:"updated_at" gorm:"column:UPDATED_AT"`
	DeletedAt *gorm.DeletedAt `json:"-" gorm:"column:DELETED_AT"`
}

func (article) TableName() string {
	return "ARTICLES"
}

type DatabaseSuite struct {
	suite.Suite
	ctx IE2EContext
}

func TestDatabaseSuite(t *testing.T) {
	suite.Run(t, new(DatabaseSuite))
}

func (suite *DatabaseSuite) SetupTest() {

	env := &ENVType{
		config: &ENVConfig{
			DBDriver:   "oracle",
			DBHost:     "oracle-test-db.cma9rmbpabxz.ap-southeast-1.rds.amazonaws.com",
			DBName:     "MYDB",
			DBUser:     "my_user",
			DBPassword: "my_password",
			DBPort:     "1521",
		},
	}

	db, err := NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Oracle: %v", err)
		os.Exit(1)
	}

	suite.ctx = NewE2EContext(&E2EContextOptions{
		ContextOptions: &ContextOptions{
			DB:  db,
			ENV: env,
		},
	})
}

func (s *DatabaseSuite) TestOracleConnect() {
	s.ctx.DB().AutoMigrate(&user{}, &article{})

	//err := s.ctx.DB().Create(&user{
	//	ID:        "long",
	//	Email:     "<EMAIL>",
	//	FullName:  "dsfdsfds",
	//	CreatedAt: utils.GetCurrentDateTime(),
	//}).Error
	//assert.NoError(s.T(), err)

	//err := s.ctx.DB().Create(&article{
	//	ID:        "longarticle1",
	//	UserID:    "long",
	//	Name:      "test",
	//	CreatedAt: utils.GetCurrentDateTime(),
	//}).Error
	//
	//assert.NoError(s.T(), err)
	//
	//err := s.ctx.DB().Model(&user{}).Unscoped().Where("ID = ?", "long").Updates(user{
	//	DeletedAt: nil,
	//	FullName:  "passakon puttasuwan",
	//}).Error
	//
	//assert.NoError(s.T(), err)

	var u user
	err := s.ctx.DB().First(&u).Error

	assert.Nil(s.T(), err)
	assert.NotEmpty(s.T(), u)

	users := make([]user, 0)
	err = s.ctx.DB().Table(user{}.TableName()).Find(&users).Error

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), len(users), 1)

	usersPage := make([]user, 0)
	res, err := Paginate(s.ctx.DB().Preload("Articles"), &usersPage, &PageOptions{
		Q:       "",
		Limit:   30,
		Page:    1,
		OrderBy: nil,
	})

	utils.LogStruct(usersPage)
	assert.Equal(s.T(), len(usersPage), 1)
	assert.Equal(s.T(), res.Total, int64(1))
	assert.NoError(s.T(), err)
	//err = s.ctx.DB().Table(user{}.TableName()).Delete(&users, "ID = ?", "long").Error
	//
	//assert.NoError(s.T(), err)
}
